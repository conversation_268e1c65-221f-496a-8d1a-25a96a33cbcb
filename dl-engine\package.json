{"name": "dl-engine", "description": "Digital Learning Engine - 专注于数字化学习和教育场景的3D/XR应用开发平台", "version": "1.0.0", "homepage": "https://dl-engine.org", "private": true, "bin": {"dl-engine": "npx/cli.js"}, "workspaces": ["packages/*", "packages/projects/projects/**"], "keywords": ["three", "three.js", "ecs", "webgl", "education", "learning", "3d", "xr", "vr", "ar", "digital-learning"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">= 22.11.0"}, "scripts": {"build-client": "cd packages/client && npm run build", "check": "npm run lint && npm run check-errors && npm run check-eslint && npm run test && npm run build-client", "check-errors": "lerna run --scope '@dl-engine/*' check-errors && lerna run --ignore '@dl-engine/*' check-errors", "check-eslint": "eslint --quiet .", "clean-node-modules": "npx rimraf node_modules && npx rimraf package-lock.json && npx lerna exec npx rimraf node_modules && npx lerna exec npx rimraf package-lock.json", "create-project": "ts-node --swc scripts/create-project", "dev": "npm run dev-docker && concurrently -n agones,server,client -c '#CEB793,#FF9800,#7E2D40' npm:dev-agones-silent npm:dev-server npm:dev-client", "dev-noclient": "npm run dev-docker && concurrently -n agones,server,taskserver npm:dev-agones-silent npm:dev-server npm:dev-taskserver", "dev-agones": "cd scripts && ./start-agones.sh", "dev-agones-silent": "npm run dev-agones &> /dev/null", "dev-client": "cd packages/client && npm run dev", "dev-taskserver": "cd packages/taskserver && npm run dev", "dev-docker": "cd scripts && ./start-containers.sh", "dev-server": "cd packages/server && npm run dev", "lint": "eslint --ext .ts,.tsx packages/*/src/**/*", "lint:fix": "eslint --ext .ts,.tsx packages/*/src/**/* --fix", "format": "prettier --write \"packages/*/src/**/*.{ts,tsx}\"", "test": "lerna run test --stream", "test:coverage": "lerna run test:coverage --stream", "build": "lerna run build --stream", "postinstall": "npm run precommit-hooks", "precommit-hooks": "npx husky install", "prepare": "husky install"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.26.0", "@ianvs/prettier-plugin-sort-imports": "4.1.0", "@testing-library/react": "15.0.4", "@types/node": "22.11.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "4.3.1", "@vitest/coverage-istanbul": "2.1.1", "@vitest/ui": "2.1.1", "concurrently": "7.6.0", "eslint": "9.5.0", "globals": "15.6.0", "husky": "^8.0.3", "lerna": "6.5.1", "lint-staged": "13.2.0", "prettier": "3.0.0", "stylelint": "15.10.0", "stylelint-config-standard": "34.0.0", "ts-node": "10.9.2", "typescript": "5.6.3", "vite": "5.4.8", "vitest": "2.1.1"}, "dependencies": {"@hookstate/core": "4.0.1", "@swc/core": "1.7.35", "dotenv": "16.0.3", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^13.5.0", "i18next": "^23.7.6", "three": "0.176.0", "antd": "5.0.0", "tailwindcss": "^3.3.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss}": ["stylelint --fix", "prettier --write"]}}