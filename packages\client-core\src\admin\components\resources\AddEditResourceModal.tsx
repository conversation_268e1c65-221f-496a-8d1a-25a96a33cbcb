/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { ModalState } from '@ir-engine/client-core/src/common/services/ModalState'
import { StaticResourceType, uploadAssetPath } from '@ir-engine/common/src/schema.type.module'
import { cleanURL } from '@ir-engine/common/src/utils/cleanURL'
import { AssetsPreviewPanel } from '@ir-engine/editor/src/components/assets/AssetsPreviewPanel'
import {
  AssetTypeToMimeType,
  ExtensionToAssetType,
  MimeTypeToAssetType
} from '@ir-engine/engine/src/assets/constants/fileTypes'
import { useHookstate } from '@ir-engine/hyperflux'
import { Button, Input, RadioGroup } from '@ir-engine/ui'
import Modal from '@ir-engine/ui/src/primitives/tailwind/Modal'

import { NotificationService } from '../../../common/services/NotificationService'
import { uploadToFeathersService } from '../../../util/upload'

const getNameAndType = async (url: string) => {
  const urlParts = url.split('/')
  const fileName = urlParts[urlParts.length - 1]

  const response = await fetch(url)
  const blob = await response.blob()
  if (!blob.type) {
    const extension = fileName.split('.').pop()
    const assetType = ExtensionToAssetType[extension!]
    const mimeType = AssetTypeToMimeType[assetType]
    return {
      name: fileName,
      mimeType: mimeType,
      assetType: assetType
    }
  }

  return {
    name: fileName,
    mimeType: blob.type,
    assetType: MimeTypeToAssetType[blob.type]
  }
}

const getDefaultErrors = () => ({
  serverError: '',
  name: '',
  resourceFile: '',
  resourceURL: ''
})

export default function CreateResourceModal({ selectedResource }: { selectedResource?: StaticResourceType }) {
  const { t } = useTranslation()

  const modalProcessing = useHookstate(false)
  const errors = useHookstate(getDefaultErrors())

  const state = useHookstate({
    id: selectedResource?.id ? selectedResource.id : '',
    name: selectedResource?.key ? selectedResource.key : '',
    mimeType: selectedResource?.mimeType ? selectedResource.mimeType : '',
    project: selectedResource?.project ? selectedResource.project : '',
    source: 'url' as 'url' | 'file',
    resourceFile: undefined as File | undefined,
    resourceURL: selectedResource?.url ? selectedResource.url : ''
  })

  const previewPanelRef = React.useRef()

  useEffect(() => {
    if (state.source.value === 'file' && state.resourceFile.value) {
      state.name.set(state.resourceFile.value.name)
      if (state.resourceFile.value.type) {
        state.mimeType.set(state.resourceFile.value.type)
      } else {
        const extension = state.resourceFile.value.name.split('.').pop()!
        const assetType = ExtensionToAssetType[extension]
        const mimeType = AssetTypeToMimeType[assetType]
        state.mimeType.set(mimeType)
      }

      ;(previewPanelRef as any).current?.onSelectionChanged({
        name: state.resourceFile.value.name,
        resourceUrl: URL.createObjectURL(state.resourceFile.value) + '#' + state.resourceFile.value.name,
        contentType: MimeTypeToAssetType[state.mimeType.value]
      })
    } else {
      const url = cleanURL(state.resourceURL.value)
      getNameAndType(url).then(({ name, mimeType, assetType }) => {
        state.name.set(name)
        state.mimeType.set(mimeType)
        ;(previewPanelRef as any).current?.onSelectionChanged({
          name: name,
          resourceUrl: url,
          contentType: assetType
        })
      })
    }
  }, [state.source, state.resourceFile, state.resourceURL])

  const handleSubmit = async () => {
    errors.set(getDefaultErrors())

    if (!state.name.value) {
      errors.name.set(t('admin:components.resources.nameCantEmpty'))
    }
    if (state.source.value === 'file' && !state.resourceFile.value) {
      errors.resourceFile.set(t('admin:components.resources.resourceFileCantEmpty'))
    } else if (state.source.value === 'url' && !state.resourceURL.value) {
      errors.resourceURL.set(t('admin:components.resources.resourceUrlCantEmpty'))
    }
    if (Object.values(errors.value).some((value) => value.length > 0)) {
      return
    }

    try {
      if (state.source.value === 'file' && state.resourceFile.value) {
        await uploadToFeathersService(uploadAssetPath, [state.resourceFile.value], {
          type: 'admin-file-upload',
          args: { path: state.name.value, project: state.project.value }
        })
      } else if (state.source.value === 'url' && state.resourceURL.value) {
        const response = await fetch(state.resourceURL.value)
        const url = cleanURL(state.resourceURL.value)
        const blob = await response.blob()
        const resourceFile = new File([blob], url.split('/').pop()!, {
          type: state.mimeType.value
        })
        await uploadToFeathersService(uploadAssetPath, [resourceFile], {
          type: 'admin-file-upload',
          args: { path: state.name.value, project: state.project.value }
        })
      }
      ModalState.closeModal()
    } catch (e) {
      NotificationService.dispatchNotify(e.message, { variant: 'error' })
      errors.serverError.set(e.message)
    }
  }

  return (
    <Modal
      title={t('admin:components.resources.createResource')}
      onClose={ModalState.closeModal}
      className="w-[50vw] max-w-2xl"
      onSubmit={handleSubmit}
    >
      <div className="grid w-full gap-4">
        {errors.serverError.value && <p className="mt-4 text-red-700">{errors.serverError.value}</p>}
        <Input
          value={state.name.value}
          labelProps={{
            text: t('admin:components.resources.resourceName'),
            position: 'top'
          }}
          onChange={(e) => {
            state.name.set(e.target.value)
          }}
          disabled={modalProcessing.value}
        />
        <Input
          value={state.mimeType.value}
          disabled
          labelProps={{
            text: t('admin:components.resources.columns.mimeType'),
            position: 'top'
          }}
        />
        <Input
          value={state.project.value}
          labelProps={{
            text: t('admin:components.resources.columns.project'),
            position: 'top'
          }}
          onChange={(e) => {
            state.project.set(e.target.value)
          }}
          disabled={modalProcessing.value}
        />
        <RadioGroup
          value={state.source.value}
          options={[
            { label: 'URL', value: 'url' },
            { label: 'File', value: 'file' }
          ]}
          horizontal
          onChange={(value) => state.source.set(value)}
          disabled={modalProcessing.value}
        />
        {state.source.value === 'file' && (
          <Button fullWidth className="mb-4" disabled={modalProcessing.value}>
            <label className="block w-full cursor-pointer">
              {t('admin:components.resources.selectFile')}
              <input
                className="mb-4 hidden"
                type="file"
                onChange={(e) => {
                  e.preventDefault()
                  if (e.target.files) {
                    state.resourceFile.set(e.target.files[0])
                  }
                }}
              />
            </label>
          </Button>
        )}

        {state.source.value === 'url' && (
          <Input
            value={state.resourceURL.value}
            onChange={(e) => {
              state.resourceURL.set(e.target.value)
            }}
            labelProps={{
              text: t('admin:components.resources.resourceUrl'),
              position: 'top'
            }}
            disabled={modalProcessing.value}
          />
        )}

        <AssetsPreviewPanel ref={previewPanelRef} />
      </div>
    </Modal>
  )
}
