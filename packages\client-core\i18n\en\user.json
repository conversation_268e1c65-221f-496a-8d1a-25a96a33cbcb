{"common": {"delete": "Delete", "edit": "Edit", "save": "Save", "saving": "Saving", "upload": "Upload", "attach": "Attach", "discardChanges": "Discard Changes", "confirmDiscardChange": "Changes you made will not be saved.", "changesLostAlert": "If you proceed, all your changes will be lost.", "discard": "Discard", "userBannedMessage": "You have been banned from joining this space.", "userBannedMessageDescription": "If you believe this is an error, please contact customer support.", "windowBlocked": "Your browser may have blocked the new tab. Check your settings."}, "auth": {"confirmEmail": {"header": "Confirmation Email", "resendEmail": "Please check your email to verify your account. If you did not receive an email, click {{here}} to resend the verification email.", "haveAnAccount": "Have an account?"}, "login": {"email": "Email | SMS", "username": "Username + Password", "social": "Social"}, "magiclink": {"header": "Login Link", "descriptionEmail": "Please enter your email address, and we will send you a login link via email.", "descriptionSMS": "Please enter your phone number, and we will send you a login link via SMS.", "descriptionEmailSMS": "Please enter your email address or phone number (10 digits, US only), and we will send you a login link via email or SMS.", "descriptionSMSUS": "Please enter your phone number (10 digits, US only), and we will send you a login link via SMS.", "lbl-email": "Email address", "lbl-emailPhone": "Email or Phone number", "lbl-phone": "Phone number", "agree": "I agree to the ", "terms": "Terms & Conditions", "lbl-submit": "Send Login Link", "email-sent-msg": "Check your inbox - we've just sent you a magic link", "success-msg": "A login magic link has been sent. Please check your email or SMS.", "sms-sent-msg": "A login magic link has been sent. Please check your SMS."}, "social": {"apple": "Log in with Apple", "gitHub": "Log in with GitHub", "google": "Log in with Google", "facebook": "Log in with <PERSON><PERSON>", "linkedin": "Log in with LinkedIn", "twitter": "Log in with <PERSON>"}, "register": {"header": "Sign Up", "login": "Log In", "lbl-signup": "Sign Up", "lbl-password": "Password", "lbl-email": "Email Address", "passwordNotMatch": "Passwords do not match.", "ph-password": "Password", "ph-confirmPassword": "Password Confirm", "ph-email": "Email"}, "verifyEmail": {"header": "<PERSON><PERSON><PERSON>", "processing": "Please wait a moment while processing..."}}, "usermenu": {"locationTable": {"title": "Spaces", "loading": "Loading", "col-name": "Name", "col-scene": "Scene", "col-maxuser": "Max Users", "lbl-search": "Search for Spaces", "lbl-new": "New"}, "newLocation": {"title": "Space", "success": "Saved successfully.", "failure": "Something went wrong.", "removeSuccess": "Space removed successfully.", "nameInUse": "This name is already in use.", "nameRequire": "Name is required.", "validNumber": "Only numbers (0-9) are allowed.", "lbl-name": "Name", "lbl-maxuser": "Max Users", "lbl-scene": "Scene", "lbl-type": "Type", "lbl-ve": "Video Enabled", "lbl-gme": "Global Media Enabled", "lbl-update": "Update", "lbl-create": "Create", "tooltipCanNotBeDeleted": "The lobby cannot be deleted.", "confirmation": "Are you sure?", "lbl-yes": "Yes", "lbl-no": "No"}, "socials": {"containerHeading": "Socials", "btn-friend": "Friends", "btn-party-group": "Party / Group", "btn-mediaSession": "Media Session", "btn-share-invite": "Share Link / Invite Code"}, "location": {"containerHeading": "Current Space", "btn-immersive": "Immersive Mode", "btn-respawn": "Respawn", "btn-emote": "<PERSON><PERSON>", "btn-help": "Help", "btn-reportIssue": "Report Issue", "btn-screenRecord": "Screen Record", "btn-adminControls": "Admin Controls"}, "adminControls": {"containerHeading": "Admin Controls", "btn-userList": "User List", "btn-banList": "Ban List", "btn-help": "Help", "btn-requestSystemAdmin": "Request System Admin"}, "mediaSession": {"containerHeading": "Media Session", "btn-audio": "Toggle Audio", "btn-video": "Toggle Video", "btn-faceTracking": "Face Tracking", "btn-screenShare": "Share Screen", "btn-chat": "World Chat"}, "share": {"lbl-share": "Share", "description-share": "Share this Space", "ph-phoneEmail": "Invite by email or phone number", "ph-email": "Invite by email", "ph-phone": "Invite by phone number", "lbl-phoneEmail": "Send to email or phone number", "lbl-email": "Send to email", "lbl-phone": "Send to phone number", "lbl-copy": "Copy", "lbl-send-invite": "Send Invite", "lbl-spectator-mode": "Spectator Mode", "title": "Share this Space", "mobileTitle": "View this Item in AR on your Mobile Device", "linkCopied": "Link copied to clipboard.", "friends": "Friends", "party": "Party", "shareDirect": "Share Direct Link", "shareEmbed": "Embed This Space", "shareTitle": "AR/VR World | Infinite Reality Engine", "shareDescription": "Check out AR/VR world on Infinite Reality Engine", "shareFailed": "An error occurred during sharing. Please try again."}, "party": {"title": "Party", "create": "Create", "createPartyText": "Create a Party for private conversations", "invite": "Invite", "leave": "Leave", "kick": "Kick", "admin": "Admin", "you": "You", "deleteConfirmation": "Are you sure you want to delete this party?"}, "setting": {"chromeAEC": "If you experience echo, enable echo cancellation by going to ", "audio": "Audio", "lbl-master-volume": "Total Volume", "info-master-volume": "Volume setting for all audio (everything but microphone)", "lbl-microphone": "Microphone", "info-microphone": "Volume setting for your microphone, adjusts your voice volume for others", "general": "General", "graphics": "Graphics", "controls": "Controls", "lbl-control-type": "Controller Type", "lbl-left-control-scheme": "Left Control Scheme", "lbl-right-control-scheme": "Right Control Scheme", "lbl-preferred-hand": "Preferred Hand", "lbl-quality": "Quality Preset", "lbl-shadow": "Shadows", "lbl-pp": "Post Processing", "lbl-pbr": "Full PBR", "lbl-automatic": "Automatic", "xrusersetting": "XR", "other-audio-setting": "Other Audio Settings", "use-positional-media": "Spatial user audio and video above the avatar", "user-avatar": "Avatar", "invert-rotation": "Invert Gamepads", "show-avatar": "Show Avatar", "lbl-user-volume": "User Volume", "info-user-volume": "Volume setting for other user's voice", "lbl-notification": "Notification Volume", "info-notification": "Volume setting for notifications (menu sounds, etc.)", "lbl-sound-effect": "Scene Volume", "info-sound-effect": "Volume setting for scene sounds that are not marked as music", "lbl-background-music-volume": "Music Volume", "info-background-music-volume": "Volume setting for music, (this is all audio in the scene marked as music)", "themes": "Themes", "theme": "Theme", "client": "Client", "studio": "Studio", "admin": "Admin", "windowsPerformanceHelp": "Windows OS Performance", "lbl-isVisible": "Show User Nameplate", "lbl-triggerDistance": "Nameplate Trigger Distance"}, "profile": {"lbl-profile": "Profile", "lbl-settings": "Settings", "lbl-username": "Username", "usernameError": "Invalid username", "usernameLengthError": "Username must have less than {{maxCharacters}} characters", "update-msg": "Username updated", "inviteCode": "Invite Code", "createLoginLink": "Create Login Link", "loginLink": "Login Link", "loginLinkCopied": "Login link copied", "youAreAn": "You are an", "youAreA": "You are a", "showUserId": "Show User ID", "hideUserId": "Hide User ID", "showApiKey": "Show API Key", "hideApiKey": "Hide API Key", "apiKey": "API Key", "logout": {"title": "Are you sure you want to log out?", "submit": "Log out", "cancel": "Cancel"}, "connectPhone": "Connect your Phone Number", "connectEmail": "Connect your Email", "connectPhoneEmail": "Connect your Email or Phone Number", "or": "Or", "lbl-wallet": "Login with <PERSON><PERSON>", "ph-email": "Email", "ph-phone": "Phone Number", "ph-phoneEmail": "Phone Number / Email", "phoneEmailError": "Invalid phone number or email address.", "phoneError": "Invalid phone number.", "emailError": "Invalid email address.", "useReadyPlayerMe": "Use ReadyPlayer.Me", "useAvaturn": "Use Avaturn", "loginWithXRWallet": "Login with XR Wallet", "issueVC": "Issue a VC", "requestVC": "Request a VC", "addSocial": "Connect your Social Logins", "logIn": "Log In", "agreeToRequired": "Please agree to all Napster 3D Studio requirements before proceeding:", "agreeTOS": "I agree to the ", "termsOfService": "Infinite Reality Terms of Service", "agreeToMarketing": "I agree to receive marketing communications.", "confirmAge16": "I am 16 years of age or older", "confirmAge18": "I am 18 years of age or older", "removeSocial": "Remove Social Logins", "connections": {"title": "Connections", "ipError": "Cannot remove active Identity Provider.", "disconnect": "disconnect", "connect": "Connect"}, "profileDropdown": {"avatar": "User Avatar Icon", "profile": "Profile", "adminConsole": "<PERSON><PERSON>", "logout": "Logout"}, "userIcon": {"lbl-name": "Your name", "lbl-update": "Update", "userId": "User ID", "ph-uploadImg": "Upload Product Image", "lbl-selectAcvatar": "Select Avatar", "lbl-submit": "Submit"}, "userSettings": {"volume": "Volume", "microphone": "Microphone", "resolution": "Resolution", "high": "High", "low": "Low", "spatialMedia": "Use Spatial Media", "enterMuted": "Enter the Room Muted."}, "delete": {"deleteAccount": "Delete your account", "deleteControlsText": "Are you sure you want to delete your account?", "deleteControlsCancel": "Cancel", "deleteControlsConfirm": "Delete my account", "finalDeleteText": "Deleting your account cannot be undone!", "finalDeleteCancel": "Cancel", "finalDeleteConfirm": "Delete my account"}, "userIdCopied": "User ID copied", "apiKeyCopied": "API Key copied", "refreshApiKey": "Refresh API Key", "privacyPolicy": "Napster 3D Studio Privacy Policy", "creatorPrivacyPolicy": "Creator Privacy Policy", "helpChat": "Help Chat", "reportWorld": "Report Space", "report": "Report a {{type}}", "typeAbuse": "Type of abuse", "selectAbuseTypeRequired": "Please select the type of abuse", "selectOne": "Select One", "reportDetails": "Report Details", "reportDetailsRequired": "Please provide report details.", "reportDetailsPlaceholder": "In your own words, help us understand what went wrong.", "reportFileLabel": "Attachments (Optional)", "reportFileRequired": "Please attach at least one file", "reportSuccessMessage": "Your report has been submitted."}, "oauth": {"authenticating": "Authenticating..."}, "magicLink": {"wait": "Please wait a moment while processing..."}}, "oauth": {"error": "Unfortunately, {{name}} authentication failed.", "redirectToRoot": "Return to Home", "promptForConnection": "Connect to existing account?", "askConnection": "{{email}} is already associated with an account. Would you like to link this {{service}} Single Sign-On to that account?", "acceptConnection": "Yes", "declineConnection": "No (create new account)"}, "magicLink": {"wait": "Please wait a moment while processing...", "promptForConnection": "Connect to existing account?", "askConnection": "{{email}} is already associated with an account. Would you like to link this magiclink login to that account?", "acceptConnection": "Yes", "declineConnection": "No (create new account)"}, "dashboard": {"header": "Dashboard", "dashboard": "Dashboard", "users": "Users", "feeds": "Feeds", "instances": "Instances", "locations": "Spaces", "routes": "Routes", "projects": "Projects", "invites": "<PERSON><PERSON><PERSON>", "sessions": "Sessions", "groups": "Groups", "parties": "Parties", "chats": "Chats", "content": "Content Packs", "scenes": "Scenes", "avatars": "Avatars", "resources": "Resources", "benchmarking": "Benchmarking", "bots": "<PERSON><PERSON>", "setting": "Settings", "server": "Server", "recordings": "Recordings", "channels": "Channels", "crashReport": "Crash Report", "moderation": "Moderation"}, "resource": {"createResource": "Create Resource"}, "avatar": {"titleEditAvatar": "Edit Avatar", "titleSelectAvatar": "Change Your Avatar", "titleUploadAvatar": "Upload Avatar", "titleSelectThumbnail": "Select Thumbnail", "titleCustomizeAvatar": "Customize Your Avatar", "lbl-browse": "Browse", "lbl-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "lbl-upload": "Upload", "selectAvatar": "Select Avatar...", "selectThumbnail": "Select Thumbnail...", "rotate": "Rotate", "pan": "Pan", "noBone": "Object does not contain any bones or skin", "outOfBound": "Object is out of bounds.", "emptyObj": "Object is empty.", "maxTriangleError": "Object contains more than {{allowedTriangles}} faces.", "selectValidFile": "Select a valid 3D avatar file.", "selectValidThumbnail": "Selected a valid thumbnail file.", "fileOversized": "Avatar file size must be between {{minSize}} MB and {{maxSize}} MB", "confirmation": "Are you sure?", "canNotBeRemoved": "Active Avatar cannot be removed", "remove": "Remove avatar", "uploadFiles": "Upload files", "useUrlInstead": "Use URL instead", "cancel": "Cancel", "createAvatar": "Create Avatar", "uploadAvatar": "Upload Avatar", "delete": "Delete", "view": "View", "uploadAvatarInfo": "Update Avatar Information", "name": "Name", "enterName": "Enter name", "avatarInformation": "Avatar Information", "description": "Description", "key": "Key", "none": "None", "submit": "Submit", "edit": "Edit", "confirm": "Confirm", "avatarUrl": "Avatar URL", "upload-success-msg": "Avatar Uploaded Successfully.", "remove-success-msg": "Avatar Uploaded Successfully.", "warning-msg": "Avatar resource is empty, have you synced avatars to your static file storage?", "loadingReadyPlayerMe": "Loading ReadyPlayer.Me", "loadingAvaturn": "Loading Avaturn", "downloading": "Downloading Avatar", "loadingPreview": "Loading Avatar Preview", "uploading": "Uploading Avatar", "searchAvatar": "Search Avatar", "noAvatars": "No avatars to display", "avatar": "Avatar", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "enterAvatarUrl": "Enter Avatar URL", "enterThumbnailUrl": "<PERSON><PERSON>nail URL", "avatarName": "Avatar Name", "done": "Done", "discardAvatarChanges": "Discard Your Avatar?", "InputAvatarName": "Name Your Avatar", "saveAvatar": "Save Avatar", "savingAvatar": "Saving {{avatar}}", "packagingAvatar": "Packaging & adding avatar to your Account...", "avatarPreviewDisabledMessage": "Preview Image not Available"}, "menu": {"settings": "Profile", "sendLocation": "Share", "emote": "<PERSON><PERSON>", "friends": "Friends", "toggleMute": "Microphone", "toggleVideo": "Camera", "cycleCamera": "Cycle Camera", "poseTracking": "Motion Capture", "shareScreen": "Screenshare", "enterVR": "VR", "enterAR": "AR", "exitSpectate": "Exit Spectate", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "toggleChat": "Toggle <PERSON>", "enterFullScreen": "Enter Fullscreen", "exitFullScreen": "Exit Fullscreen", "hide": "<PERSON>de", "show": "Show"}, "personMenu": {"inviteToParty": "Invite to Party", "addAsFriend": "Add as Friend", "cancelRequest": "Cancel Request", "unFriend": "Unfriend", "acceptRequest": "Accept Request", "declineRequest": "Decline Request", "mute": "Mute", "block": "Block", "unblock": "Unblock"}, "person": {"you": "You", "yourScreen": "Your Screen", "muteForEveryone": "Mute for everyone", "unmuteForEveryone": "Unmute for everyone", "muteMe": "Mute me", "unmuteMe": "Unmute me", "muteThisPerson": "Mute this person", "unmuteThisPerson": "Unmute this person", "openPictureInPicture": "Open Picture-in-Picture"}, "friends": {"find": "Find", "friends": "Friends", "messages": "Messages", "blocked": "Blocked", "message": "Message", "profile": "Profile", "call": "Call", "accept": "Accept", "decline": "Decline", "unblock": "Unblock", "pending": "Pending", "requested": "Requested", "noUsers": "No users to display", "requestReceived": "has requested to be your friend", "requestAccepted": "has accepted your request to be friends"}, "messages": {"enterMessage": "Enter Message", "rotateLandscape": "Please rotate to landscape"}, "roomMenu": {"createRoom": "Create a room", "joinRoom": "Join a room", "roomCode": "Room Code", "locationName": "Published Space Name", "joinRoomCode": "Provide the existing room code", "create": "Create", "join": "Join", "locationRequired": "Please provide a Published Space Name.", "roomCodeLength": "Room code must be 6 digits. No zeros are allowed.", "invalidRoomCode": "Unable to find a room with this code. Please enter a valid room code."}, "instanceChat": {"wantToChat": "Want to chat with others?", "register": "Create an account", "verifyAge": "Verify your age"}, "videoWindows": {"more": "More", "reportUser": "Report User", "blockUser": "Block User"}, "moderation": {"abuseReason": {"nudity": "Nudity", "fakeNewsScams": "Fake News & Scams", "disturbingInappropriate": "Disturbing/Inappropriate", "cheatingHacking": "Cheating/Hacking", "bullyingHarassment": "Bullying/Harassment", "illegalActivity": "Illegal Activity", "copyrightInfringements": "Copyright & Other Infringements", "childExploitation": "Child Exploitation", "somethingElse": "Something Else"}}}