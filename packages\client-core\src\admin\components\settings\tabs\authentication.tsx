/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { forwardRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import { useFind, useMutation } from '@ir-engine/common'
import { engineSettingPath, EngineSettingType } from '@ir-engine/common/src/schema.type.module'
import { State, useHookstate } from '@ir-engine/hyperflux'
import { Button, Input } from '@ir-engine/ui'
import PasswordInput from '@ir-engine/ui/src/components/tailwind/PasswordInput'
import Accordion from '@ir-engine/ui/src/primitives/tailwind/Accordion'
import LoadingView from '@ir-engine/ui/src/primitives/tailwind/LoadingView'
import Text from '@ir-engine/ui/src/primitives/tailwind/Text'
import Toggle from '@ir-engine/ui/src/primitives/tailwind/Toggle'

import { getDataType } from '@ir-engine/common/src/utils/dataTypeUtils'
import { flattenObjectToArray, unflattenArrayToObject } from '@ir-engine/common/src/utils/jsonHelperUtils'
import { AuthenticationConfig } from '@ir-engine/server-core/src/appconfig'
import { initialAuthState } from '../../../../common/initialAuthState'

const OAUTH_TYPES = {
  APPLE: 'apple',
  DISCORD: 'discord',
  FACEBOOK: 'facebook',
  GITHUB: 'github',
  GOOGLE: 'google',
  LINKEDIN: 'linkedin',
  TWITTER: 'twitter'
}

const AuthenticationTab = forwardRef(({ open }: { open: boolean }, ref: React.MutableRefObject<HTMLDivElement>) => {
  const { t } = useTranslation()

  const loadingState = useHookstate({
    loading: false,
    errorMessage: ''
  })
  const engineSettingData = useFind(engineSettingPath, {
    query: {
      category: 'authentication',
      paginate: false
    }
  })
  const authSetting = unflattenArrayToObject(
    engineSettingData.data.map((el) => ({ key: el.key, value: el.value, dataType: el.dataType }))
  ) as AuthenticationConfig
  const state = useHookstate(initialAuthState)
  const holdAuth = useHookstate(initialAuthState)
  const keySecret = useHookstate({
    apple: authSetting?.oauth?.apple,
    discord: authSetting?.oauth?.discord,
    github: authSetting?.oauth?.github,
    google: authSetting?.oauth?.google,
    twitter: authSetting?.oauth?.twitter,
    linkedin: authSetting?.oauth?.linkedin,
    facebook: authSetting?.oauth?.facebook
  })
  const authSettingMutation = useMutation(engineSettingPath)

  useEffect(() => {
    if (engineSettingData.status === 'success') {
      const tempAuthState = { ...initialAuthState }
      authSetting?.authStrategies?.forEach((el) => {
        Object.entries(el).forEach(([strategyName, strategy]) => {
          tempAuthState[strategyName] = strategy
        })
      })
      state.set(tempAuthState)
      holdAuth.set(tempAuthState)

      const tempKeySecret = JSON.parse(
        JSON.stringify({
          apple: authSetting?.oauth?.apple,
          discord: authSetting?.oauth?.discord,
          github: authSetting?.oauth?.github,
          google: authSetting?.oauth?.google,
          twitter: authSetting?.oauth?.twitter,
          linkedin: authSetting?.oauth?.linkedin,
          facebook: authSetting?.oauth?.facebook
        })
      )
      keySecret.set(tempKeySecret)
    }
  }, [engineSettingData.status])

  const handleSubmit = () => {
    loadingState.loading.set(true)
    // Create a map of all strategies with their current values
    const currentStrategiesMap = Object.keys(state.value).reduce(
      (acc, item) => {
        acc[item] = state[item].value
        return acc
      },
      {} as Record<string, boolean>
    )

    // Preserve order by using authStrategiesInDb as reference
    const updatedAuthStrategies = authSetting.authStrategies.map((strategy) => {
      const [key] = Object.keys(strategy)
      return { [key]: currentStrategiesMap[key] ?? false }
    })

    // Add any new strategies that weren't in authStrategiesInDb
    Object.keys(currentStrategiesMap).forEach((key) => {
      if (!updatedAuthStrategies.some((strategy) => Object.keys(strategy)[0] === key)) {
        updatedAuthStrategies.push({ [key]: currentStrategiesMap[key] })
      }
    })

    const oauth = { ...authSetting.oauth, ...(keySecret.value as any) }

    const updatedSettings = flattenObjectToArray({ oauth: oauth, authStrategies: updatedAuthStrategies })

    const authOperationPromises: Promise<EngineSettingType | EngineSettingType[]>[] = []

    updatedSettings.forEach((setting) => {
      const settingInDb = engineSettingData.data.find((el) => el.key === setting.key)
      if (!settingInDb) {
        authOperationPromises.push(
          authSettingMutation.create({
            key: setting.key,
            category: 'authentication',
            dataType: getDataType(setting.value),
            value: `${setting.value}`,
            type: 'private'
          })
        )
      } else if (settingInDb.value != setting.value) {
        authOperationPromises.push(
          authSettingMutation.patch(settingInDb.id, {
            key: setting.key,
            category: 'authentication',
            dataType: getDataType(setting.value),
            value: setting.value,
            type: 'private'
          })
        )
      }
    })

    Promise.all(authOperationPromises)
      .then(() => {
        loadingState.set({ loading: false, errorMessage: '' })
      })
      .catch((e) => {
        loadingState.set({ loading: false, errorMessage: e.message })
      })
  }

  const handleCancel = () => {
    const temp = { ...initialAuthState }
    authSetting?.authStrategies?.forEach((el) => {
      Object.entries(el).forEach(([strategyName, strategy]) => {
        temp[strategyName] = strategy
      })
    })

    const tempKeySecret = JSON.parse(
      JSON.stringify({
        apple: authSetting?.oauth?.apple,
        discord: authSetting?.oauth?.discord,
        github: authSetting?.oauth?.github,
        google: authSetting?.oauth?.google,
        twitter: authSetting?.oauth?.twitter,
        linkedin: authSetting?.oauth?.linkedin,
        facebook: authSetting?.oauth?.facebook
      })
    )
    keySecret.set(tempKeySecret)
    state.set(temp)
  }

  const handleOnChangeAppId = (event, type) => {
    keySecret.set({
      ...JSON.parse(JSON.stringify(keySecret.value)),
      [type]: {
        ...JSON.parse(JSON.stringify(keySecret[type].value)),
        appId: event.target.value
      }
    })
  }

  const handleOnChangeKey = (event, type) => {
    keySecret.set({
      ...JSON.parse(JSON.stringify(keySecret.value)),
      [type]: {
        ...JSON.parse(JSON.stringify(keySecret[type].value ?? {})),
        key: event.target.value
      }
    })
  }

  const handleOnChangeSecret = (event, type) => {
    keySecret.set({
      ...JSON.parse(JSON.stringify(keySecret.value)),
      [type]: {
        ...JSON.parse(JSON.stringify(keySecret[type].value ?? {})),
        secret: event.target.value
      }
    })
  }

  const onSwitchHandle = (toggleState: State<boolean>, value: boolean) => {
    toggleState.set(value)
  }

  return (
    <Accordion
      title={t('admin:components.setting.authentication.header')}
      subtitle={t('admin:components.setting.authentication.subtitle')}
      ref={ref}
      open={open}
    >
      <div className="mt-6 grid grid-cols-2 gap-4">
        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.service'),
            position: 'top'
          }}
          value={authSetting?.service || ''}
          disabled
        />

        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.entity'),
            position: 'top'
          }}
          value={authSetting?.entity || ''}
          disabled
        />

        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.jwtAlgorithm'),
            position: 'top'
          }}
          value={authSetting?.jwtAlgorithm || ''}
          disabled
        />

        <PasswordInput
          fullWidth
          labelProps={{
            text: t('admin:components.setting.secret'),
            position: 'top'
          }}
          value={authSetting?.secret || ''}
          disabled
        />

        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.jwtPublicKey'),
            position: 'top'
          }}
          value={authSetting?.jwtPublicKey || ''}
          disabled
        />
      </div>

      <Text component="h3" fontSize="xl" fontWeight="semibold" className="mb-4 mt-6 w-full">
        {t('admin:components.setting.authStrategies')}
      </Text>

      <div className="grid grid-cols-6 gap-x-6 gap-y-4">
        {Object.keys(state.value).map((strategyName, i) => {
          const displayStrategyName =
            strategyName === 'twitter' ? 'x' : strategyName === 'facebook' ? 'meta' : strategyName
          return (
            <Toggle
              key={i}
              label={displayStrategyName}
              value={state[strategyName].value}
              disabled={strategyName === 'jwt'}
              onChange={(value) => onSwitchHandle(state[strategyName], value)}
            />
          )
        })}
      </div>

      <Text component="h3" fontSize="xl" fontWeight="semibold" className="my-4 w-full">
        {t('admin:components.setting.oauth')}
      </Text>

      <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
        {t('admin:components.setting.defaults')}
      </Text>

      <div className="grid grid-cols-3 gap-4">
        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.host'),
            position: 'top'
          }}
          value={authSetting?.oauth?.defaults?.host || ''}
          disabled
        />

        <Input
          fullWidth
          labelProps={{
            text: t('admin:components.setting.protocol'),
            position: 'top'
          }}
          value={authSetting?.oauth?.defaults?.protocol || ''}
          disabled
        />
      </div>

      <hr className="my-6 border " />
      <div className="grid grid-cols-3 gap-4">
        {holdAuth?.apple?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.apple')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.apple?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.APPLE)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.apple?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.APPLE)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.apple || ''}
              disabled
            />
          </div>
        )}
        {holdAuth?.discord?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.discord')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.discord?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.DISCORD)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.discord?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.DISCORD)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.discord || ''}
              disabled
            />
          </div>
        )}

        {holdAuth?.linkedin?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.linkedIn')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.linkedin?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.LINKEDIN)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.linkedin?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.LINKEDIN)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.linkedin || ''}
              disabled
            />
          </div>
        )}

        {holdAuth?.facebook?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.facebook')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.facebook?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.FACEBOOK)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.facebook?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.FACEBOOK)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.facebook || ''}
              disabled
            />
          </div>
        )}

        {holdAuth?.google?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.google')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.google?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.GOOGLE)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.google?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.GOOGLE)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.google || ''}
              disabled
            />
          </div>
        )}

        {holdAuth?.twitter?.value && (
          <div className="col-span-1 grid gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.twitter')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.twitter?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.TWITTER)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.twitter?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.TWITTER)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.twitter || ''}
              disabled
            />
          </div>
        )}

        {holdAuth?.github?.value && (
          <div className="col-span-1 gap-y-2">
            <Text component="h4" fontSize="base" fontWeight="medium" className="my-4 w-full">
              {t('admin:components.setting.github')}
            </Text>

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.githubAppId'),
                position: 'top'
              }}
              value={keySecret?.value?.github?.appId || ''}
              onChange={(e) => handleOnChangeAppId(e, OAUTH_TYPES.GITHUB)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.key'),
                position: 'top'
              }}
              value={keySecret?.value?.github?.key || ''}
              onChange={(e) => handleOnChangeKey(e, OAUTH_TYPES.GITHUB)}
            />

            <PasswordInput
              fullWidth
              labelProps={{
                text: t('admin:components.setting.secret'),
                position: 'top'
              }}
              value={keySecret?.value?.github?.secret || ''}
              onChange={(e) => handleOnChangeSecret(e, OAUTH_TYPES.GITHUB)}
            />

            <Input
              fullWidth
              labelProps={{
                text: t('admin:components.setting.callback'),
                position: 'top'
              }}
              value={authSetting?.callback?.github || ''}
              disabled
            />
          </div>
        )}
      </div>

      <div className="mt-6 grid grid-cols-8 gap-6">
        <Button size="sm" className="text-primary col-span-1 " onClick={handleCancel} fullWidth>
          {t('admin:components.common.reset')}
        </Button>

        <Button size="sm" className="col-span-1" variant="primary" onClick={handleSubmit} fullWidth>
          {t('admin:components.common.save')}
          {loadingState.loading.value && <LoadingView spinnerOnly className="h-6 w-6" />}
        </Button>
      </div>
    </Accordion>
  )
})

export default AuthenticationTab
