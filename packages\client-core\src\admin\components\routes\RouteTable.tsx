/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'

import { useFind, useMutation } from '@ir-engine/common'
import { InstalledRoutesInterface } from '@ir-engine/common/src/interfaces/Route'
import { routePath } from '@ir-engine/common/src/schema.type.module'
import Toggle from '@ir-engine/ui/src/primitives/tailwind/Toggle'

import { routeColumns, RouteRowType } from '../../common/constants/route'
import DataTable from '../../common/Table'

export default function RoutesTable() {
  const installedRouteData = useFind('routes-installed').data
  const routesQuery = useFind(routePath, {
    query: { $limit: 20 }
  })
  const routeActivateCreate = useMutation('route-activate').create

  const isRouteActive = (project: string, route: string) =>
    routesQuery.data.findIndex((a) => {
      return a.project === project && a.route === route
    }) !== -1

  const activateCallback = (project: string, route: string, checked: boolean) => {
    routeActivateCreate({ project, route, activate: checked }).then(() => routesQuery.refetch())
  }

  const createRows = (rows: readonly InstalledRoutesInterface[]): RouteRowType[] =>
    rows
      .map((row) => {
        if (!row.routes?.length) return []
        return row.routes.map((route) => ({
          id: row.project + route,
          project: row.project,
          route: route,
          action: (
            <Toggle
              value={isRouteActive(row.project, route)}
              onChange={(checked) => activateCallback(row.project, route, checked)}
            />
          )
        }))
      })
      .flat()

  return <DataTable size="xl" query={routesQuery} columns={routeColumns} rows={createRows(installedRouteData)} />
}
