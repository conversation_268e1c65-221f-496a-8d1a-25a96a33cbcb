{
    "version": "0.2.0",
    "compounds": [
        {
            "name": "Dev Mode",
            "configurations": [
                "dev-agones",
                "dev-api-server",
                "dev-instanceserver",
                "dev-instanceserver-dev-channel",
                "dev-client",
                "dev-taskserver",
                "Chrome",
            ]
            // npm:dev-agones-silent npm:dev-server npm:dev-client npm:dev-taskserver
        },
        {
            "name": "Dev UI",
            "configurations": [
                "ui-jest",
                "ui-storybook"
            ]
            // npm:dev-agones-silent npm:dev-server npm:dev-client npm:dev-taskserver
        }
    ],
    "configurations": [
        {
            "command": "cd packages/ui && npm run test:watch",
            "name": "ui-jest",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/ui && npm run storybook",
            "name": "ui-storybook",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Chrome",
            "url": "https://localhost:3000",
            "runtimeArgs": [
                "--ignore-certificate-errors",
                "--unsafely-treat-insecure-origin-as-secure=https://localhost:3000"
            ],
            "webRoot": "${workspaceRoot}/packages/client/src"
        },
        {
            "command": "cd packages/server && npm run dev-api-server",
            "name": "dev-api-server",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/instanceserver && npm run dev",
            "name": "dev-instanceserver",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/instanceserver && npm run dev-channel",
            "name": "dev-instanceserver-dev-channel",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run dev-agones-silent",
            "name": "dev-agones",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/server && npm run dev",
            "name": "dev-server",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/client && npm run dev",
            "name": "dev-client",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/taskserver && npm run dev",
            "name": "dev-taskserver",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run test",
            "name": "npm run test",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/editor && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - editor",
            "request": "launch",
            "type": "node-terminal"
        },
        {
            "command": "cd packages/spatial && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - spatial",
            "request": "launch",
            "type": "node-terminal"
        },
        {
            "command": "cd packages/server-core && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - server-core",
            "request": "launch",
            "type": "node-terminal"
        },
        {
            "command": "cd packages/spatial && npm run test -- --reporter=basic ${file}",
            "name": "vitest run - spatial : Current File",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/ecs && npm run test -- --reporter=basic ${file}",
            "name": "vitest run - ecs : Current File",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/engine && npm run test -- --reporter=basic ${file}",
            "name": "vitest run - engine : Current File",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/ecs && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - ecs",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/engine && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - engine",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "cd packages/common && npm run test -- --maxConcurrency 1 --no-file-parallelism --bail 1 --reporter=basic",
            "name": "npm run test - common",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run dev",
            "name": "npm run dev",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run dev-tabs",
            "name": "npm run dev-tabs",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run prepare-database",
            "name": "npm run prepare-database",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run dev-taskserver",
            "name": "npm run dev-taskserver",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run dev-reinit",
            "name": "npm run dev-reinit",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run migrate",
            "name": "npm run migrate",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "command": "npm run check-errors",
            "name": "npm run check-errors",
            "request": "launch",
            "type": "node-terminal",
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Dev",
            "cwd": "${workspaceFolder}",
            "runtimeArgs": [
                "run-script",
                "dev"
            ],
            "runtimeExecutable": "npm",
            "skipFiles": [
                "<node_internals>/**"
            ],
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Server",
            "cwd": "${workspaceFolder}/packages/server",
            "runtimeArgs": [
                "run-script",
                "dev"
            ],
            "runtimeExecutable": "npm",
            "skipFiles": [
                "<node_internals>/**"
            ],
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Instanceserver",
            "cwd": "${workspaceFolder}/packages/instanceserver",
            "runtimeArgs": [
                "run-script",
                "dev"
            ],
            "runtimeExecutable": "npm",
            "skipFiles": [
                "<node_internals>/**"
            ],
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Engine Tests",
            "cwd": "${workspaceFolder}/packages/engine",
            "program": "${workspaceFolder}/node_modules/mocha/bin/_mocha",
            "args": [
                "--config",
                "${workspaceFolder}/packages/engine/.mocharc.js",
            ],
            "internalConsoleOptions": "openOnSessionStart",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Server Core Tests",
            "cwd": "${workspaceFolder}/packages/server-core",
            "program": "${workspaceFolder}/node_modules/mocha/bin/_mocha",
            "args": [
                "--config",
                "${workspaceFolder}/packages/server-core/.mocharc.js",
            ],
            "internalConsoleOptions": "openOnSessionStart",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug e2e Tests",
            "program": "${workspaceFolder}/node_modules/mocha/bin/_mocha",
            "args": [
                "--config",
                "${workspaceFolder}/.mocharc.js",
            ],
            "internalConsoleOptions": "openOnSessionStart",
            "skipFiles": [
                "<node_internals>/**"
            ]
        }
    ]
}
