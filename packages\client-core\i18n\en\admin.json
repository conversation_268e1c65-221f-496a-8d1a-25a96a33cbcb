{"components": {"location": {"columns": {"name": "Name", "sceneId": "Scene", "maxUsersPerInstance": "Max Users Per Instance", "locationType": "Type", "tags": "Tags", "videoEnabled": "Video Enabled", "action": "Action"}, "lbl-name": "Title", "lbl-maxuser": "Max Users", "lbl-scene": "Scene", "lbl-ve": "Video Enabled", "lbl-gme": "Global Media Enabled", "lbl-lobby": "Make Lobby", "lbl-featured": "Featured", "lbl-ae": "Audio Enabled", "lbl-se": "Screen Sharing Enabled", "lbl-fe": "Face Streaming Enabled", "lbl-je": "Jump Control Enabled", "lbl-vre": "VR Functionality Enabled", "createLocation": "Create Space", "updateLocation": "Update Space", "removeLocation": "Remove Space", "type": "Type", "selectScene": "Select a Scene", "sceneCantEmpty": "Scene cannot be empty", "maxUserCantEmpty": "Max users cannot be empty", "maxUserExceeded": "The number of users entered exceeds the allowed maximum. Please enter a number between 1 and {{maxUsers}}.", "nameCantEmpty": "Name cannot be empty", "typeCantEmpty": "Type cannot be empty", "nameRequired": "Name is required", "maxUsersRequired": "Max users is required", "sceneRequired": "Scene is required", "typeRequired": "Type is required", "confirmLocationDelete": "Are you sure you want to delete this Space?", "lobby": "Lobby", "featured": "Featured", "locations": "Spaces"}, "bot": {"uniqueCommand": "Command must be unique", "commandRequired": "Command is required", "nameCantEmpty": "Name cannot be empty", "descriptionCantEmpty": "Description cannot be empty", "locationCantEmpty": "Space cannot be empty", "createNewBot": "Create new bot", "addMoreBots": "Add more bots to the system.", "location": "Space", "instance": "Instance", "addCommand": "Add command", "addMoreCommand": "Add additional commands", "command": "Command", "enterCommand": "Enter a command here", "description": "Description", "enterDescription": "Enter a description here", "spawnBot": "Spawn Bot", "lastRunStatus": "Last run status", "autoRefreshing": "Auto-refreshing in 10 seconds", "engineBots": "Napster Engine bots", "updateBot": "UPDATE BOT", "name": "Name", "confirmBotDelete": "Are you sure you want to delete this bot?"}, "common": {"cancel": "Cancel", "close": "Close", "confirmation": "Confirmation", "edit": "Edit", "submit": "Submit", "save": "Save", "confirm": "Confirm", "none": "None", "update": "Update", "remove": "Remove", "reset": "Reset", "delete": "Delete", "view": "View", "kick": "Kick", "ban": "Ban", "yes": "Yes", "no": "No", "all": "All", "refresh": "Refresh", "name": "Name", "lastUpdatedBy": "Last updated by User ID: {{userId}} on {{updatedAt}}", "fillRequiredFields": "Please complete all required fields", "fixErrorFields": "Please review and correct all errors before proceeding", "logOut": "Log Out", "newestFirst": "Newest First", "oldestFirst": "Oldest First"}, "analytics": {"loading": "Loading analytics...", "activeParties": "Active Parties", "activeLocations": "Active Spaces", "activeInstances": "Active Instances", "activeScenes": "Active Scenes", "instanceUsers": "Instance Users", "channelUsers": "Channel Users", "dailyUsers": "Daily Users", "dailyNewUsers": "Daily New Users", "usersToday": "Users Today", "newUsersToday": "New Users Today", "activity": "Activity", "users": "Users"}, "crashReport": {"title": "Crash Report", "loading": "Loading crash report..."}, "group": {"createGroup": "Create Group", "name": "Name", "description": "Description", "nameCantEmpty": "Name cannot be empty", "descriptionCantEmpty": "Description cannot be empty", "scopeTypeCantEmpty": "Scope cannot be empty", "nameRequired": "Name is required", "descriptionRequired": "Description is required", "scopeTypeRequired": "Scope is required", "grantScope": "<PERSON>", "groupUsers": "Group Users", "confirmGroupDelete": "Are you sure you want to delete this group?", "selectAllScopes": "Select All Scopes", "clearAllScopes": "Clear All Scopes"}, "invite": {"columns": {"id": "Id", "name": "Name", "passcode": "Passcode", "type": "Type", "targetObjectId": "Target ID", "spawnType": "Spawn Type", "spawnDetails": "Spawn Details", "action": "Actions"}, "invites": "<PERSON><PERSON><PERSON>", "sendInvite": "Send Invite", "receivedInvite": "Received Invite", "confirmInviteDelete": "Are you sure you want to delete this invite?", "confirmMultiInviteDelete": "Are you sure you want to delete all selected invites?", "create": "Create Invite", "recipients": "Recipients", "type": "Type", "singleTargetLabel": "Invite recipient", "targetLabel": "Invite recipients", "target": "Enter a comma-separated list of invite codes and/or emails/SMSes", "singleTarget": "Enter an invite code or email/SMS", "friend": "User invite code", "group": "Group ID", "party": "Party ID", "submit": "Send Invite(s)", "update": "Update Invite", "remove": "Remove Invite", "removeInvites": "Remove Invites", "location": "Space", "instance": "Instance", "user": "User", "new-user": "User", "spawnPoint": "Spawn Point", "userPosition": "User Position", "timedInvite": "Timed", "oneTime": "One Time Use", "makeAdmin": "Make Admin", "selectLocation": "Select Space", "selectInstance": "Select Instance", "spawnAtPosition": "Spawn at Position", "selectSpawnPoint": "Select Spawn Point", "selectUserPosition": "Select User Position", "deleteSelected": "Delete selected invites", "startTime": "Start Time", "endTime": "End Time", "errors": {"recipients": "Please enter the email addresses of the recipients", "inviteLocation": "Please select a Space", "inviteInstance": "Please select an Instance", "spawnPoint": "Please select a Spawn Point", "userPosition": "Please select a user position", "startTime": "Please select the start date and time", "endTime": "Please select the end date and time"}}, "channel": {"channels": "Channels", "columns": {"id": "Id", "name": "Name", "action": "Action"}, "instanceRequired": "Instance is required", "instanceCantEmpty": "Instance cannot be empty", "instance": "Instance", "createOne": "Create One", "createChannel": "Create Channel", "update": "Update Channel", "confirmChannelDelete": "Are you sure you want to delete this Channel?", "confirmMultiChannelDelete": "Are you sure you want to delete all selected Channels?", "name": "Channel Name", "remove": "Remove Channel", "removeChannels": "Remove Channels"}, "project": {"columns": {"name": "Name", "projectVersion": "Version", "enabled": "Enabled", "visibility": "Visibility", "commitSHA": "Commit SHA", "commitDate": "Commit Date", "actions": "Actions"}, "actions": {"push": "<PERSON><PERSON>", "repo": "Repo", "access": "Access", "invalidateCache": "Invalidate Cache", "update": "Update", "history": "History"}, "projectHistory": "Project History", "addProject": "Add Project", "updateProject": "Update Project", "downloadProject": "Download Project", "rebuild": "Rebuild", "rebuilding": "Rebuilding", "updateEngine": "Update Engine", "updateSelector": "Update projects", "currentEngineVersion": "Current Engine Version: {{version}}", "currentEngineCommit": "Current Engine Commit: {{commit}}", "projectWarning": "WARNING: Updating projects along with the Engine version may result in loss of work. If you revert to an old engine Project and update projects with it, your copy of these projects will also be reverted. It is recommended that you back up the destination branch of these projects if you have made modifications that might be lost.", "updateAndRebuild": "Update Engine/Rebuild", "checking": "Checking Rebuild", "project": "Project", "githubUrl": "GitHub URL", "urlRequired": "URL is required", "urlCantEmpty": "URL cannot be empty", "updateGithubRepoLink": "Update GitHub Repo Link", "setGithubRepoLink": "Set GitHub Repo Link", "githubRepoUrl": "GitHub Repo URL", "publicUrl": "Public URL", "processing": "Processing", "confirmProjectsRebuild": "Rebuild these Projects?", "confirmProjectRebuild": "Rebuild this Project?", "confirmProjectResetMain1": "Are you sure you want to reset this Project?", "confirmProjectResetMain2": "to the main branch in the linked GitHub repository? Your branch, ", "confirmProjectResetMain3": "will be forcibly overridden by the contents of the main branch.", "confirmProjectInvalidate": "Are you sure you want to invalidate storage provider's cache of", "confirmProjectDelete": "Are you sure you want to delete this Project?", "confirmPushProjectToGithub": "Push this Project to its associated GitHub repository?", "outdatedBuild": "This Project has been updated locally since the last build", "hasLocalChanges": "This Project has local changes, and will not auto-update from GitHub if it's enabled in order to prevent accidental data loss", "userAccess": "User Access", "userInviteCode": "User Invite Code", "inviteCodeCantEmpty": "Invite code cannot be empty", "inviteCodeRequired": "Invite code is required", "source": "Source Repository", "sourceType": "Source Type", "destination": "Destination Repository", "destinationType": "Destination Type", "destinationProcessing": "Checking that you have access to Destination repository", "sourceVsDestinationProcessing": "Checking that Source and Destination are the same Project, or Destination is empty", "files": "Files", "branchData": "Source branch", "commitData": "Commit", "branchProcessing": "Fetching branches of Project", "commitsProcessing": "Fetching commits of Project", "mismatchedProjectWarning": "This version of this Project does not match the installed version of Napster Engine. There may be compilation or runtime errors if this Project is installed.", "needsGithubProvider": "You must connect your account with GitHub in order to create new Projects, or add or update Projects from GitHub", "sourceProjectName": "Project in Source repository", "destinationProjectName": "Project in Destination repository", "destinationRepoEmpty": "The Destination repository is empty", "destinationURLValid": "Is Destination URL valid and accessible?", "sourceURLValid": "Is Source URL valid and accessible?", "sourceMatchesDestination": "Does Source Project match Destination, or is Destination empty?", "changeDestination": "Change Destination Repository", "copyDestination": "Copy Destination", "autoUpdate": "Auto Update", "enableAutoUpdate": "Enable Auto Update", "autoUpdateMode": "Auto Update Mode", "dev": "<PERSON>", "devTooltip": "In dev mode, the Project is updated whenever a new commit is pushed to Source branch of Source repository", "prod": "Prod", "prodTooltip": "In prod mode, the Project is updated whenever a tag release is pushed in Source repository", "autoUpdateInterval": "Auto Update Interval", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "commitSHACopied": "Commit SHA copied", "buildStatus": "Build Status", "defaultProjectUpdateTooltip": "default-project is updated when engine is updated", "refreshGithubRepoAccess": "Refresh GitHub Repository Access", "refreshingGithubRepoAccess": "Refreshing"}, "buildStatus": {"columns": {"id": "Build #", "status": "Status", "commitSHA": "Commit SHA", "logs": "Logs", "dateStarted": "Date Started", "dateEnded": "Date Ended"}, "buildStatus": "Build Status", "running": "Running", "viewLogs": "View Logs"}, "instance": {"columns": {"id": "Instance ID", "ipAddress": "IP Address", "currentUsers": "Current Users", "isActive": "Is Active", "locationName": "Space", "channelId": "Channel", "podName": "Pod Name", "action": "Action"}, "actions": {"view": "View", "delete": "Delete"}, "active": "Active", "ended": "Ended", "instances": "Instance", "confirmInstanceDelete": "Are you sure you want to delete this Instance?", "location": "Space", "count": "Count", "kick": "Kick", "ban": "Ban", "unban": "<PERSON><PERSON>", "banned": "Banned until {{duration}}", "kickDuration": "Kick Duration", "confirmUserBan": "Are you sure you want to ban this user?", "noInstanceUsers": "No users in this instance"}, "resources": {"columns": {"id": "Id", "key": "Key", "name": "Name", "mimeType": "Mime Type", "project": "Project", "action": "Action"}, "confirmation": "Confirmation", "resources": "Resources", "resourceType": "Resource Type", "preview": "Preview", "createResource": "Create Resource", "resourceName": "Resource Name", "confirmResourceDelete": "Are you sure you want to delete this Resource?", "nameCantEmpty": "Name cannot be empty", "resourceTypeCantEmpty": "Resource Type cannot be empty", "resourceUrlCantEmpty": "Resource URL cannot be empty", "resourceFileCantEmpty": "Resource File cannot be empty", "nameRequired": "Name is required", "resourceTypeRequired": "Resource type is required", "resourceUrlInvalid": "Resource URL is not valid", "resourceFileOversized": "The resource file size must be between {{minSize}} MB and {{maxSize}} MB", "selectFile": "Select File", "resourceUrl": "Resource URL"}, "setting": {"authentication": {"header": "Authentication", "subtitle": "Edit Authentication Settings"}, "service": "Service", "githubAppId": "App ID (Enter for GitHub App, omit for OAuth App)", "secret": "Secret", "jwtAlgorithm": "JWT Algorithm", "jwtPublicKey": "JWT Public Key", "entity": "Entity", "authStrategies": "Authentication Strategies", "userName": "User Name", "local": "Local", "oauth": "OAuth", "password": "Password", "defaults": "De<PERSON>ults", "host": "Host", "protocol": "Protocol", "apple": "Apple", "discord": "Discord", "facebook": "Meta", "google": "Google", "github": "<PERSON><PERSON><PERSON>", "linkedIn": "LinkedIn", "twitter": "X", "key": "Key", "callback": "Callback", "appTitle": "App Title", "appSubtitle": "App Subtitle", "appDescription": "App Description", "appBackground": "App Background", "appSocialLinks": "Social Links", "icon": "Icon", "link": "Link", "resetTheme": "Reset to De<PERSON>ult", "aws": {"header": "AWS", "subtitle": "Edit AWS Settings"}, "features": {"header": "Features", "subtitle": "Edit Feature Flag Settings"}, "keys": "Keys", "accessKeyId": "Access Key ID", "secretAccessKey": "Secret Access Key", "eks": "EKS", "hostedZoneId": "Hosted Zone ID", "s3": "S3", "endpoint": "Endpoint", "staticResourceBucket": "Static Resource Bucket", "region": "Region", "avatarDir": "AvatarDir", "s3DevMode": "S3DevMode", "domain": "Domain", "distributionId": "Distribution ID", "cloudFront": "Cloud Front", "sms": "SMS", "applicationId": "Application ID", "senderId": "Sender ID", "apiKey": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "chargebee": "Chargebee", "client": {"header": "Client", "subtitle": "Edit Client Settings", "main": "Main", "logo": "Logo", "other": "Other", "media": "Audio & Video"}, "clientId": "OAuth2 client_id", "clientSecret": "OAuth2 client_secret", "coil": "Coil", "coilPaymentPointer": "Payment Pointer", "title": "Title", "description": "Description", "logo": "Logo", "appleTouchIcon": "Apple Touch Icon", "icon192px": "Icon 192px", "icon512px": "Icon 512px", "favIcon16px": "FavIcon 16px", "favIcon32px": "FavIcon 32px", "webmanifestLink": "Webmanifest", "swScriptLink": "service-worker.js", "releaseName": "Release Name", "releaseVersion": "Release", "email": {"header": "Email", "subtitle": "Edit Email <PERSON>"}, "smtp": "SMTP", "port": "Port", "secure": "Secure", "auth": "<PERSON><PERSON>", "from": "From", "for": "For", "to": "To", "subject": "Subject", "login": "<PERSON><PERSON>", "friend": "Friend", "group": "group", "party": "Party", "channel": "Channel", "smsNameCharLimit": "SMS Name Character Limit", "instanceServer": {"header": "Instance Server", "subtitle": "Edit Instance Server Settings"}, "clientHost": "Client Host", "rtcStartPort": "RTC Start Port", "rtcEndPort": "RTC End Port", "rtcPortBlockSize": "RTC Block Size", "identifierDigits": "Identifier Digits", "mode": "Mode", "locationName": "Published Space Name", "maxUsersPerInstance": "Max Users Per Instance", "settings": "Settings", "redis": {"header": "Redis", "subtitle": "<PERSON> <PERSON><PERSON>"}, "enabled": "Enabled", "address": "Address", "hostName": "Host Name", "rootDirectory": "Root Directory", "publicDirectory": "Public Directory", "nodeModulesDirectory": "Node Modules Directory", "localStorageProvider": "Local Storage Provider", "performDryRun": "Perform Dry Run", "storageProvider": "Storage Provider", "googleAnalyticsMeasurementId": "Google Analytics Measurement ID", "googleTagManagerContainerId": "Google Tag Manager Container ID", "googleTagManagerAuth": "Google Tag Manager <PERSON><PERSON>", "googleTagManagerAuthDescription": "Auth token for testing, see custom environments.", "googleTagManagerPreview": "Google Tag Manager Preview", "googleTagManagerPreviewDescription": "Environment to test, see custom environments.", "certPath": "<PERSON>rt<PERSON><PERSON>", "keyPath": "Key<PERSON><PERSON>", "githubWebhookSecret": "<PERSON><PERSON><PERSON>", "githubPrivateKey": "Github Private Key", "instanceserverUnreachableTimeoutSeconds": "Seconds for instanceserver to be considered unreachable", "ipGeolocationApiUrl": "IP Geolocation API URL", "ipGeolocationApiToken": "IP Geolocation API Token", "patchInstanceserver": "Patch Instanceserver", "hub": "<PERSON><PERSON>", "placeholderText": "This is the input placeholder", "addSocialLink": "Add Social Link", "themePlayground": "Theme Playground", "defaultThemes": "Default Themes", "theme": "Theme", "editor": "Editor", "studio": "Studio", "admin": "Admin", "shortTitle": "PWA Short Title", "startPath": "PWA Start Path", "shortTitleTooltip": "The displayed title of a PWA-installed copy of this app if 'title' is too long; recommended max 12 characters", "startPathTooltip": "The path that a PWA-installed copy of this app will direct to", "authSettingsRefreshNotification": "Updating authentication settings requires restarting the API pods. This should be complete within a few minutes. Changes you just made may not be reflected until then.", "homepageLinkButtonEnabled": "Show link button on homepage", "homepageLinkButtonRedirect": "URL of homepage link button", "homepageLinkButtonText": "Text of homepage link button", "audioMaxBitrate": "User Audio max bitrate (kbps)", "videoCodec": "User video codec", "videoMaxResolution": "User webcam max resolution", "videoFHD": "1920x1080", "videoHD": "1280x720", "videoFWVGA": "854x480", "videoNHD": "640x360", "videoLowResMaxBitrate": "User low-res video max bitrate (kbps)", "videoMidResMaxBitrate": "User mid-res video max bitrate (kbps)", "videoHighResMaxBitrate": "User high-res video max bitrate (kbps)", "screenshareCodec": "Screenshare video codec", "screenshareLowResMaxBitrate": "Screenshare low-res video max bitrate (kbps)", "screenshareMidResMaxBitrate": "Screenshare mid-res video max bitrate (kbps)", "screenshareHighResMaxBitrate": "Screenshare high-res video max bitrate (kbps)", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms Of Service", "assistanceLink": "Assistance", "webRTCSettings": {"main": "WebRTC Settings", "useCustomICEServers": "Use custom iceServers configuration", "iceServers": "iceServers (STUN/TURN configuration)", "iceServer": "iceServer #", "iceURL": "ICE URL #", "username": "Username", "credential": "Password", "useFixedCredentials": "Use fixed username/password to authenticate STUN/TURN server", "useTimeLimitedCredentials": "Use time-limited credentials to authenticate STUN/TURN server", "webRTCStaticAuthSecretKey": "Shared static-auth-secret for authenticating STUN/TURN server", "usePrivateInstanceserverIP": "Return instanceservers' raw private IP address instead of DNS record"}, "project": {"header": "Project", "subtitle": "Edit Project Settings", "submit": "Submit", "clear": "Clear", "save": "Save", "delete": "Delete", "add": "Add", "showAssetOnly": "Show Asset Only Projects", "noSettingsMessage": "No settings available", "duplicateKey": "Duplicate keys cannot exist", "keyName": "Key Name", "value": "Value"}, "server": {"header": "Server", "subtitle": "Edit Mode, Storage Provider, Host Name, Hub,etc."}, "helm": {"header": "Helm Chart Versions", "subtitle": "To apply a new Helm chart version, Save here, then go to /admin/projects and Rebuild", "main": "Main deployment Helm version", "builder": "Builder deployment Helm version", "explainer": "To apply a new Helm chart version, Save here, then go to /admin/projects and Rebuild", "builderHelmToDeploy": "Builder Helm chart to deploy", "mainHelmToDeploy": "Main Helm chart to deploy"}, "taskServer": {"taskServer": "Task Server", "subtitle": "Edit Task Server Settings", "port": "Port", "processInterval": "Process Interval"}, "plugins": "Plugins", "metabase": {"header": "Metabase", "subtitle": "<PERSON> <PERSON><PERSON>", "siteUrl": "Site URL", "secretKey": "Secret Key", "environment": "Environment", "expiration": "Expiration", "crashDashboardId": "Crash Dashboard Id"}, "zendesk": {"header": "Zendesk", "subtitle": "Edit Zendesk Settings"}, "keyName": "key Name", "kid": "Key Id"}, "avatar": {"columns": {"id": "ID", "name": "Name", "user": "Owner", "isPublic": "Public", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "action": "Action"}, "avatars": "Avatars", "source": "Source", "file": "File", "url": "URL", "uploadAvatar": "Upload Avatar Model", "avatarUrlPreview": "Enter Avatar URL to preview", "add": "Add Avatar", "update": "Update Avatar", "view": "View Avatar", "remove": "Remove Avatar", "avatarPreview": "Avatar Preview", "thumbnailPreview": "Thumbnail Preview", "avatarUrl": "Avatar URL", "thumbnailUrl": "Thumbnail URL", "selectAvatar": "Select Avatar", "clearAvatar": "Clear Avatar", "selectThumbnail": "Select Thumbnail", "clearThumbnail": "<PERSON> Thumbnail", "saveThumbnail": "Generate Thumbnail", "uploadThumbnail": "Upload Thumbnail", "thumbnailURLPreview": "Enter thumbnail URL to preview", "saveThumbnailTooltip": "Generate thumbnail from Avatar preview", "nameCantEmpty": "Name cannot be empty", "avatarUrlCantEmpty": "Avatar URL cannot be empty", "thumbnailUrlCantEmpty": "Thumbnail URL cannot be empty", "avatarFileCantEmpty": "Avatar file cannot be empty", "thumbnailFileCantEmpty": "Thumbnail file cannot be empty", "nameRequired": "Name is required", "avatarUrlInvalid": "Avatar URL is not valid", "thumbnailUrlInvalid": "Thumbnail URL is not valid", "avatarFileOversized": "Avatar file size must be between {{minSize}} MB and {{maxSize}} MB", "thumbnailFileOversized": "Thumbnail file size must be between {{minSize}} MB and {{maxSize}} MB", "zoom": "Zoom", "leftClick": "Left Click", "rightClick": "Right Click", "scroll": "<PERSON><PERSON>", "loading": "Loading", "confirmAvatarDelete": "Are you sure you want to delete this Avatar?", "confirmMultiDelete": "Are you sure you want to delete all selected Avatars?", "deleteSelected": "Delete selected Avatars", "confirmThumbnailReplace": "Replace the previous Avatar thumbnail with the current Avatar preview?"}, "user": {"columns": {"id": "Id", "name": "Name", "avatar": "Avatar", "accountIdentifier": "Linked Accounts", "lastLogin": "Last Login", "ageVerified": "Age Verified (13 or above)", "isGuest": "Is Guest", "isDeactivated": "Is Deactivated", "createdAt": "Created", "action": "Action"}, "users": "Users", "addUser": "Add User", "updateUser": "Update User", "nameCantEmpty": "Name cannot be empty", "avatarCantEmpty": "Avatar cannot be empty", "scopeTypeCantEmpty": "Scope type cannot be empty", "createUser": "Create User", "nameRequired": "Name is required", "namePlaceholder": "Enter name of the user", "avatarRequired": "Avatar is required", "scopeTypeRequired": "Scope type is required", "hideGuests": "Hide guests", "id": "Id", "name": "Name", "avatar": "Avatar", "grantScope": "<PERSON>", "location": "Space", "inviteCode": "Invite Code", "instance": "Instance", "remove": "Remove User", "removeUsers": "Remove Users", "confirmUserDelete": "Are you sure you want to delete this user?", "confirmMultiUserDelete": "Are you sure you want to delete all these users?", "isGuest": "Is Guest", "isDeactivated": "Is Deactivated", "selectAllScopes": "Select All Scopes", "clearAllScopes": "Clear All Scopes", "userSearch": "User by id, name or account identifier", "linkedAccounts": "Linked Accounts", "apple": "Apple", "discord": "Discord", "facebook": "Meta", "google": "Google", "github": "<PERSON><PERSON><PERSON>", "linkedIn": "LinkedIn", "twitter": "X", "sms": "SMS", "email": "Email", "selectAvatar": "Select an Avatar", "selectScopes": "Select Scopes"}, "server": {"servers": "Servers", "migrations": "Migrations", "apiJobs": "API Jobs", "loading": "Fetching Server Info", "loadingLogs": "Fetching Server Logs", "name": "Name", "status": "Status", "serverStatus": {"succeeded": "Succeeded", "running": "Running", "pending": "Pending", "failed": "Failed", "Unknown": "Unknown"}, "type": "Type", "users": "Users", "instance": "Instance", "restarts": "Restarts", "containers": "Containers", "container": "Container", "age": "Time", "logs": "Logs", "serverLogs": "Server Logs", "actions": "Actions", "autoRefresh": "Auto Refresh", "none": "None", "seconds": "seconds", "minute": "minute", "minutes": "minutes", "download": "Download", "downloadLogs": "Download Logs", "removePod": "Remove Pod", "confirmPodDelete": "Are you sure you want to delete this Pod?", "viewLogs": "View Logs", "columns": {"migrationsInfo": {"id": "ID", "name": "Name", "batch": "<PERSON><PERSON>", "migration_time": "Migration Time"}, "apiJobs": {"id": "ID", "name": "Name", "status": "Status", "start_time": "Start Time", "end_time": "End Time", "return_data": "Return Data"}}}, "recording": {"columns": {"id": "Recording ID", "user": "User", "ended": "Ended", "schema": "<PERSON><PERSON><PERSON>", "preview": "Preview", "action": "Action"}, "recording": "Recordings", "confirmRecordingDelete": "Are you sure you want to delete this recording?", "recordingFiles": "Recordings Files for:"}, "route": {"routes": "Routes", "columns": {"project": "Project", "route": "Route", "action": "Active"}}, "history": {"publishedLocation": "published the Space", "modifiedLocation": "modified the Space", "unpublishedLocation": "unpublished the Space", "fromScene": "from the scene", "added": "added the", "created": "created the", "removed": "removed the", "renamed": "renamed a", "modified": "modified the", "accessTo": "access to", "scene": "Scene", "resource": "Resource", "updatedTags": "updated the tags for", "createdThumbnail": "created a thumbnail: ", "updatedThumbnail": "updated the thumbnail of the resource: ", "removedThumbnail": "removed the thumbnail for the resource: ", "updatePermission": "updated the permission of the user", "createdProject": "created Project: "}, "moderation": {"columns": {"id": "Report ID", "type": "Type", "username": "Username being reported", "reason": "Reason", "status": "Status", "dateReported": "Date Reported", "action": "Action"}, "header": "Moderation", "reportResolved": "Report resolved", "errorResolvingReport": "Error resolving report", "userBanned": "User Banned", "userUnBanned": "User Unbanned", "type": "Type", "accountType": "Account Type", "owner": "Owner", "user": "User", "usernameBeingReported": "Username being reported", "userId": "User Id", "reporter": "Reporter", "reason": "Reason", "status": "Status", "dateReported": "Date Reported", "details": "Details", "reportCsvFilename": "report.csv", "reportDetails": "Report Details", "reasonForAbuse": "Reason for abuse", "space": "Space", "ipAddress": "Reporting User IP", "reportedUserIp": "Reported User IP", "uploadedFiles": "Uploaded files", "exportReport": "Export report", "resolveIssue": "Resolve Issue", "banUser": "Ban User", "action": "Action", "loginHistory": "Login History", "loginHistoryDescription": "Login history for IP address: {{ipAddress}} (last 30 days)", "viewLoginHistory": "View Login History", "loginTime": "Login Time", "userAgent": "User Agent", "noLoginHistory": "No login history found for this IP address", "manageAccess": "Manage who has access to your worlds.", "bannedUsers": "Banned Users", "statusFilter": "Status Filter", "viewDetails": "View Details", "addUsersToBan": "Add users to ban", "addUsers": "Add Users", "addUser": "Add User", "username": "Username", "usernamePlaceholder": "Enter username", "uidPlaceholder": "User ID", "unBan": "<PERSON><PERSON>", "ban": "Ban", "cancel": "Cancel", "loading": "Loading", "id": "Report ID", "bannedSelectUserError": "Select a user to ban", "bannedUserReasonError": "Select a reason for banning the user", "userAlreadyBanned": "This user is already banned", "userBannedSelectUserHelperText": "Select a user to ban", "userBannedSelectReasonHelperText": "Select a reason for banning the user", "all": "All", "open": "Open", "resolved": "Resolved", "moderationban": {"columns": {"username": "Username", "userId": "User Id", "reason": "Reason", "space": "Space", "status": "Status", "ipAddress": "IP Address", "dateReported": "Date Reported", "action": "Action"}}}}}