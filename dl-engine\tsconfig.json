{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "useDefineForClassFields": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@dl-engine/*": ["packages/*/src"], "@dl-engine/common": ["packages/common/src"], "@dl-engine/ecs": ["packages/ecs/src"], "@dl-engine/hyperflux": ["packages/hyperflux/src"], "@dl-engine/spatial": ["packages/spatial/src"], "@dl-engine/engine": ["packages/engine/src"], "@dl-engine/client": ["packages/client/src"], "@dl-engine/editor": ["packages/editor/src"], "@dl-engine/server": ["packages/server/src"], "@dl-engine/ui": ["packages/ui/src"], "@dl-engine/xrui": ["packages/xrui/src"], "@dl-engine/visual-script": ["packages/visual-script/src"]}}, "include": ["packages/*/src/**/*", "packages/*/tests/**/*", "scripts/**/*", "*.ts", "*.tsx"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "references": [{"path": "./packages/common"}, {"path": "./packages/ecs"}, {"path": "./packages/hyperflux"}, {"path": "./packages/spatial"}, {"path": "./packages/engine"}, {"path": "./packages/client"}, {"path": "./packages/editor"}, {"path": "./packages/server"}, {"path": "./packages/ui"}, {"path": "./packages/xrui"}, {"path": "./packages/visual-script"}]}