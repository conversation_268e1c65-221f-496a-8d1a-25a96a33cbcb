{"extends": ["stylelint-config-standard"], "plugins": [], "rules": {"indentation": 2, "string-quotes": "single", "no-duplicate-selectors": true, "color-hex-case": "lower", "color-hex-length": "short", "color-named": "never", "selector-combinator-space-after": "always", "selector-attribute-quotes": "always", "selector-attribute-operator-space-before": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-brackets-space-inside": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "property-no-vendor-prefix": true, "value-no-vendor-prefix": true, "number-leading-zero": "always", "function-url-quotes": "always", "font-weight-notation": "numeric", "comment-empty-line-before": "always", "at-rule-no-vendor-prefix": true, "rule-empty-line-before": "always-multi-line", "selector-pseudo-element-colon-notation": "double", "selector-pseudo-class-parentheses-space-inside": "never", "media-feature-range-operator-space-before": "always", "media-feature-range-operator-space-after": "always", "media-feature-parentheses-space-inside": "never", "media-feature-colon-space-before": "never", "media-feature-colon-space-after": "always"}, "ignoreFiles": ["node_modules/**/*", "dist/**/*", "build/**/*", "coverage/**/*", "public/**/*"]}