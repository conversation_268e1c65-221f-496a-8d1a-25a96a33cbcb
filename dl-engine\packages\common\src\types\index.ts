/*
Digital Learning Engine
Copyright © 2025 Digital Learning Engine Team. All Rights Reserved.
*/

// 基础类型定义
export type OpaqueType<K, T = string> = T & { __TYPE__: K }

// 用户相关类型
export type UserID = OpaqueType<'UserID'>
export type UserName = OpaqueType<'UserName'>
export type PhoneNumber = OpaqueType<'PhoneNumber'>
export type CountryCode = OpaqueType<'CountryCode'>
export type VerificationCode = OpaqueType<'VerificationCode'>

// 网络相关类型
export type NetworkID = OpaqueType<'NetworkID'>
export type PeerID = OpaqueType<'PeerID'>
export type RoomID = OpaqueType<'RoomID'>
export type SessionID = OpaqueType<'SessionID'>

// 实体相关类型
export type EntityID = OpaqueType<'EntityID'>
export type EntityUUID = OpaqueType<'EntityUUID'>
export type ComponentID = OpaqueType<'ComponentID'>
export type SystemID = OpaqueType<'SystemID'>

// 资产相关类型
export type AssetID = OpaqueType<'AssetID'>
export type AssetURL = OpaqueType<'AssetURL'>
export type FileHash = OpaqueType<'FileHash'>

// 项目相关类型
export type ProjectID = OpaqueType<'ProjectID'>
export type SceneID = OpaqueType<'SceneID'>
export type LessonID = OpaqueType<'LessonID'>

// 用户接口
export interface User {
  id: UserID
  name: UserName
  phone: PhoneNumber
  countryCode: CountryCode
  nickname?: string
  avatarUrl?: string
  isGuest: boolean
  ageVerified: boolean
  createdAt: string
  updatedAt: string
  lastLogin?: string
  isDeactivated?: boolean
  deactivatedAt?: string
}

// 手机号登录接口
export interface PhoneLoginRequest {
  phone: PhoneNumber
  countryCode: CountryCode
  verificationCode: VerificationCode
}

export interface SendCodeRequest {
  phone: PhoneNumber
  countryCode: CountryCode
}

export interface PhoneLoginResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

// 网络状态接口
export interface NetworkPeer {
  id: PeerID
  userId: UserID
  connected: boolean
  latency: number
  lastSeen: string
}

export interface Room {
  id: RoomID
  name: string
  description?: string
  maxParticipants: number
  currentParticipants: number
  isPrivate: boolean
  createdBy: UserID
  createdAt: string
  updatedAt: string
}

// 3D 相关类型
export interface Vector3 {
  x: number
  y: number
  z: number
}

export interface Quaternion {
  x: number
  y: number
  z: number
  w: number
}

export interface Transform {
  position: Vector3
  rotation: Quaternion
  scale: Vector3
}

// 资产相关接口
export interface Asset {
  id: AssetID
  name: string
  type: AssetType
  url: AssetURL
  size: number
  hash: FileHash
  metadata?: Record<string, any>
  createdBy: UserID
  createdAt: string
  updatedAt: string
}

export enum AssetType {
  IMAGE = 'image',
  MODEL = 'model',
  AUDIO = 'audio',
  VIDEO = 'video',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  SCENE = 'scene'
}

// 项目相关接口
export interface Project {
  id: ProjectID
  name: string
  description?: string
  thumbnail?: string
  isPublic: boolean
  createdBy: UserID
  collaborators: UserID[]
  createdAt: string
  updatedAt: string
}

export interface Scene {
  id: SceneID
  projectId: ProjectID
  name: string
  description?: string
  data: SceneData
  createdAt: string
  updatedAt: string
}

export interface SceneData {
  entities: EntityData[]
  environment: EnvironmentData
  physics: PhysicsData
}

export interface EntityData {
  id: EntityUUID
  name: string
  components: ComponentData[]
  children: EntityUUID[]
  parent?: EntityUUID
}

export interface ComponentData {
  type: string
  data: Record<string, any>
}

export interface EnvironmentData {
  skybox?: string
  lighting: LightingData
  fog?: FogData
}

export interface LightingData {
  ambient: Vector3
  directional?: {
    direction: Vector3
    color: Vector3
    intensity: number
  }
}

export interface FogData {
  color: Vector3
  near: number
  far: number
  density?: number
}

export interface PhysicsData {
  gravity: Vector3
  timestep: number
  enabled: boolean
}

// 教育相关接口
export interface Lesson {
  id: LessonID
  title: string
  description?: string
  sceneId: SceneID
  duration: number
  maxStudents: number
  instructor: UserID
  students: UserID[]
  status: LessonStatus
  scheduledAt?: string
  startedAt?: string
  endedAt?: string
  createdAt: string
  updatedAt: string
}

export enum LessonStatus {
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: ApiError
  message?: string
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
}

// 分页类型
export interface Paginated<T> {
  data: T[]
  total: number
  limit: number
  skip: number
}

// 查询参数类型
export interface QueryParams {
  $limit?: number
  $skip?: number
  $sort?: Record<string, 1 | -1>
  $select?: string[]
  [key: string]: any
}

// WebRTC 相关类型
export interface MediaStreamConfig {
  audio: boolean
  video: boolean
  screen?: boolean
}

export interface PeerConnection {
  id: PeerID
  connection: RTCPeerConnection
  dataChannel?: RTCDataChannel
  mediaStream?: MediaStream
  state: RTCPeerConnectionState
}

// 事件类型
export interface EventData<T = any> {
  type: string
  data: T
  timestamp: number
  source: PeerID
}

// 国际化类型
export interface I18nResource {
  [key: string]: string | I18nResource
}

export interface I18nOptions {
  lng?: string
  fallbackLng?: string
  resources: Record<string, { translation: I18nResource }>
}

// 配置类型
export interface AppConfig {
  name: string
  version: string
  environment: 'development' | 'staging' | 'production'
  api: {
    baseUrl: string
    timeout: number
  }
  database: {
    mysql: DatabaseConfig
    redis: DatabaseConfig
    postgres: DatabaseConfig
  }
  storage: {
    provider: 'minio' | 's3'
    endpoint: string
    bucket: string
  }
  ai: {
    provider: 'ollama'
    endpoint: string
    model: string
  }
}

export interface DatabaseConfig {
  host: string
  port: number
  database: string
  username: string
  password: string
}
