{"semi": false, "trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "importOrder": ["^react$", "^react-dom$", "^@?\\w", "^@dl-engine/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "plugins": ["@ianvs/prettier-plugin-sort-imports"]}