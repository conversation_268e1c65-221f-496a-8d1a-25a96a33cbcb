version: '3.8'

services:
  # MySQL 主数据库
  mysql:
    image: mariadb:10.7
    container_name: dl-engine-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: dl_engine
      MYSQL_USER: dl_user
      MYSQL_PASSWORD: dl_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/sql:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: dl-engine-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network

  # PostgreSQL 向量数据库
  postgres:
    image: pgvector/pgvector:pg15
    container_name: dl-engine-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: dl_engine_vector
      POSTGRES_USER: dl_user
      POSTGRES_PASSWORD: dl_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/sql/postgres:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network

  # Minio 对象存储
  minio:
    image: minio/minio:latest
    container_name: dl-engine-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: dl_engine
      MINIO_ROOT_PASSWORD: dl_engine_password
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - dl-engine-network

  # Ollama AI 服务
  ollama:
    image: ollama/ollama:latest
    container_name: dl-engine-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - dl-engine-network

  # 开发环境文件服务器
  file-server:
    image: nginx:alpine
    container_name: dl-engine-file-server
    restart: unless-stopped
    ports:
      - "8642:80"
    volumes:
      - ./public:/usr/share/nginx/html
      - ./scripts/nginx/nginx.conf:/etc/nginx/nginx.conf
    networks:
      - dl-engine-network

volumes:
  mysql_data:
  redis_data:
  postgres_data:
  minio_data:
  ollama_data:

networks:
  dl-engine-network:
    driver: bridge
