{"version": 3, "file": "xatlas.js", "mappings": ";qBACA,IACMA,EADFC,GACED,EAAiC,oBAAbE,UAA4BA,SAASC,cAAgBD,SAASC,cAAcC,SAAMC,EAEnG,SACAJ,GAGT,IAA6EK,EAAoBC,EAA7FC,OAAmC,KAFrCP,EAAqBA,GAAsB,CAAC,GAEKA,EAAmB,CAAC,EAA6CO,EAAc,MAAE,IAAIC,SAAQ,SAASC,EAAQC,GAAQL,EAAoBI,EAAQH,EAAmBI,CAAM,IAAOC,OAAOC,yBAAyBL,EAAc,MAAE,WAAUI,OAAOE,eAAeN,EAAc,MAAE,QAAQ,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,qKAAqK,IAAIL,OAAOE,eAAeN,EAAc,MAAE,QAAQ,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,qKAAqK,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,gBAAeI,OAAOE,eAAeN,EAAc,MAAE,aAAa,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,0KAA0K,IAAIL,OAAOE,eAAeN,EAAc,MAAE,aAAa,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,0KAA0K,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,mBAAkBI,OAAOE,eAAeN,EAAc,MAAE,gBAAgB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,6KAA6K,IAAIL,OAAOE,eAAeN,EAAc,MAAE,gBAAgB,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,6KAA6K,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,iBAAgBI,OAAOE,eAAeN,EAAc,MAAE,cAAc,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,2KAA2K,IAAIL,OAAOE,eAAeN,EAAc,MAAE,cAAc,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,2KAA2K,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,iBAAgBI,OAAOE,eAAeN,EAAc,MAAE,cAAc,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,2KAA2K,IAAIL,OAAOE,eAAeN,EAAc,MAAE,cAAc,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,2KAA2K,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,wBAAuBI,OAAOE,eAAeN,EAAc,MAAE,qBAAqB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,kLAAkL,IAAIL,OAAOE,eAAeN,EAAc,MAAE,qBAAqB,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,kLAAkL,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,aAAYI,OAAOE,eAAeN,EAAc,MAAE,UAAU,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,uKAAuK,IAAIL,OAAOE,eAAeN,EAAc,MAAE,UAAU,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,uKAAuK,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,uBAAsBI,OAAOE,eAAeN,EAAc,MAAE,oBAAoB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,iLAAiL,IAAIL,OAAOE,eAAeN,EAAc,MAAE,oBAAoB,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,iLAAiL,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,aAAYI,OAAOE,eAAeN,EAAc,MAAE,UAAU,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,uKAAuK,IAAIL,OAAOE,eAAeN,EAAc,MAAE,UAAU,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,uKAAuK,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,WAAUI,OAAOE,eAAeN,EAAc,MAAE,QAAQ,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,qKAAqK,IAAIL,OAAOE,eAAeN,EAAc,MAAE,QAAQ,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,qKAAqK,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,eAAcI,OAAOE,eAAeN,EAAc,MAAE,YAAY,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,yKAAyK,IAAIL,OAAOE,eAAeN,EAAc,MAAE,YAAY,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,yKAAyK,KAAQL,OAAOC,yBAAyBL,EAAc,MAAE,0BAAyBI,OAAOE,eAAeN,EAAc,MAAE,uBAAuB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,oLAAoL,IAAIL,OAAOE,eAAeN,EAAc,MAAE,uBAAuB,CAACO,cAAa,EAAKG,IAAI,WAAWD,GAAM,oLAAoL,KAAI,IAA2BE,EAAvBC,EAAgB,CAAC,EAAU,IAAID,KAAOX,EAAWA,EAAOa,eAAeF,KAAMC,EAAgBD,GAAKX,EAAOW,IAAM,IAAyGG,EAA6BC,EAAlIC,EAAW,GAAsgB,GAAtTF,EAAmC,iBAATG,OAAkBF,EAA6C,mBAAhBG,cAAgE,iBAAVC,SAA8C,iBAAnBA,QAAQC,UAA4BD,QAAQC,SAASC,KAA0GrB,EAAoB,YAAG,MAAM,IAAIsB,MAAM,sKAAsK,IAA4KC,EAAxKC,EAAgB,GAAkL,IAAGV,IAAoBC,EAAiwC,MAAM,IAAIO,MAAM,+BAA17B,GAA7TP,EAAuBS,EAAgBC,KAAKC,SAASC,KAAajC,SAASC,gBAAe6B,EAAgB9B,SAASC,cAAcC,KAAOJ,IAAYgC,EAAgBhC,GAAoDgC,EAAH,IAAnCA,EAAgBI,QAAQ,SAA8BJ,EAAgBK,OAAO,EAAEL,EAAgBM,YAAY,KAAK,GAAwB,GAAwB,iBAATb,QAA0C,mBAAhBC,cAA4B,MAAM,IAAII,MAAM,0LAA0TP,IAAuBQ,EAAW,SAAoBQ,GAAK,IAAIC,EAAI,IAAIC,eAAuF,OAAxED,EAAIE,KAAK,MAAMH,GAAI,GAAOC,EAAIG,aAAa,cAAcH,EAAII,KAAK,MAAa,IAAIC,WAAWL,EAAIM,SAAS,GAA4Y,IAAswLC,EAAq4BC,EAAeC,EAAtpNC,EAAI1C,EAAc,OAAG2C,QAAQC,IAAIC,KAAKF,SAAaG,EAAI9C,EAAiB,UAAG2C,QAAQI,KAAKF,KAAKF,SAAS,IAAIhC,KAAOC,EAAoBA,EAAgBC,eAAeF,KAAMX,EAAOW,GAAKC,EAAgBD,IAAgiH,SAASqC,EAASC,GAAUD,EAASE,QAAMF,EAASE,MAAM,CAAC,GAAMF,EAASE,MAAMD,KAAOD,EAASE,MAAMD,GAAM,EAAEH,EAAIG,GAAM,CAAlpHrC,EAAgB,KAAQZ,EAAkB,YAAEgB,EAAWhB,EAAkB,WAAMI,OAAOC,yBAAyBL,EAAO,cAAaI,OAAOE,eAAeN,EAAO,YAAY,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,0LAA0L,IAAOT,EAAoB,aAAcA,EAAoB,YAAMI,OAAOC,yBAAyBL,EAAO,gBAAeI,OAAOE,eAAeN,EAAO,cAAc,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,6LAA6L,IAAOT,EAAa,MAAQA,EAAa,KAAMI,OAAOC,yBAAyBL,EAAO,SAAQI,OAAOE,eAAeN,EAAO,OAAO,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,gLAAgL,IAAI0C,OAAqD,IAAvCnD,EAAmC,2BAAgB,uFAAuFmD,OAA+C,IAAjCnD,EAA6B,qBAAgB,iFAAiFmD,OAAiD,IAAnCnD,EAA+B,uBAAgB,mFAAmFmD,OAA+C,IAAjCnD,EAA6B,qBAAgB,iFAAiFmD,OAA+B,IAAjBnD,EAAa,KAAgB,uDAAuDmD,OAAoC,IAAtBnD,EAAkB,UAAgB,gEAAgEmD,OAAqC,IAAvBnD,EAAmB,WAAgB,kEAAkEmD,OAAyC,IAA3BnD,EAAuB,eAAgB,0EAA0EmD,OAAuC,IAAzBnD,EAAqB,aAAgB,8DAAkEI,OAAOC,yBAAyBL,EAAO,SAAQI,OAAOE,eAAeN,EAAO,OAAO,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,gLAAgL,IAAQL,OAAOC,yBAAyBL,EAAO,cAAaI,OAAOE,eAAeN,EAAO,YAAY,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,yLAAyL,IAAQL,OAAOC,yBAAyBL,EAAO,eAAcI,OAAOE,eAAeN,EAAO,aAAa,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,2LAA2L,IAAQL,OAAOC,yBAAyBL,EAAO,mBAAkBI,OAAOE,eAAeN,EAAO,iBAAiB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,mMAAmM,IAAikET,EAAmB,aAAEuC,EAAWvC,EAAmB,YAAMI,OAAOC,yBAAyBL,EAAO,eAAcI,OAAOE,eAAeN,EAAO,aAAa,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,2LAA2L,IAAyBT,EAAsB,eAAgBA,EAAsB,cAAMI,OAAOC,yBAAyBL,EAAO,kBAAiBI,OAAOE,eAAeN,EAAO,gBAAgB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,iMAAiM,IAA4B,iBAAd2C,aAAwB3C,GAAM,mCAAgE,IAAI4C,GAAM,EAAuB,SAASF,EAAOG,EAAUL,GAAUK,GAAW7C,GAAM,qBAAqBwC,EAAM,CAAy9B,IAAsBM,EAAiC,oBAAdC,YAA0B,IAAIA,YAAY,aAAQ3D,EAAU,SAAS4D,EAAkBC,EAAKC,EAAIC,GAA6D,IAA7C,IAAIC,EAAOF,EAAIC,EAAmBE,EAAOH,EAAUD,EAAKI,MAAWA,GAAQD,MAAUC,EAAO,GAAGA,EAAOH,EAAI,IAAID,EAAKK,UAAUR,EAAa,OAAOA,EAAYS,OAAON,EAAKK,SAASJ,EAAIG,IAAyB,IAAX,IAAIG,EAAI,GAASN,EAAIG,GAAO,CAAC,IAAII,EAAGR,EAAKC,KAAO,GAAQ,IAAHO,EAAL,CAAoD,IAAIC,EAAe,GAAZT,EAAKC,KAAU,GAAa,MAAN,IAAHO,GAAJ,CAAmE,IAAIE,EAAe,GAAZV,EAAKC,KAAuQ,GAAhP,MAAN,IAAHO,GAAcA,GAAO,GAAHA,IAAQ,GAAGC,GAAI,EAAEC,GAAqB,MAAN,IAAHF,IAAalB,EAAS,gCAAgCkB,EAAGG,SAAS,IAAI,0FAA0FH,GAAO,EAAHA,IAAO,GAAGC,GAAI,GAAGC,GAAI,EAAc,GAAZV,EAAKC,MAAaO,EAAG,MAAOD,GAAKK,OAAOC,aAAaL,OAAQ,CAAC,IAAIM,EAAGN,EAAG,MAAMD,GAAKK,OAAOC,aAAa,MAAMC,GAAI,GAAG,MAAS,KAAHA,EAAQ,CAAvY,MAAhDP,GAAKK,OAAOC,cAAiB,GAAHL,IAAQ,EAAEC,EAA7E,MAArCF,GAAKK,OAAOC,aAAaL,EAA6e,CAAE,OAAOD,CAAG,CAAC,SAASQ,EAAaC,EAAId,GAAgB,OAAOc,EAAIjB,EAAkBkB,EAAOD,EAAId,GAAgB,EAAE,CAAy7C,IAAIgB,EAAkC,oBAAdpB,YAA0B,IAAIA,YAAY,iBAAY3D,EAAU,SAASgF,EAAcH,EAAId,GAAgBT,EAAOuB,EAAI,GAAG,EAAE,iEAAkI,IAAjE,IAAIZ,EAAOY,EAAQf,EAAIG,GAAQ,EAAMgB,EAAOnB,EAAIC,EAAe,IAAUD,GAAKmB,IAASC,EAAQpB,MAAOA,EAAkB,IAAdG,EAAOH,GAAK,GAAYe,EAAI,IAAIE,EAAc,OAAOA,EAAaZ,OAAOW,EAAOZ,SAASW,EAAIZ,IAAiC,IAAnB,IAAIkB,EAAE,EAAMf,EAAI,KAAW,CAAC,IAAIgB,EAASC,EAAOR,EAAM,EAAFM,GAAK,GAAG,GAAa,GAAVC,GAAaD,GAAGpB,EAAe,EAAE,OAAOK,IAAMe,EAAEf,GAAKK,OAAOC,aAAaU,EAAS,CAAE,CAAC,SAASE,EAAclB,EAAImB,EAAOC,GAAsU,GAArTlC,EAAOiC,EAAO,GAAG,EAAE,iEAAiEjC,EAA+B,iBAAjBkC,EAA0B,mIAAmJxF,IAAlBwF,IAA6BA,EAAgB,YAAcA,EAAgB,EAAE,OAAO,EAAuH,IAAlG,IAAIC,EAASF,EAAWG,GAA3CF,GAAiB,GAAqE,EAAXpB,EAAIuB,OAASH,EAAgB,EAAEpB,EAAIuB,OAAeR,EAAE,EAAEA,EAAEO,IAAkBP,EAAE,CAAC,IAAIC,EAAShB,EAAIwB,WAAWT,GAAGE,EAAOE,GAAQ,GAAGH,EAASG,GAAQ,CAAC,CAAqB,OAApBF,EAAOE,GAAQ,GAAG,EAASA,EAAOE,CAAQ,CAAC,SAASI,EAAiBzB,GAAK,OAAkB,EAAXA,EAAIuB,MAAQ,CAAC,SAASG,EAAcjB,EAAId,GAAgBT,EAAOuB,EAAI,GAAG,EAAE,kEAAqF,IAAnB,IAAIM,EAAE,EAAMf,EAAI,KAAWe,GAAGpB,EAAe,IAAG,CAAC,IAAIgC,EAAMC,EAAOnB,EAAM,EAAFM,GAAK,GAAG,GAAU,GAAPY,EAAS,MAAU,KAAFZ,EAAKY,GAAO,MAAM,CAAC,IAAIpB,EAAGoB,EAAM,MAAM3B,GAAKK,OAAOC,aAAa,MAAMC,GAAI,GAAG,MAAS,KAAHA,EAAQ,MAAMP,GAAKK,OAAOC,aAAaqB,EAAO,CAAC,OAAO3B,CAAG,CAAC,SAAS6B,EAAc7B,EAAImB,EAAOC,GAAuU,GAAtTlC,EAAOiC,EAAO,GAAG,EAAE,kEAAkEjC,EAA+B,iBAAjBkC,EAA0B,mIAAmJxF,IAAlBwF,IAA6BA,EAAgB,YAAcA,EAAgB,EAAE,OAAO,EAA4D,IAA1D,IAAIC,EAASF,EAAWtB,EAAOwB,EAASD,EAAgB,EAAUL,EAAE,EAAEA,EAAEf,EAAIuB,SAASR,EAAE,CAAC,IAAIC,EAAShB,EAAIwB,WAAWT,GAA6K,GAAvKC,GAAU,OAAOA,GAAU,QAA8CA,EAAS,QAAiB,KAATA,IAAgB,IAAmB,KAAxEhB,EAAIwB,aAAaT,IAA4Da,EAAOT,GAAQ,GAAGH,GAASG,GAAQ,GAAY,EAAEtB,EAAO,KAAK,CAAqB,OAApB+B,EAAOT,GAAQ,GAAG,EAASA,EAAOE,CAAQ,CAAC,SAASS,EAAiB9B,GAAe,IAAV,IAAI+B,EAAI,EAAUhB,EAAE,EAAEA,EAAEf,EAAIuB,SAASR,EAAE,CAAC,IAAIC,EAAShB,EAAIwB,WAAWT,GAAMC,GAAU,OAAOA,GAAU,SAAQD,EAAEgB,GAAK,CAAC,CAAC,OAAOA,CAAG,CAAyX,IAA2GC,EAAOC,EAAMvB,EAAOO,EAAOH,EAAQc,EAAOM,EAAQC,EAAQC,EAAQ,SAASC,EAA2BC,GAAKN,EAAOM,EAAIvG,EAAc,MAAEkG,EAAM,IAAIM,UAAUD,GAAKvG,EAAe,OAAEkF,EAAO,IAAIuB,WAAWF,GAAKvG,EAAe,OAAE6F,EAAO,IAAIa,WAAWH,GAAKvG,EAAe,OAAE2E,EAAO,IAAItC,WAAWkE,GAAKvG,EAAgB,QAAE+E,EAAQ,IAAI4B,YAAYJ,GAAKvG,EAAgB,QAAEmG,EAAQ,IAAIS,YAAYL,GAAKvG,EAAgB,QAAEoG,EAAQ,IAAIS,aAAaN,GAAKvG,EAAgB,QAAEqG,EAAQ,IAAIS,aAAaP,EAAI,CAAC,IAAuBQ,EAAU,MAAM5D,GAAuB,EAAE,4BAA4B,IAAI6D,EAAY,QAAWhH,EAAoB,aAAEmD,EAAO6D,IAAchH,EAAoB,YAAE,yDAAyD,IAAIiH,EAAuBjH,EAAuB,gBAAG,SAAylC,SAASkH,IAAmB/D,IAAkB,EAAV4D,IAAiBZ,EAAuB,GAAdY,GAAW,IAAM,SAASZ,EAAuB,GAAdY,GAAW,IAAM,WAAWlB,EAAO,GAAG,UAAU,CAAC,SAASsB,IAAmB,IAAG9D,EAAH,CAAgB,IAAI+D,EAAQjB,EAAuB,GAAdY,GAAW,IAAUM,EAAQlB,EAAuB,GAAdY,GAAW,IAAkB,UAATK,GAA4B,YAATC,GAAqB5G,GAAM,mHAAmH4G,EAAQhD,SAAS,IAAI,IAAI+C,EAAQ/C,SAAS,KAAoB,aAAZwB,EAAO,IAAgBpF,GAAM,oFAAhU,CAAoZ,CAA7pDL,OAAOC,yBAAyBL,EAAO,mBAAkBI,OAAOE,eAAeN,EAAO,iBAAiB,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,2MAA2M,IAAI0C,EAAO8D,GAAwBD,EAAY,yDAAyDC,EAAuB,kBAAkBD,EAAY,KAAK7D,EAA2B,oBAAbuD,YAAgD,oBAAfI,mBAA4DjH,IAAhC6G,WAAWY,UAAUvD,eAAiDlE,IAA3B6G,WAAWY,UAAU5G,IAAgB,wDAAgF8B,EAAtBxC,EAAmB,WAAcA,EAAmB,WAAkB,IAAIoD,YAAYmE,OAAO,CAAC,QAAUN,EAArqD,MAA2sD,QAAU,WAA2ChB,EAAOzD,EAAWyD,QAAgD9C,GAAzC8D,EAAuBhB,EAAOuB,YAAvzD,OAAi3D,GAAGrE,GAA8B,GAAGmD,EAA2BL,GAA0lB,WAAY,IAAIwB,EAAI,IAAIhB,WAAW,GAAOiB,EAAG,IAAIlB,UAAUiB,EAAIxB,QAAqB,GAAbwB,EAAI,GAAG,MAAiB,MAARC,EAAG,IAAkB,KAARA,EAAG,GAAQ,KAAK,yDAA0D,CAA9K,GAAkL,IAAIC,EAAa,GAAOC,EAAW,GAAOC,EAAW,GAAOC,EAAc,GAAOC,GAAmB,EAAy1B5E,EAAO6E,KAAKC,KAAK,+HAA+H9E,EAAO6E,KAAKE,OAAO,iIAAiI/E,EAAO6E,KAAKG,MAAM,gIAAgIhF,EAAO6E,KAAKI,MAAM,gIAAgI,IAAIC,EAAgB,EAAMC,EAAqB,KAASC,EAAsB,KAASC,EAAsB,CAAC,EAAspC,SAAS/H,GAAMgI,GAASzI,EAAgB,SAAGA,EAAgB,QAAEyI,GAAe3F,EAAT2F,GAAM,IAAapF,GAAM,EAAkB,IAAu7IqF,EAAkzBC,EAAruKC,EAAO,SAASH,EAAK,SAAgtKE,EAA/M,WAAwB,IAAIE,EAAM,IAAIvH,MAAM,IAAIuH,EAAMC,MAAM,CAAC,IAAI,MAAM,IAAIxH,KAAK,CAAC,MAAMyH,GAAGF,EAAME,CAAC,CAAC,IAAIF,EAAMC,MAAO,MAAM,4BAA6B,CAAC,OAAOD,EAAMC,MAAMzE,UAAU,CAA8B2E,GAAkBhJ,EAAwB,kBAAE2I,GAAI,KAAK3I,EAAwB,mBAAl4B0I,EAAM,gBAAm5BC,EAAv3BM,QAAQP,GAAM,SAASQ,GAAG,IAArMC,EAAyMC,GAAzMD,EAAoND,EAA9MlG,EAAS,+EAAsFmG,GAAkH,OAAOD,IAAIE,EAAEF,EAAEE,EAAE,KAAKF,EAAE,GAAG,KAAl/IT,EAAKG,EAAO,IAAIG,EAAE,IAAI3F,YAAYiG,aAAaZ,GAA4B,MAAtB1I,EAAmBgJ,GAASA,CAAC,CAAxS/I,EAAwB,gBAAE,CAAC,EAAEA,EAAwB,gBAAE,CAAC,EAAiP,IAAIsJ,GAAG,CAACT,MAAM,WAAWpI,GAAM,mPAAmP,EAAE8I,KAAK,WAAWD,GAAGT,OAAO,EAAEW,eAAe,WAAWF,GAAGT,OAAO,EAAEY,oBAAoB,WAAWH,GAAGT,OAAO,EAAEa,eAAe,WAAWJ,GAAGT,OAAO,EAAE3G,KAAK,WAAWoH,GAAGT,OAAO,EAAEc,MAAM,WAAWL,GAAGT,OAAO,EAAEe,eAAe,WAAWN,GAAGT,OAAO,EAAEgB,YAAY,WAAWP,GAAGT,OAAO,EAAEiB,gBAAgB,WAAWR,GAAGT,OAAO,EAAEkB,WAAW,WAAsBT,GAAGT,OAAO,GAAG7I,EAA0B,kBAAEsJ,GAAGE,eAAexJ,EAA+B,uBAAEsJ,GAAGG,oBAA+L,SAASO,GAAUC,GAAU,OAArLhG,EAAsMgG,EAAlMC,EAA4G,wCAA7F5F,OAAOgD,UAAU6C,WAAWlG,EAAIkG,WAAWD,GAA8B,IAAtBjG,EAAIrC,QAAQsI,GAArG,IAAmBjG,EAAIiG,CAAyN,CAA6B,SAASE,GAAoBC,EAAKC,GAAU,OAAO,WAAW,IAAIC,EAAYF,EAASG,EAAIF,EAAoX,OAAvWA,IAAUE,EAAIxK,EAAY,KAAEmD,EAAO4E,EAAmB,oBAAoBwC,EAAY,0CAA0CpH,GAAO,EAAe,oBAAoBoH,EAAY,yFAA6FC,EAAIH,IAAOlH,EAAOqH,EAAIH,GAAM,6BAA6BE,EAAY,eAAsBC,EAAIH,GAAMI,MAAM,KAAKC,UAAU,CAAC,CAAC,IAAtrrBC,GAA0rrBC,GAAe,cAAuF,SAASC,KAAY,IAAI,GAAGtI,EAAY,OAAO,IAAIF,WAAWE,GAAY,GAAGhB,EAAY,OAAOA,EAAWqJ,IAAqB,KAAK,iDAAkD,CAAC,MAAM9H,GAAKrC,GAAMqC,EAAI,CAAC,CAA0hE,SAASgI,GAAqBC,GAAW,KAAMA,EAAUvF,OAAO,GAAE,CAAC,IAAIwF,EAASD,EAAUE,QAAQ,GAAoB,mBAAVD,EAAV,CAA0D,IAAI7B,EAAK6B,EAAS7B,KAAsB,iBAAPA,OAAmCtJ,IAAfmL,EAASE,IAAiBzI,EAAUjC,IAAI2I,EAAd1G,GAA2BA,EAAUjC,IAAI2I,EAAd1G,CAAoBuI,EAASE,KAAW/B,OAAoBtJ,IAAfmL,EAASE,IAAgB,KAAKF,EAASE,IAA/L,MAAzBF,EAAShL,EAAoN,CAAC,CAA7oFgK,GAAUY,MAArurBD,GAAgxrBC,GAA1BA,GAA7urB5K,EAAmB,WAAUA,EAAmB,WAAE2K,GAAKnJ,GAAwBA,EAAgBmJ,IAAyzyB,IAAIQ,GAAsC,EAAtCA,GAAwD,EAAxDA,GAAsE,EAAtEA,GAAsF,GAAtFA,GAAyG,GAAzGA,GAAiH,GAAkH,SAASC,GAAcC,GAAQC,KAAKD,OAAOA,EAAOC,KAAK5G,IAAI2G,EAAOF,GAAwBG,KAAKC,SAAS,SAASC,GAAM3F,EAAOyF,KAAK5G,IAAIyG,IAAgC,GAAGK,CAAI,EAAEF,KAAKG,SAAS,WAAW,OAAO5F,EAAOyF,KAAK5G,IAAIyG,IAAgC,EAAE,EAAEG,KAAKI,eAAe,SAASC,GAAY9F,EAAOyF,KAAK5G,IAAIyG,IAAsC,GAAGQ,CAAU,EAAEL,KAAKM,eAAe,WAAW,OAAO/F,EAAOyF,KAAK5G,IAAIyG,IAAsC,EAAE,EAAEG,KAAKO,aAAa,SAASC,GAAUjG,EAAOyF,KAAK5G,IAAIyG,IAAoC,GAAGW,CAAQ,EAAER,KAAKS,WAAW,SAASC,GAAQA,EAAOA,EAAO,EAAE,EAAE9F,EAAMoF,KAAK5G,IAAIyG,GAAkC,GAAGa,CAAM,EAAEV,KAAKW,WAAW,WAAW,OAA4D,GAArD/F,EAAMoF,KAAK5G,IAAIyG,GAAkC,EAAK,EAAEG,KAAKY,aAAa,SAASC,GAAUA,EAASA,EAAS,EAAE,EAAEjG,EAAMoF,KAAK5G,IAAIyG,GAAoC,GAAGgB,CAAQ,EAAEb,KAAKc,aAAa,WAAW,OAA8D,GAAvDlG,EAAMoF,KAAK5G,IAAIyG,GAAoC,EAAK,EAAEG,KAAK/B,KAAK,SAASiC,EAAKG,GAAYL,KAAKC,SAASC,GAAMF,KAAKI,eAAeC,GAAYL,KAAKO,aAAa,GAAGP,KAAKS,YAAW,GAAOT,KAAKY,cAAa,EAAM,EAAEZ,KAAKe,QAAQ,WAAW,IAAIC,EAAMzG,EAAOyF,KAAK5G,IAAIyG,IAAoC,GAAGtF,EAAOyF,KAAK5G,IAAIyG,IAAoC,GAAGmB,EAAM,CAAC,EAAEhB,KAAKiB,YAAY,WAAW,IAAIC,EAAK3G,EAAOyF,KAAK5G,IAAIyG,IAAoC,GAAgF,OAA7EtF,EAAOyF,KAAK5G,IAAIyG,IAAoC,GAAGqB,EAAK,EAAErJ,EAAOqJ,EAAK,GAAiB,IAAPA,CAAQ,CAAC,CAAqB,SAASC,KAA6B,OAAOA,GAA2BC,oBAAoB,CAAC,CAAgc,IAAIC,GAAoB,CAAC,EAAE,SAASC,GAAeC,GAAa,KAAMA,EAAYrH,QAAO,CAAC,IAAId,EAAImI,EAAYC,MAAcD,EAAYC,KAAMC,CAAIrI,EAAI,CAAC,CAAC,SAASsI,GAA2BC,GAAS,OAAO3B,KAAmB,aAAEnF,EAAQ8G,GAAS,GAAG,CAAC,IAAIC,GAAqB,CAAC,EAAMC,GAAgB,CAAC,EAAMC,GAAiB,CAAC,EAAMC,GAAO,GAAOC,GAAO,GAAG,SAASC,GAAsBlD,GAAM,QAAGxK,IAAYwK,EAAM,MAAM,WAAmD,IAAImD,GAA5CnD,EAAKA,EAAKpB,QAAQ,iBAAiB,MAAgBxD,WAAW,GAAG,OAAG+H,GAAGH,IAAQG,GAAGF,GAAc,IAAIjD,EAAiBA,CAAK,CAAC,SAASoD,GAAoBpD,EAAKqD,GAAuC,OAAjCrD,EAAKkD,GAAsBlD,GAAa,IAAIsD,SAAS,OAAO,mBAAmBtD,EAAnB,uEAApB,CAA6HqD,EAAK,CAAC,SAASE,GAAYC,EAAcC,GAAW,IAAIC,EAAWN,GAAoBK,GAAU,SAASE,GAAS1C,KAAKjB,KAAKyD,EAAUxC,KAAK0C,QAAQA,EAAQ,IAAIlF,EAAM,IAAIxH,MAAM0M,GAASlF,WAAiBjJ,IAARiJ,IAAmBwC,KAAKxC,MAAMwC,KAAKjH,WAAW,KAAKyE,EAAMG,QAAQ,qBAAqB,IAAI,IAA4O,OAAzO8E,EAAWzG,UAAUlH,OAAO6N,OAAOJ,EAAcvG,WAAWyG,EAAWzG,UAAU4G,YAAYH,EAAWA,EAAWzG,UAAUjD,SAAS,WAAW,YAAkBxE,IAAfyL,KAAK0C,QAA4B1C,KAAKjB,KAAiBiB,KAAKjB,KAAK,KAAKiB,KAAK0C,OAAQ,EAASD,CAAU,CAAC,IAAII,QAActO,EAAU,SAASuO,GAAmBJ,GAAS,MAAM,IAAIG,GAAcH,EAAQ,CAAC,SAASK,GAA8BC,EAAQC,EAAeC,GAA0F,SAASC,EAAWC,GAAgB,IAAIC,EAAiBH,EAAkBE,GAAmBC,EAAiBnJ,SAAS8I,EAAQ9I,QAAQ4I,GAAmB,mCAAmC,IAAI,IAAIpJ,EAAE,EAAEA,EAAEsJ,EAAQ9I,SAASR,EAAG4J,GAAaN,EAAQtJ,GAAG2J,EAAiB3J,GAAI,CAApVsJ,EAAQO,SAAQ,SAASrD,GAAM4B,GAAiB5B,GAAM+C,CAAc,IAAiR,IAAIG,EAAe,IAAII,MAAMP,EAAe/I,QAAYuJ,EAAkB,GAAOC,EAAW,EAAET,EAAeM,SAAQ,SAASI,EAAGjK,GAAMmI,GAAgBtM,eAAeoO,GAAKP,EAAe1J,GAAGmI,GAAgB8B,IAASF,EAAkBG,KAAKD,GAAQ/B,GAAqBrM,eAAeoO,KAAK/B,GAAqB+B,GAAI,IAAG/B,GAAqB+B,GAAIC,MAAK,WAAWR,EAAe1J,GAAGmI,GAAgB8B,KAAMD,IAA2BD,EAAkBvJ,QAAQiJ,EAAWC,EAAgB,IAAG,IAAM,IAAIK,EAAkBvJ,QAAQiJ,EAAWC,EAAgB,CAA+hD,SAASS,GAAiBC,GAAM,OAAOA,GAAM,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,MAAM,IAAIC,UAAU,sBAAsBD,GAAM,CAAyI,IAAIE,QAAiBzP,EAAU,SAAS0P,GAAiB7K,GAA0B,IAArB,IAAI8K,EAAI,GAAOC,EAAE/K,EAAUC,EAAO8K,IAAID,GAAKF,GAAiB3K,EAAO8K,MAAM,OAAOD,CAAG,CAAC,IAAIE,QAAa7P,EAAU,SAAS8P,GAAkB3B,GAAS,MAAM,IAAI0B,GAAa1B,EAAQ,CAAC,SAASY,GAAagB,EAAQC,EAAmBC,GAA6B,GAApBA,EAAQA,GAAS,CAAC,IAAO,mBAAmBD,GAAqB,MAAM,IAAIR,UAAU,2DAA2D,IAAIhF,EAAKwF,EAAmBxF,KAAmG,GAA1FuF,GAASD,GAAkB,SAAStF,EAAK,iDAAoD8C,GAAgBtM,eAAe+O,GAAS,CAAC,GAAGE,EAAQC,6BAA8B,OAAYJ,GAAkB,yBAAyBtF,EAAK,UAAW,CAA8E,GAA7E8C,GAAgByC,GAASC,SAA0BzC,GAAiBwC,GAAY1C,GAAqBrM,eAAe+O,GAAS,CAAC,IAAI7E,EAAUmC,GAAqB0C,UAAgB1C,GAAqB0C,GAAS7E,EAAU8D,SAAQ,SAASmB,GAAIA,GAAI,GAAE,CAAC,CAA+vC,SAASC,GAA4BC,GAAwFP,GAAsCO,EAAtEC,GAAGC,QAAQC,gBAAgBhG,KAAgD,4BAA4B,CAAC,IAAIiG,IAAkB,EAAM,SAASC,GAAgBC,GAAQ,CAA8I,SAASC,GAAmBN,GAAIA,EAAGO,MAAMpE,OAAO,EAAe,IAAI6D,EAAGO,MAAMpE,OAAzN,SAAuB6D,GAAOA,EAAGQ,SAAUR,EAAGS,aAAaC,cAAcV,EAAGQ,UAAeR,EAAGC,QAAQC,gBAAgBQ,cAAcV,EAAGzL,IAAK,CAAgGoM,CAAcX,EAAI,CAAC,SAASY,GAAgBP,GAAQ,MAAG,oBAAqBQ,mBAAmBD,GAAgB,SAASP,GAAQ,OAAOA,CAAM,EAASA,IAAOF,GAAkB,IAAIU,mBAAkB,SAASC,GAAM,IAAI,IAAIC,EAAOD,EAAKE,QAAQD,EAAOE,KAAKF,EAAOD,EAAKE,OAAO,CAAC,IAAIhB,EAAGe,EAAO5E,MAAU6D,EAAGzL,IAA0D+L,GAAmBN,GAAxExN,QAAQI,KAAK,2BAA2BoN,EAAGzL,IAAiC,CAAC,IAAGqM,GAAgB,SAASP,GAA+D,OAAvDF,GAAkBe,SAASb,EAAOA,EAAOL,GAAGK,EAAOL,IAAWK,CAAM,EAAED,GAAgB,SAASC,GAAQF,GAAkBgB,WAAWd,EAAOL,GAAG,EAASY,GAAgBP,GAAO,CAAguB,IAAIe,QAAc1R,EAAc2R,GAAc,GAAG,SAASC,KAAsB,KAAMD,GAAchM,QAAO,CAAC,IAAI0K,EAAIsB,GAAc1E,MAAMoD,EAAIC,GAAGuB,iBAAgB,EAAMxB,EAAY,QAAG,CAAC,CAAgpB,SAASyB,KAAc,CAAC,IAAIC,GAAmB,CAAC,EAAE,SAASC,GAAoBC,EAAMC,EAAWC,GAAW,QAAGnS,IAAYiS,EAAMC,GAAYE,cAAc,CAAC,IAAIC,EAASJ,EAAMC,GAAYD,EAAMC,GAAY,WAAyP,OAA1OD,EAAMC,GAAYE,cAAcpR,eAAe6J,UAAUlF,SAASmK,GAAkB,aAAaqC,EAAU,iDAAiDtH,UAAUlF,OAAO,uBAAuBsM,EAAMC,GAAYE,cAAc,MAAaH,EAAMC,GAAYE,cAAcvH,UAAUlF,QAAQiF,MAAMa,KAAKZ,UAAU,EAAEoH,EAAMC,GAAYE,cAAc,GAAGH,EAAMC,GAAYE,cAAcC,EAASC,UAAUD,CAAQ,CAAC,CAAC,SAASE,GAAmB/H,EAAKiC,EAAM+F,GAAiBrS,EAAOa,eAAewJ,UAAUxK,IAAYwS,QAAcxS,IAAYG,EAAOqK,GAAM4H,oBAAepS,IAAYG,EAAOqK,GAAM4H,cAAcI,KAAe1C,GAAkB,gCAAgCtF,EAAK,WAAWwH,GAAoB7R,EAAOqK,EAAKA,GAASrK,EAAOa,eAAewR,IAAe1C,GAAkB,uFAAuF0C,EAAa,MAAMrS,EAAOqK,GAAM4H,cAAcI,GAAc/F,IAAWtM,EAAOqK,GAAMiC,OAASzM,IAAYwS,IAAcrS,EAAOqK,GAAMgI,aAAaA,GAAc,CAAC,SAASC,GAAgBjI,EAAK6D,EAAYqE,EAAkB1B,EAAc2B,EAAUC,EAAcC,EAAOC,GAAUrH,KAAKjB,KAAKA,EAAKiB,KAAK4C,YAAYA,EAAY5C,KAAKiH,kBAAkBA,EAAkBjH,KAAKuF,cAAcA,EAAcvF,KAAKkH,UAAUA,EAAUlH,KAAKmH,cAAcA,EAAcnH,KAAKoH,OAAOA,EAAOpH,KAAKqH,SAASA,EAASrH,KAAKsH,qBAAqB,EAAE,CAAC,SAASC,GAAcnO,EAAIoO,EAASC,GAAc,KAAMD,IAAWC,GAAkBD,EAASJ,QAAQ/C,GAAkB,gCAAgCoD,EAAa1I,KAAK,wBAAwByI,EAASzI,MAAM3F,EAAIoO,EAASJ,OAAOhO,GAAKoO,EAASA,EAASN,UAAU,OAAO9N,CAAG,CAAC,SAASsO,GAAoCnG,EAAY2D,GAAQ,GAAY,OAATA,EAAwF,OAAtElF,KAAK2H,aAAatD,GAAkB,uBAAuBrE,KAAKjB,MAAa,EAAMmG,EAAOL,IAAIR,GAAkB,gBAAgBuD,GAAa1C,GAAQ,UAAUlF,KAAKjB,MAAUmG,EAAOL,GAAGzL,KAAKiL,GAAkB,mDAAmDrE,KAAKjB,MAAM,IAAI8I,EAAY3C,EAAOL,GAAGC,QAAQC,gBAAsF,OAA9DwC,GAAcrC,EAAOL,GAAGzL,IAAIyO,EAAY7H,KAAK+E,gBAA2B,CAAC,SAAS+C,GAAyBvG,EAAY2D,GAAQ,IAAI9L,EAAI,GAAY,OAAT8L,EAAwF,OAAtElF,KAAK2H,aAAatD,GAAkB,uBAAuBrE,KAAKjB,MAASiB,KAAK+H,gBAAgB3O,EAAI4G,KAAKgI,iBAAkC,OAAdzG,GAAoBA,EAAYqC,KAAK5D,KAAKuF,cAAcnM,GAAYA,GAAgB,EAAO8L,EAAOL,IAAIR,GAAkB,gBAAgBuD,GAAa1C,GAAQ,UAAUlF,KAAKjB,MAAUmG,EAAOL,GAAGzL,KAAKiL,GAAkB,mDAAmDrE,KAAKjB,OAAUiB,KAAKiI,SAAS/C,EAAOL,GAAGC,QAAQmD,SAAS5D,GAAkB,oCAAoCa,EAAOL,GAAGS,aAAaJ,EAAOL,GAAGS,aAAavG,KAAKmG,EAAOL,GAAGC,QAAQ/F,MAAM,sBAAsBiB,KAAKjB,MAAM,IAAI8I,EAAY3C,EAAOL,GAAGC,QAAQC,gBAAkF,GAAlE3L,EAAImO,GAAcrC,EAAOL,GAAGzL,IAAIyO,EAAY7H,KAAK+E,iBAAoB/E,KAAK+H,eAAwH,YAArGxT,IAAY2Q,EAAOL,GAAGQ,UAAUhB,GAAkB,mDAA0DrE,KAAKkI,eAAe,KAAK,EAAKhD,EAAOL,GAAGS,eAAetF,KAAM5G,EAAI8L,EAAOL,GAAGQ,SAAchB,GAAkB,oCAAoCa,EAAOL,GAAGS,aAAaJ,EAAOL,GAAGS,aAAavG,KAAKmG,EAAOL,GAAGC,QAAQ/F,MAAM,sBAAsBiB,KAAKjB,MAAM,MAAM,KAAK,EAAE3F,EAAI8L,EAAOL,GAAGQ,SAAS,MAAM,KAAK,EAAE,GAAGH,EAAOL,GAAGS,eAAetF,KAAM5G,EAAI8L,EAAOL,GAAGQ,aAAa,CAAC,IAAI8C,EAAajD,EAAc,QAAI9L,EAAI4G,KAAKoI,SAAShP,EAAIiP,IAAiB,WAAWF,EAAqB,QAAG,KAAqB,OAAd5G,GAAoBA,EAAYqC,KAAK5D,KAAKuF,cAAcnM,EAAK,CAAC,MAAM,QAAQiL,GAAkB,+BAAgC,OAAOjL,CAAG,CAAC,SAASkP,GAAuC/G,EAAY2D,GAAQ,GAAY,OAATA,EAAwF,OAAtElF,KAAK2H,aAAatD,GAAkB,uBAAuBrE,KAAKjB,MAAa,EAAMmG,EAAOL,IAAIR,GAAkB,gBAAgBuD,GAAa1C,GAAQ,UAAUlF,KAAKjB,MAAUmG,EAAOL,GAAGzL,KAAKiL,GAAkB,mDAAmDrE,KAAKjB,MAASmG,EAAOL,GAAGC,QAAQmD,SAAS5D,GAAkB,mCAAmCa,EAAOL,GAAGC,QAAQ/F,KAAK,sBAAsBiB,KAAKjB,MAAM,IAAI8I,EAAY3C,EAAOL,GAAGC,QAAQC,gBAAsF,OAA9DwC,GAAcrC,EAAOL,GAAGzL,IAAIyO,EAAY7H,KAAK+E,gBAA2B,CAA2R,SAASwD,GAAgBnP,EAAIoO,EAASC,GAAc,GAAGD,IAAWC,EAAc,OAAOrO,EAAI,QAAG7E,IAAYkT,EAAaP,UAAW,OAAO,KAAK,IAAIsB,EAAGD,GAAgBnP,EAAIoO,EAASC,EAAaP,WAAW,OAAQ,OAALsB,EAAkB,KAAYf,EAAaJ,SAASmB,EAAG,CAAumB,IAAIC,GAAoB,CAAC,EAA0S,SAASC,GAAgB1M,EAAU2M,GAAoT,OAAxSA,EAAO7D,SAAU6D,EAAOvP,KAAK0J,GAAmB,8CAAkE6F,EAAOrD,gBAA+BqD,EAAOtD,UAA2CvC,GAAmB,oDAAoD6F,EAAOvD,MAAM,CAACpE,MAAM,GAAUyE,GAAgB3Q,OAAO6N,OAAO3G,EAAU,CAAC6I,GAAG,CAAC7D,MAAM2H,KAAU,CAAk1D,SAASC,GAAkB7J,EAAKgG,EAAgB4C,EAAYM,EAAQF,EAAec,EAAYX,EAAcY,EAAcd,EAAeI,EAAS7C,GAAevF,KAAKjB,KAAKA,EAAKiB,KAAK+E,gBAAgBA,EAAgB/E,KAAK2H,YAAYA,EAAY3H,KAAKiI,QAAQA,EAAQjI,KAAK+H,eAAeA,EAAe/H,KAAK6I,YAAYA,EAAY7I,KAAKkI,cAAcA,EAAclI,KAAK8I,cAAcA,EAAc9I,KAAKgI,eAAeA,EAAehI,KAAKoI,SAASA,EAASpI,KAAKuF,cAAcA,EAAkBwC,QAA4CxT,IAA5BwQ,EAAgBmC,UAAyNlH,KAAiB,WAAE8H,GAAlNG,GAASjI,KAAiB,WAAE0H,GAAoC1H,KAAK+I,mBAAmB,OAAU/I,KAAiB,WAAEsI,GAAuCtI,KAAK+I,mBAAmB,KAAuD,CAAC,SAASC,GAAoBjK,EAAKiC,EAAM+F,GAAkBrS,EAAOa,eAAewJ,IAAO+D,GAAmB,4CAA0CvO,IAAYG,EAAOqK,GAAM4H,oBAAepS,IAAYwS,EAAcrS,EAAOqK,GAAM4H,cAAcI,GAAc/F,GAAWtM,EAAOqK,GAAMiC,EAAMtM,EAAOqK,GAAM8H,SAASE,EAAa,CAAiR,SAASkC,GAAwBC,EAAUC,GAAwL,IAAIC,GAApF,IAA3FF,EAAUjF,GAAiBiF,IAAiD5S,QAAQ,KAA5Z,SAAsB+S,EAAIjQ,GAAKvB,EAAOwR,EAAI/S,QAAQ,MAAM,EAAE,oDAAoD,IAAIgT,EAAS,GAAG,OAAO,WAAWA,EAASpP,OAAOkF,UAAUlF,OAAO,IAAI,IAAIR,EAAE,EAAEA,EAAE0F,UAAUlF,OAAOR,IAAK4P,EAAS5P,GAAG0F,UAAU1F,GAAG,OAAvgqB,SAAiB2P,EAAIjQ,EAAImQ,GAAM,OAAsB,GAAnBF,EAAI/S,QAAQ,KAA/Z,SAAuB+S,EAAIjQ,EAAImQ,GAAkN,OAA5M1R,EAAO,WAAWwR,KAAO3U,EAAO,iDAAiD2U,EAAI,KAAQE,GAAMA,EAAKrP,OAAQrC,EAAO0R,EAAKrP,SAASmP,EAAIG,UAAU,GAAG7L,QAAQ,KAAK,MAAMzD,QAAarC,EAAmB,GAAZwR,EAAInP,QAAcqP,GAAMA,EAAKrP,OAAexF,EAAO,WAAW2U,GAAKlK,MAAM,KAAK,CAAC/F,GAAKqQ,OAAOF,IAAc7U,EAAO,WAAW2U,GAAKK,KAAK,KAAKtQ,EAAI,CAAgEuQ,CAAcN,EAAIjQ,EAAImQ,GAAapS,EAAUjC,IAAIkE,GAAK+F,MAAM,KAAKoK,EAAK,CAAy4pBK,CAAQP,EAAIjQ,EAAIkQ,EAAS,CAAC,CAA8JO,CAAaX,EAAUC,GAAoBhS,EAAUjC,IAAIiU,GAAwJ,MAArG,mBAALC,GAAiB/E,GAAkB,2CAA2C6E,EAAU,KAAKC,GAAoBC,CAAE,CAAC,IAAIU,QAAiBvV,EAAU,SAASwV,GAAY7J,GAAM,IAAI9G,EAAI4Q,GAAe9J,GAAUsI,EAAGvE,GAAiB7K,GAAgB,OAAX6Q,GAAM7Q,GAAYoP,CAAE,CAAC,SAAS0B,GAAsBxH,EAAQyH,GAAO,IAAIC,EAAa,GAAOC,EAAK,CAAC,EAAmN,MAArBF,EAAM5G,SAAlM,SAAS+G,EAAMpK,GAASmK,EAAKnK,IAAiB2B,GAAgB3B,KAAiB4B,GAAiB5B,GAAO4B,GAAiB5B,GAAMqD,QAAQ+G,IAAcF,EAAaxG,KAAK1D,GAAMmK,EAAKnK,IAAM,GAAI,IAA4B,IAAI4J,GAAiBpH,EAAQ,KAAK0H,EAAaG,IAAIR,IAAaS,KAAK,CAAC,OAAO,CAAo1E,SAASC,GAAoBrF,EAAMsF,GAA2B,IAAb,IAAIC,EAAM,GAAWjR,EAAE,EAAEA,EAAE0L,EAAM1L,IAAKiR,EAAM/G,KAAKrJ,GAAQmQ,GAAc,GAAGhR,IAAI,OAAOiR,CAAK,CAA87D,SAASC,GAAqBlE,EAAUmE,EAASC,EAAUC,EAAeC,GAAe,IAAInE,EAASgE,EAAS3Q,OAAU2M,EAAS,GAAGxC,GAAkB,kFAA4K,IAA1F,IAAI4G,EAAgC,OAAdJ,EAAS,IAAuB,OAAZC,EAAqBI,GAAqB,EAAcxR,EAAE,EAAEA,EAAEmR,EAAS3Q,SAASR,EAAG,GAAiB,OAAdmR,EAASnR,SAA4CnF,IAAjCsW,EAASnR,GAAGqP,mBAA+B,CAACmC,GAAqB,EAAK,KAAK,CAAE,IAAIC,EAA2B,SAAnBN,EAAS,GAAG9L,KAAkBqM,EAAS,GAAOC,EAAc,GAAG,IAAQ3R,EAAE,EAAEA,EAAEmN,EAAS,IAAInN,EAAG0R,IAAe,IAAJ1R,EAAM,KAAK,IAAI,MAAMA,EAAE2R,IAAoB,IAAJ3R,EAAM,KAAK,IAAI,MAAMA,EAAE,QAAQ,IAAI4R,EAAc,mBAAmBrJ,GAAsByE,GAAW,IAAI0E,EAAxD,kCAAsGvE,EAAS,GAA/G,oCAAyJH,EAAU,8DAA8DG,EAAS,GAA1O,iBAAoQqE,IAAsBI,GAAe,2BAA0B,IAAIC,EAAUL,EAAqB,cAAc,OAAWM,EAAM,CAAC,oBAAoB,UAAU,KAAK,iBAAiB,UAAU,cAAkBC,EAAM,CAACpH,GAAkB0G,EAAeC,EAAc1J,GAAeuJ,EAAS,GAAGA,EAAS,IAAyG,IAAlGI,IAAmBK,GAAe,yCAAyCC,EAAU,cAAqB7R,EAAE,EAAEA,EAAEmN,EAAS,IAAInN,EAAG4R,GAAe,UAAU5R,EAAE,kBAAkBA,EAAE,eAAe6R,EAAU,QAAQ7R,EAAE,SAASmR,EAASnR,EAAE,GAAGqF,KAAK,KAAKyM,EAAM5H,KAAK,UAAUlK,GAAG+R,EAAM7H,KAAKiH,EAASnR,EAAE,IAA8M,GAAvMuR,IAAmBI,EAAc,aAAaA,EAAcnR,OAAO,EAAE,KAAK,IAAImR,GAAcC,IAAgBH,EAAQ,YAAY,IAAI,cAAcE,EAAcnR,OAAO,EAAE,KAAK,IAAImR,EAAc,OAAUH,EAAsBI,GAAe,sCAAsC,IAAQ5R,EAAEuR,EAAkB,EAAE,EAAEvR,EAAEmR,EAAS3Q,SAASR,EAAE,CAAC,IAAIgS,EAAc,IAAJhS,EAAM,YAAY,OAAOA,EAAE,GAAG,QAA4C,OAAjCmR,EAASnR,GAAGqP,qBAA2BuC,GAAeI,EAAU,SAASA,EAAU,SAASb,EAASnR,GAAGqF,KAAK,KAAKyM,EAAM5H,KAAK8H,EAAU,SAASD,EAAM7H,KAAKiH,EAASnR,GAAGqP,oBAAoB,CAAqM,OAAhMoC,IAASG,GAAe,sDAA8DA,GAAe,MAAME,EAAM5H,KAAK0H,GAA5jF,SAAc1I,EAAY+I,GAAc,KAAK/I,aAAuBP,UAAW,MAAM,IAAI0B,UAAU,4CAA4CnB,EAAY,4BAA4B,IAAIgJ,EAAMzJ,GAAoBS,EAAY7D,MAAM,uBAAsB,WAAW,IAAG6M,EAAM5P,UAAU4G,EAAY5G,UAAU,IAAI4I,EAAI,IAAIgH,EAAUC,EAAEjJ,EAAYzD,MAAMyF,EAAI+G,GAAc,OAAOE,aAAa/W,OAAO+W,EAAEjH,CAAG,CAAktEkH,CAAKzJ,SAASmJ,GAAOrM,MAAM,KAAKsM,EAA6B,CAAi6C,IAAIM,GAAgB,GAAOC,GAAmB,CAAC,CAAC,EAAE,CAAChL,WAAMzM,GAAW,CAACyM,MAAM,MAAM,CAACA,OAAM,GAAM,CAACA,OAAM,IAAQ,SAASiL,GAAe/G,GAAWA,EAAO,GAAG,KAAM8G,GAAmB9G,GAAQ1E,WAAUwL,GAAmB9G,QAAQ3Q,EAAUwX,GAAgBnI,KAAKsB,GAAQ,CAA+Z,SAASmD,GAAiBrH,GAAO,OAAOA,GAAO,UAAKzM,EAAW,OAAO,EAAE,KAAK,KAAM,OAAO,EAAE,KAAK,EAAM,OAAO,EAAE,KAAK,EAAO,OAAO,EAAE,QAAS,IAAI2Q,EAAO6G,GAAgB7R,OAAO6R,GAAgBvK,MAAMwK,GAAmB9R,OAA2D,OAApD8R,GAAmB9G,GAAQ,CAAC1E,SAAS,EAAEQ,MAAMA,GAAckE,EAAQ,CAA8X,SAAS0C,GAAasE,GAAG,GAAO,OAAJA,EAAU,MAAM,OAAO,IAAIC,SAASD,EAAE,MAAO,WAAJC,GAAkB,UAAJA,GAAiB,aAAJA,EAAuBD,EAAEnT,WAAsB,GAAGmT,CAAE,CAAC,SAASE,GAA0BrN,EAAKY,GAAO,OAAOA,GAAO,KAAK,EAAE,OAAO,SAASgC,GAAS,OAAO3B,KAAmB,aAAElF,EAAQ6G,GAAS,GAAG,EAAE,KAAK,EAAE,OAAO,SAASA,GAAS,OAAO3B,KAAmB,aAAEjF,EAAQ4G,GAAS,GAAG,EAAE,QAAQ,MAAM,IAAIoC,UAAU,uBAAuBhF,GAAM,CAAyjC,SAASsN,GAA4BtN,EAAKY,EAAM2M,GAAQ,OAAO3M,GAAO,KAAK,EAAE,OAAO2M,EAAO,SAA2B3K,GAAS,OAAO/G,EAAM+G,EAAQ,EAAE,SAA2BA,GAAS,OAAOtI,EAAOsI,EAAQ,EAAE,KAAK,EAAE,OAAO2K,EAAO,SAA4B3K,GAAS,OAAO/H,EAAO+H,GAAS,EAAE,EAAE,SAA4BA,GAAS,OAAOlI,EAAQkI,GAAS,EAAE,EAAE,KAAK,EAAE,OAAO2K,EAAO,SAA4B3K,GAAS,OAAOpH,EAAOoH,GAAS,EAAE,EAAE,SAA4BA,GAAS,OAAO9G,EAAQ8G,GAAS,EAAE,EAAE,QAAQ,MAAM,IAAIoC,UAAU,yBAAyBhF,GAAM,CAAq0M,SAASwN,GAA0BzI,GAAM,IAAqG,OAAjG5M,EAAWsV,KAAK1I,EAAKnJ,EAAOuB,WAAW,QAAQ,IAAIlB,EAA2B9D,EAAWyD,QAAe,CAAC,CAAC,MAAM8C,GAAGpG,QAAQkG,MAAM,0DAA0D5C,EAAOuB,WAAW,aAAa4H,EAAK,0BAA0BrG,EAAE,CAAC,CAAy+B,IAAIgP,GAAS,CAACC,SAAS,CAAC,EAAEC,QAAQ,CAAC,KAAK,GAAG,IAAIC,UAAU,SAASC,EAAOC,GAAM,IAAInS,EAAO8R,GAASE,QAAQE,GAAQhV,EAAO8C,GAAkB,IAAPmS,GAAiB,KAAPA,IAAqB,IAATD,EAAWzV,EAAII,GAAKW,EAAkBwC,EAAO,IAAIA,EAAOT,OAAO,GAAOS,EAAOiJ,KAAKkJ,EAAM,EAAEC,aAAQxY,EAAUW,IAAI,WAAyG,OAA9F2C,EAAyBtD,MAAlBkY,GAASM,SAAoBN,GAASM,SAAS,EAAUxS,EAAOkS,GAASM,QAAQ,GAAG,EAAa,EAAEC,OAAO,SAAS5T,GAA+B,OAAlBD,EAAaC,EAAe,EAAE6T,MAAM,SAASC,EAAIC,GAAwD,OAAxCtV,EAAPqV,GAAK,EAAgB,IAAPC,GAA8B,IAARA,GAAkBD,CAAG,GAA+XrK,GAAcnO,EAAsB,cAAE4N,GAAYtM,MAAM,iBAAvtmC,WAA0D,IAAzB,IAAIoX,EAAM,IAAI5J,MAAM,KAAa9J,EAAE,EAAEA,EAAE,MAAMA,EAAG0T,EAAM1T,GAAGV,OAAOC,aAAaS,GAAGsK,GAAiBoJ,CAAK,CAAimmCC,GAAwBjJ,GAAa1P,EAAqB,aAAE4N,GAAYtM,MAAM,gBAAnp9BqQ,GAAYrK,UAAqB,UAA3jG,SAA+BsR,GAAO,KAAKtN,gBAAgBqG,IAAc,OAAO,EAAM,KAAKiH,aAAiBjH,IAAc,OAAO,EAAgJ,IAA1I,IAAIkH,EAAUvN,KAAK6E,GAAGC,QAAQC,gBAAoByI,EAAKxN,KAAK6E,GAAGzL,IAAQqU,EAAWH,EAAMzI,GAAGC,QAAQC,gBAAoB2I,EAAMJ,EAAMzI,GAAGzL,IAAUmU,EAAUrG,WAAWsG,EAAKD,EAAUnG,OAAOoG,GAAMD,EAAUA,EAAUrG,UAAU,KAAMuG,EAAWvG,WAAWwG,EAAMD,EAAWrG,OAAOsG,GAAOD,EAAWA,EAAWvG,UAAU,OAAOqG,IAAYE,GAAYD,IAAOE,CAAK,EAAumFrH,GAAYrK,UAAiB,MAAv2C,WAAgF,GAA/CgE,KAAK6E,GAAGzL,KAAKuL,GAA4B3E,MAASA,KAAK6E,GAAG8I,wBAAgD,OAAvB3N,KAAK6E,GAAGO,MAAMpE,OAAO,EAAShB,KAAU,IAAp5C4N,EAAw5CC,EAAMpI,GAAgB3Q,OAAO6N,OAAO7N,OAAOgZ,eAAe9N,MAAM,CAAC6E,GAAG,CAAC7D,OAA79C4M,EAA8/C5N,KAAK6E,GAA1/C,CAACO,MAAMwI,EAAExI,MAAMgB,gBAAgBwH,EAAExH,gBAAgBuH,wBAAwBC,EAAED,wBAAwBvU,IAAIwU,EAAExU,IAAI0L,QAAQ8I,EAAE9I,QAAQO,SAASuI,EAAEvI,SAASC,aAAasI,EAAEtI,mBAAu5C,OAAvDuI,EAAMhJ,GAAGO,MAAMpE,OAAO,EAAE6M,EAAMhJ,GAAGuB,iBAAgB,EAAayH,CAAM,EAAoiCxH,GAAYrK,UAAkB,OAAjkC,WAAkCgE,KAAK6E,GAAGzL,KAAKuL,GAA4B3E,MAASA,KAAK6E,GAAGuB,kBAAkBpG,KAAK6E,GAAG8I,yBAAyBtJ,GAAkB,yCAAyCY,GAAgBjF,MAAMmF,GAAmBnF,KAAK6E,IAAQ7E,KAAK6E,GAAG8I,0BAAyB3N,KAAK6E,GAAGQ,cAAS9Q,EAAUyL,KAAK6E,GAAGzL,SAAI7E,EAAU,EAAowB8R,GAAYrK,UAAqB,UAApyB,WAAiC,OAAOgE,KAAK6E,GAAGzL,GAAG,EAAywBiN,GAAYrK,UAAuB,YAArnB,WAAoV,OAA7SgE,KAAK6E,GAAGzL,KAAKuL,GAA4B3E,MAASA,KAAK6E,GAAGuB,kBAAkBpG,KAAK6E,GAAG8I,yBAAyBtJ,GAAkB,yCAAyC6B,GAActC,KAAK5D,MAAgC,IAAvBkG,GAAchM,QAAY+L,IAAeA,GAAcE,IAAqBnG,KAAK6E,GAAGuB,iBAAgB,EAAYpG,IAAI,EAA+gQ4I,GAAkB5M,UAAU+R,WAApzG,SAAsC3U,GAAwD,OAAhD4G,KAAK8I,gBAAe1P,EAAI4G,KAAK8I,cAAc1P,IAAYA,CAAG,EAAovGwP,GAAkB5M,UAAUqE,WAA/wG,SAAsCjH,GAAQ4G,KAAKuF,eAAevF,KAAKuF,cAAcnM,EAAK,EAA6tGwP,GAAkB5M,UAA0B,eAAE,EAAE4M,GAAkB5M,UAAgC,qBAAE0F,GAA2BkH,GAAkB5M,UAAwB,aAAr4G,SAAwCkJ,GAAoB,OAATA,GAAeA,EAAe,QAAI,EAAi1G0D,GAAkB5M,UAAwB,aAAhzD,SAAwC5C,GAAK,IAAI4U,EAAWhO,KAAK+N,WAAW3U,GAAK,IAAI4U,EAAiC,OAArBhO,KAAKK,WAAWjH,GAAY,KAAK,IAAImL,EAA7oB,SAA8B0J,EAAO7U,GAAsC,OAAjCA,EAAvO,SAA0B6U,EAAO7U,GAA0E,SAA5D7E,IAAN6E,GAAiBiL,GAAkB,+BAAqC4J,EAAO/G,WAAW9N,EAAI6U,EAAO7G,OAAOhO,GAAK6U,EAAOA,EAAO/G,UAAU,OAAO9N,CAAG,CAA+C8U,CAAiBD,EAAO7U,GAAYqP,GAAoBrP,EAAI,CAAsjB+U,CAAqBnO,KAAK+E,gBAAgBiJ,GAAY,QAAGzZ,IAAYgQ,EAAmB,CAAC,GAAG,IAAIA,EAAmBM,GAAGO,MAAMpE,MAA+E,OAAxEuD,EAAmBM,GAAGzL,IAAI4U,EAAWzJ,EAAmBM,GAAGQ,SAASjM,EAAWmL,EAA0B,QAAS,IAAIiE,EAAGjE,EAA0B,QAAyB,OAArBvE,KAAKK,WAAWjH,GAAYoP,CAAG,CAAC,SAAS4F,IAAoB,OAAGpO,KAAK+H,eAAuBW,GAAgB1I,KAAK+E,gBAAgBkC,kBAAkB,CAACnC,QAAQ9E,KAAK6I,YAAYzP,IAAI4U,EAAW1I,aAAatF,KAAKqF,SAASjM,IAAkBsP,GAAgB1I,KAAK+E,gBAAgBkC,kBAAkB,CAACnC,QAAQ9E,KAAK5G,IAAIA,GAAM,CAAC,IAA8LiV,EAA1LC,EAAWtO,KAAK+E,gBAAgBoC,cAAc6G,GAAgBO,EAAwBjI,GAAmBgI,GAAY,IAAIC,EAAyB,OAAOH,EAAkB1E,KAAK1J,MAAkCqO,EAAdrO,KAAKiI,QAAgBsG,EAAwBC,iBAA6BD,EAAwBE,YAAY,IAAIC,EAAGnG,GAAgByF,EAAWhO,KAAK+E,gBAAgBsJ,EAAOtJ,iBAAiB,OAAQ,OAAL2J,EAAkBN,EAAkB1E,KAAK1J,MAASA,KAAK+H,eAAuBW,GAAgB2F,EAAOtJ,gBAAgBkC,kBAAkB,CAACnC,QAAQuJ,EAAOjV,IAAIsV,EAAGpJ,aAAatF,KAAKqF,SAASjM,IAAkBsP,GAAgB2F,EAAOtJ,gBAAgBkC,kBAAkB,CAACnC,QAAQuJ,EAAOjV,IAAIsV,GAAK,EAA10Eha,EAAkC,0BAA9a,WAAqC,OAAOI,OAAO6Z,KAAKlG,IAAqBvO,MAAM,EAAuXxF,EAAkC,0BAAxZ,WAAqC,IAAI8T,EAAG,GAAG,IAAI,IAAIoG,KAAKnG,GAAwBA,GAAoBlT,eAAeqZ,IAAIpG,EAAG5E,KAAK6E,GAAoBmG,IAAK,OAAOpG,CAAE,EAA+Q9T,EAA4B,oBAAEyR,GAAoBzR,EAAyB,iBAAzV,SAA0Bma,GAAI5I,GAAc4I,EAAM3I,GAAchM,QAAQ+L,IAAeA,GAAcE,GAAqB,EAAgnyB2D,GAAiBpV,EAAyB,iBAAE4N,GAAYtM,MAAM,oBAAniXtB,EAA4B,oBAA9V,WAA2C,IAAZ,IAAI0Q,EAAM,EAAU1L,EAAE,EAAEA,EAAEsS,GAAmB9R,SAASR,OAA8BnF,IAAxByX,GAAmBtS,MAAkB0L,EAAO,OAAOA,CAAK,EAAiO1Q,EAAwB,gBAAxP,WAA2B,IAAI,IAAIgF,EAAE,EAAEA,EAAEsS,GAAmB9R,SAASR,EAAG,QAA2BnF,IAAxByX,GAAmBtS,GAAgB,OAAOsS,GAAmBtS,GAAI,OAAO,IAAI,EAA+mX4C,EAAWsH,KAAK,CAAC/F,KAAK,WAAWiR,IAAoB,IAAI,IAA85pDC,GAA15pDC,GAAc,CAAC,yBAA7pzC,SAAmClL,GAAM,OAAOmL,GAAQnL,EAAKjE,IAAyBA,EAAuB,EAAqmzC,YAAviwC,SAAsBzG,EAAI8G,EAAKG,GAAyP,MAApO,IAAIP,GAAc1G,GAAU6E,KAAKiC,EAAKG,GAAmC,uBAAuBc,GAAmFA,GAA2BC,sBAAjFD,GAA2BC,oBAAoB,EAA8DhI,EAAI,4JAA4J,EAAoovC,8BAAn1qC,SAAwC8V,GAAY,IAAIC,EAAI9N,GAAoB6N,UAAmB7N,GAAoB6N,GAAY,IAAIlH,EAAemH,EAAInH,eAAmBzC,EAAc4J,EAAI5J,cAAkB6J,EAAaD,EAAIE,OAAkKtM,GAA8B,CAACmM,GAA3KE,EAAa7E,KAAI,SAAS+E,GAAO,OAAOA,EAAMC,gBAAgB,IAAG9F,OAAO2F,EAAa7E,KAAI,SAAS+E,GAAO,OAAOA,EAAME,kBAAkB,MAA0D,SAASC,GAAY,IAAIJ,EAAO,CAAC,EAA4hB,OAA1hBD,EAAa7L,SAAQ,SAAS+L,EAAM5V,GAAG,IAAIgW,EAAUJ,EAAMI,UAAcH,EAAiBE,EAAW/V,GAAOiW,EAAOL,EAAMK,OAAWC,EAAcN,EAAMM,cAAkBJ,EAAmBC,EAAW/V,EAAE0V,EAAalV,QAAY2V,EAAOP,EAAMO,OAAWC,EAAcR,EAAMQ,cAAcT,EAAOK,GAAW,CAACK,KAAK,SAAS3W,GAAK,OAAOmW,EAA+B,aAAEI,EAAOC,EAAcxW,GAAK,EAAE4W,MAAM,SAAS5W,EAAIwU,GAAG,IAAIrM,EAAY,GAAGsO,EAAOC,EAAc1W,EAAIoW,EAA+B,WAAEjO,EAAYqM,IAAItM,GAAeC,EAAY,EAAE,IAAS,CAAC,CAACxC,KAAKoQ,EAAIpQ,KAAK,aAAe,SAAS3F,GAAK,IAAIoP,EAAG,CAAC,EAAE,IAAI,IAAI9O,KAAK2V,EAAQ7G,EAAG9O,GAAG2V,EAAO3V,GAAGqW,KAAK3W,GAAwB,OAAnBmM,EAAcnM,GAAYoP,CAAE,EAAE,WAAa,SAASjH,EAAYqM,GAAG,IAAI,IAAI8B,KAAaL,EAAQ,KAAKK,KAAa9B,GAAI,MAAM,IAAI7J,UAAU,oBAAoB2L,EAAU,KAAM,IAAItW,EAAI4O,IAAiB,IAAI0H,KAAaL,EAAQA,EAAOK,GAAWM,MAAM5W,EAAIwU,EAAE8B,IAAuE,OAA1C,OAAdnO,GAAoBA,EAAYqC,KAAK2B,EAAcnM,GAAYA,CAAG,EAAE,eAAiB,EAAE,qBAAuBsI,GAA2BqH,mBAAmBxD,GAAe,GAAE,EAAq3nC,sBAAznlC,SAAgCjB,EAAQvF,EAAK+E,EAAKmM,EAAUC,GAAY,IAAIvQ,EAAMkE,GAAiBC,GAAkCR,GAAagB,EAAQ,CAACvF,KAAlDA,EAAKkF,GAAiBlF,GAAsC,aAAe,SAASoR,GAAI,QAAQA,CAAE,EAAE,WAAa,SAAS5O,EAAYqM,GAAG,OAAOA,EAAEqC,EAAUC,CAAU,EAAE,eAAiB,EAAE,qBAAuB,SAASvO,GAAS,IAAIvJ,EAAK,GAAU,IAAP0L,EAAU1L,EAAKwC,OAAW,GAAU,IAAPkJ,EAAU1L,EAAKwB,MAAY,IAAU,IAAPkK,EAA2B,MAAM,IAAIC,UAAU,8BAA8BhF,GAAnE3G,EAAKmC,CAAmE,CAAC,OAAOyF,KAAmB,aAAE5H,EAAKuJ,GAAShC,GAAO,EAAEoJ,mBAAmB,MAAM,EAAqnkC,uBAAv/oB,SAAiCzE,EAAQ8L,EAAeC,EAAoBC,EAAiBC,EAAuBpJ,EAAcqJ,EAAgBpJ,EAAOqJ,EAAkBpJ,EAAStI,EAAK2R,EAAoBnL,GAAexG,EAAKkF,GAAiBlF,GAAMoI,EAAc8B,GAAwBsH,EAAuBpJ,GAAkBC,IAAQA,EAAO6B,GAAwBuH,EAAgBpJ,IAAWC,IAAUA,EAAS4B,GAAwBwH,EAAkBpJ,IAAU9B,EAAc0D,GAAwByH,EAAoBnL,GAAe,IAAIoL,EAAkB1O,GAAsBlD,GAAM+H,GAAmB6J,GAAkB,WAAWzG,GAAsB,oBAAoBnL,EAAK,wBAAwB,CAACuR,GAAkB,IAAGvN,GAA8B,CAACuB,EAAQ8L,EAAeC,GAAqBC,EAAiB,CAACA,GAAkB,IAAG,SAASM,GAAmB,IAAI1J,EAAc2J,EAA/BD,EAAKA,EAAK,GAAuFC,EAAjDP,GAAkBpJ,EAAU0J,EAAK7L,iBAAwCkC,kBAAqCZ,GAAYrK,UAAU,IAAI4G,EAAYT,GAAoBwO,GAAkB,WAAW,GAAG7b,OAAOgZ,eAAe9N,QAAQiH,EAAmB,MAAM,IAAI7C,GAAa,0BAA0BrF,GAAM,QAAGxK,IAAYwQ,EAAgB+L,iBAAkB,MAAM,IAAI1M,GAAarF,EAAK,kCAAkC,IAAIqD,EAAK2C,EAAgB+L,iBAAiB1R,UAAUlF,QAAQ,QAAG3F,IAAY6N,EAAM,MAAM,IAAIgC,GAAa,2BAA2BrF,EAAK,uCAAuCK,UAAUlF,OAAO,iBAAiBpF,OAAO6Z,KAAK5J,EAAgB+L,kBAAkB/X,WAAW,yBAAyB,OAAOqJ,EAAKjD,MAAMa,KAAKZ,UAAU,IAAO6H,EAAkBnS,OAAO6N,OAAOkO,EAAc,CAACjO,YAAY,CAAC5B,MAAM4B,KAAeA,EAAY5G,UAAUiL,EAAkB,IAAIlC,EAAgB,IAAIiC,GAAgBjI,EAAK6D,EAAYqE,EAAkB1B,EAAc2B,EAAUC,EAAcC,EAAOC,GAAc0J,EAAmB,IAAInI,GAAkB7J,EAAKgG,GAAgB,GAAK,GAAM,GAAWiM,EAAiB,IAAIpI,GAAkB7J,EAAK,IAAIgG,GAAgB,GAAM,GAAM,GAAWkM,EAAsB,IAAIrI,GAAkB7J,EAAK,UAAUgG,GAAgB,GAAM,GAAK,GAA4J,OAArJuB,GAAmBhC,GAAS,CAACmK,YAAYuC,EAAiBxC,iBAAiByC,GAAuBjI,GAAoB2H,EAAkB/N,GAAmB,CAACmO,EAAmBC,EAAiBC,EAAsB,GAAE,EAAstkB,mCAAzkkB,SAA6CC,EAAarK,EAASsK,EAAgBC,EAAiBC,EAAQrJ,GAAgBnQ,EAAOgP,EAAS,GAAG,IAAIyK,EAAY7G,GAAoB5D,EAASsK,GAAiBE,EAAQpI,GAAwBmI,EAAiBC,GAAS,IAAI9H,EAAK,CAACvB,GAAoBzG,EAAY,GAAGwB,GAA8B,GAAG,CAACmO,IAAc,SAASpG,GAAkC,IAAIpE,EAAU,gBAArCoE,EAAUA,EAAU,IAA0C/L,KAA8G,QAAtGxK,IAAYuW,EAAU/F,gBAAgB+L,mBAAkBhG,EAAU/F,gBAAgB+L,iBAAiB,SAAMvc,IAAYuW,EAAU/F,gBAAgB+L,iBAAiBjK,EAAS,GAAI,MAAM,IAAIzC,GAAa,+EAA+EyC,EAAS,GAAG,gBAAgBiE,EAAU/L,KAAK,uGAAwyB,OAAjsB+L,EAAU/F,gBAAgB+L,iBAAiBjK,EAAS,GAAG,WAA8BqD,GAAsB,oBAAoBY,EAAU/L,KAAK,wBAAwBuS,EAAY,EAAEvO,GAA8B,GAAGuO,GAAY,SAASzG,GAA4c,OAAlcC,EAAU/F,gBAAgB+L,iBAAiBjK,EAAS,GAAG,WAA+BzH,UAAUlF,SAAS2M,EAAS,GAAGxC,GAAkBqC,EAAU,gBAAgBtH,UAAUlF,OAAO,yBAAyB2M,EAAS,IAAItF,EAAYrH,OAAO,EAAEqP,EAAKrP,OAAO2M,EAAS,IAAI,IAAInN,EAAE,EAAEA,EAAEmN,IAAWnN,EAAG6P,EAAK7P,GAAGmR,EAASnR,GAAe,WAAE6H,EAAYnC,UAAU1F,EAAE,IAAI,IAAIN,EAAIiY,EAAQlS,MAAM,KAAKoK,GAAkC,OAA5BjI,GAAeC,GAAoBsJ,EAAS,GAAiB,aAAEzR,EAAI,EAAQ,EAAE,IAAS,EAAE,GAAE,EAAomhB,gCAAt8b,SAA0C8X,EAAazK,EAAWI,EAASsK,EAAgBC,EAAiBG,EAAWC,EAAQC,GAAe,IAAIH,EAAY7G,GAAoB5D,EAASsK,GAAiB1K,EAAWxC,GAAiBwC,GAAY8K,EAAWtI,GAAwBmI,EAAiBG,GAAYxO,GAA8B,GAAG,CAACmO,IAAc,SAASpG,GAAkC,IAAIpE,GAA3BoE,EAAUA,EAAU,IAA2B/L,KAAK,IAAI0H,EAA6F,SAASiL,IAAsBxH,GAAsB,eAAexD,EAAU,wBAAwB4K,EAAY,CAAjMG,GAAe3G,EAAU/F,gBAAgBuC,qBAAqB1D,KAAK6C,GAA+H,IAAID,EAAMsE,EAAU/F,gBAAgBkC,kBAAsB0K,EAAOnL,EAAMC,GAA8sB,YAA/rBlS,IAAYod,QAAQpd,IAAYod,EAAOhL,eAAegL,EAAOC,YAAY9G,EAAU/L,MAAM4S,EAAO9K,WAAWA,EAAS,GAAG6K,EAAoB7K,SAASA,EAAS,EAAE6K,EAAoBE,UAAU9G,EAAU/L,KAAKyH,EAAMC,GAAYiL,IAAyBnL,GAAoBC,EAAMC,EAAWC,GAAWF,EAAMC,GAAYE,cAAcE,EAAS,GAAG6K,GAAoB3O,GAA8B,GAAGuO,GAAY,SAASzG,GAAU,IAAIgH,EAAejH,GAAqBlE,EAAUmE,EAASC,EAAUyG,EAAWC,GAA6L,YAAjLjd,IAAYiS,EAAMC,GAAYE,eAAekL,EAAehL,SAASA,EAAS,EAAEL,EAAMC,GAAYoL,GAAoBrL,EAAMC,GAAYE,cAAcE,EAAS,GAAGgL,EAAqB,EAAE,IAAS,EAAE,GAAE,EAA0mZ,uBAA/pX,SAAiCvN,EAAQvF,GAAkCuE,GAAagB,EAAQ,CAACvF,KAAlDA,EAAKkF,GAAiBlF,GAAsC,aAAe,SAASmG,GAAQ,IAAIsD,EAAGwD,GAAmB9G,GAAQlE,MAA6B,OAAvBiL,GAAe/G,GAAesD,CAAE,EAAE,WAAa,SAASjH,EAAYP,GAAO,OAAOqH,GAAiBrH,EAAM,EAAE,eAAiB,EAAE,qBAAuBU,GAA2BqH,mBAAmB,MAAM,EAAo1W,uBAAn6V,SAAiCzE,EAAQvF,EAAK+E,GAAM,IAAInE,EAAMkE,GAAiBC,GAAkCR,GAAagB,EAAQ,CAACvF,KAAlDA,EAAKkF,GAAiBlF,GAAsC,aAAe,SAASiC,GAAO,OAAOA,CAAK,EAAE,WAAa,SAASO,EAAYP,GAAO,GAAkB,iBAARA,GAAiC,kBAARA,EAAmB,MAAM,IAAI+C,UAAU,mBAAmB6D,GAAa5G,GAAO,QAAQhB,KAAKjB,MAAM,OAAOiC,CAAK,EAAE,eAAiB,EAAE,qBAAuBoL,GAA0BrN,EAAKY,GAAOoJ,mBAAmB,MAAM,EAAk/U,0BAAj/U,SAAoChK,EAAK8H,EAASsK,EAAgBjI,EAAUqI,EAAW1C,GAAI,IAAIhE,EAASJ,GAAoB5D,EAASsK,GAAiBpS,EAAKkF,GAAiBlF,GAAMwS,EAAWtI,GAAwBC,EAAUqI,GAAYzK,GAAmB/H,GAAK,WAAWmL,GAAsB,eAAenL,EAAK,wBAAwB8L,EAAS,GAAEhE,EAAS,GAAG9D,GAA8B,GAAG8H,GAAS,SAASA,GAAU,IAAIiH,EAAiB,CAACjH,EAAS,GAAG,MAAMpB,OAAOoB,EAASkH,MAAM,IAAwG,OAApG/I,GAAoBjK,EAAK6L,GAAqB7L,EAAK+S,EAAiB,KAAKP,EAAW1C,GAAIhI,EAAS,GAAS,EAAE,GAAE,EAAo9T,yBAAz5S,SAAmCmL,EAAcjT,EAAK+E,EAAKmO,EAASC,GAAUnT,EAAKkF,GAAiBlF,IAAqB,IAAZmT,IAAeA,EAAS,YAAW,IAAIvS,EAAMkE,GAAiBC,GAAUqO,EAAa,SAASnR,GAAO,OAAOA,CAAK,EAAE,GAAc,IAAXiR,EAAa,CAAC,IAAIG,EAAS,GAAG,EAAEtO,EAAKqO,EAAa,SAASnR,GAAO,OAAOA,GAAOoR,IAAWA,CAAQ,CAAC,CAAC,IAAIC,GAA0C,GAA3BtT,EAAKzI,QAAQ,YAAgBgN,GAAa0O,EAAc,CAACjT,KAAKA,EAAK,aAAeoT,EAAa,WAAa,SAAS5Q,EAAYP,GAAO,GAAkB,iBAARA,GAAiC,kBAARA,EAAmB,MAAM,IAAI+C,UAAU,mBAAmB6D,GAAa5G,GAAO,QAAQhB,KAAKjB,MAAM,GAAGiC,EAAMiR,GAAUjR,EAAMkR,EAAU,MAAM,IAAInO,UAAU,qBAAqB6D,GAAa5G,GAAO,wDAAwDjC,EAAK,wCAAwCkT,EAAS,KAAKC,EAAS,MAAM,OAAOG,EAAerR,IAAQ,EAAQ,EAANA,CAAO,EAAE,eAAiB,EAAE,qBAAuBqL,GAA4BtN,EAAKY,EAAiB,IAAXsS,GAAclJ,mBAAmB,MAAM,EAAo+Q,6BAAn+Q,SAAuCzE,EAAQgO,EAAcvT,GAAM,IAAmHwT,EAAnG,CAACrX,UAAUnE,WAAWoE,WAAWE,YAAYD,WAAWE,YAAYC,aAAaC,cAAiC8W,GAAe,SAASE,EAAiBtN,GAAyB,IAAI9M,EAAKyC,EAAYiJ,EAAK1L,EAA3C8M,IAAe,GAA6CuN,EAAKra,EAAK8M,EAAO,GAAG,OAAO,IAAIqN,EAAG5X,EAAO8X,EAAK3O,EAAK,CAA6BR,GAAagB,EAAQ,CAACvF,KAAlDA,EAAKkF,GAAiBlF,GAAsC,aAAeyT,EAAiB,eAAiB,EAAE,qBAAuBA,GAAkB,CAAC/N,8BAA6B,GAAM,EAA8/P,4BAA7/P,SAAsCH,EAAQvF,GAAkC,IAAI2T,EAAuB,iBAAvD3T,EAAKkF,GAAiBlF,IAA+CuE,GAAagB,EAAQ,CAACvF,KAAKA,EAAK,aAAe,SAASiC,GAAO,IAAiCrI,EAA7BuB,EAAOW,EAAQmG,GAAO,GAAW,GAAG0R,EAA4C,IAA3B,IAAIC,EAAe3R,EAAM,EAAUtH,EAAE,EAAEA,GAAGQ,IAASR,EAAE,CAAC,IAAIkZ,EAAe5R,EAAM,EAAEtH,EAAE,GAAGA,GAAGQ,GAAgC,GAAxBb,EAAOuZ,GAAmB,CAAC,IAA8CC,EAAc1Z,EAAawZ,EAA7DC,EAAeD,QAA+Epe,IAANoE,EAAiBA,EAAIka,GAAmBla,GAAKK,OAAOC,aAAa,GAAGN,GAAKka,GAAcF,EAAeC,EAAe,CAAC,CAAC,KAAM,CAAC,IAAIE,EAAE,IAAItP,MAAMtJ,GAAQ,IAAQR,EAAE,EAAEA,EAAEQ,IAASR,EAAGoZ,EAAEpZ,GAAGV,OAAOC,aAAaI,EAAO2H,EAAM,EAAEtH,IAAIf,EAAIma,EAAEtI,KAAK,GAAG,CAAc,OAAbP,GAAMjJ,GAAcrI,CAAG,EAAE,WAAa,SAAS4I,EAAYP,GAAoE,IAAI+R,EAA9D/R,aAAiBgS,cAAahS,EAAM,IAAIjK,WAAWiK,IAAqB,IAAIiS,EAAmC,iBAARjS,EAAsBiS,GAAqBjS,aAAiBjK,YAAYiK,aAAiBkS,mBAAmBlS,aAAiB9F,WAAYmJ,GAAkB,yCAAkF0O,EAAtCL,GAAiBO,EAA+B,WAAW,OAAzgjD,SAAyBta,GAAe,IAAV,IAAI+B,EAAI,EAAUhB,EAAE,EAAEA,EAAEf,EAAIuB,SAASR,EAAE,CAAC,IAAIyZ,EAAExa,EAAIwB,WAAWT,GAAMyZ,GAAG,OAAOA,GAAG,QAAMA,EAAE,QAAU,KAAFA,IAAS,IAAwB,KAApBxa,EAAIwB,aAAaT,IAAWyZ,GAAG,MAAMzY,EAAoBA,GAARyY,GAAG,KAAU,EAAUA,GAAG,MAAW,EAAY,CAAC,CAAC,OAAOzY,CAAG,CAAuxiD0Y,CAAgBpS,EAAM,EAAiB,WAAW,OAAOA,EAAM9G,MAAM,EAAE,IAAp1jDvB,EAAImB,EAAOC,EAA60jDG,EAAO6Y,IAAgB3Z,EAAI6V,GAAQ,EAAE/U,EAAO,GAA0B,GAAvBW,EAAQzB,GAAK,GAAGc,EAAUwY,GAAiBO,EAAl7jDta,EAAo9jDqI,EAAh9jDlH,EAAs9jDV,EAAI,EAAl8jDvB,EAA+B,iBAAhDkC,EAAq9jDG,EAAO,GAAn6jD,6HAArgC,SAA2BvB,EAAIP,EAAKib,EAAOtZ,GAAiB,KAAKA,EAAgB,GAAG,OAAO,EAA0D,IAAxD,IAAIuZ,EAASD,EAAW9a,EAAO8a,EAAOtZ,EAAgB,EAAUL,EAAE,EAAEA,EAAEf,EAAIuB,SAASR,EAAE,CAAC,IAAIyZ,EAAExa,EAAIwB,WAAWT,GAAoF,GAA9EyZ,GAAG,OAAOA,GAAG,QAAkCA,EAAE,QAAU,KAAFA,IAAS,IAAO,KAA9Cxa,EAAIwB,aAAaT,IAAqCyZ,GAAG,IAAI,CAAC,GAAGE,GAAQ9a,EAAO,MAAMH,EAAKib,KAAUF,CAAC,MAAM,GAAGA,GAAG,KAAK,CAAC,GAAGE,EAAO,GAAG9a,EAAO,MAAMH,EAAKib,KAAU,IAAIF,GAAG,EAAE/a,EAAKib,KAAU,IAAM,GAAFF,CAAI,MAAM,GAAGA,GAAG,MAAM,CAAC,GAAGE,EAAO,GAAG9a,EAAO,MAAMH,EAAKib,KAAU,IAAIF,GAAG,GAAG/a,EAAKib,KAAU,IAAIF,GAAG,EAAE,GAAG/a,EAAKib,KAAU,IAAM,GAAFF,CAAI,KAAK,CAAC,GAAGE,EAAO,GAAG9a,EAAO,MAAS4a,GAAG,SAAQzb,EAAS,gCAAgCyb,EAAEpa,SAAS,IAAI,oJAAoJX,EAAKib,KAAU,IAAIF,GAAG,GAAG/a,EAAKib,KAAU,IAAIF,GAAG,GAAG,GAAG/a,EAAKib,KAAU,IAAIF,GAAG,EAAE,GAAG/a,EAAKib,KAAU,IAAM,GAAFF,CAAI,CAAC,CAAC/a,EAAKib,GAAQ,CAAwB,CAA+NE,CAAkB5a,EAAIU,EAAOS,EAAOC,QAAmwjD,GAAGkZ,EAAqB,IAAI,IAAIvZ,EAAE,EAAEA,EAAEQ,IAASR,EAAE,CAAC,IAAI8Z,EAASxS,EAAM7G,WAAWT,GAAM8Z,EAAS,MAAKvJ,GAAM7Q,GAAKiL,GAAkB,2DAA0DhL,EAAOD,EAAI,EAAEM,GAAG8Z,CAAQ,MAAO,IAAQ9Z,EAAE,EAAEA,EAAEQ,IAASR,EAAGL,EAAOD,EAAI,EAAEM,GAAGsH,EAAMtH,GAAwD,OAAlC,OAAd6H,GAAoBA,EAAYqC,KAAKqG,GAAM7Q,GAAYA,CAAG,EAAE,eAAiB,EAAE,qBAAuBsI,GAA2BqH,mBAAmB,SAAS3P,GAAK6Q,GAAM7Q,EAAI,GAAG,EAAoyM,6BAAnyM,SAAuCkL,EAAQmP,EAAS1U,GAAkC,IAAI2U,EAAaC,EAAaC,EAAQC,EAAelU,EAAjFZ,EAAKkF,GAAiBlF,GAA+E,IAAX0U,GAAcC,EAAana,EAAcoa,EAAa9Z,EAAcga,EAAezZ,EAAiBwZ,EAAQ,WAAW,OAAOna,CAAO,EAAEkG,EAAM,GAAqB,IAAX8T,IAAcC,EAAarZ,EAAcsZ,EAAanZ,EAAcqZ,EAAepZ,EAAiBmZ,EAAQ,WAAW,OAAO/Y,CAAO,EAAE8E,EAAM,GAAE2D,GAAagB,EAAQ,CAACvF,KAAKA,EAAK,aAAe,SAASiC,GAA0F,IAAnF,IAAoDrI,EAAhDuB,EAAOW,EAAQmG,GAAO,GAAO8S,EAAKF,IAAsBjB,EAAe3R,EAAM,EAAUtH,EAAE,EAAEA,GAAGQ,IAASR,EAAE,CAAC,IAAIkZ,EAAe5R,EAAM,EAAEtH,EAAE+Z,EAAS,GAAG/Z,GAAGQ,GAAqC,GAA7B4Z,EAAKlB,GAAgBjT,GAAU,CAAC,IAAmDkT,EAAca,EAAaf,EAA7DC,EAAeD,QAAoFpe,IAANoE,EAAiBA,EAAIka,GAAmBla,GAAKK,OAAOC,aAAa,GAAGN,GAAKka,GAAcF,EAAeC,EAAea,CAAQ,CAAC,CAAc,OAAbxJ,GAAMjJ,GAAcrI,CAAG,EAAE,WAAa,SAAS4I,EAAYP,GAA2B,iBAARA,GAAmBqD,GAAkB,6CAA6CtF,GAAM,IAAI7E,EAAO2Z,EAAe7S,GAAW5H,EAAI6V,GAAQ,EAAE/U,EAAOuZ,GAAqI,OAA3H5Y,EAAQzB,GAAK,GAAGc,GAAQyF,EAAMgU,EAAa3S,EAAM5H,EAAI,EAAEc,EAAOuZ,GAA2B,OAAdlS,GAAoBA,EAAYqC,KAAKqG,GAAM7Q,GAAYA,CAAG,EAAE,eAAiB,EAAE,qBAAuBsI,GAA2BqH,mBAAmB,SAAS3P,GAAK6Q,GAAM7Q,EAAI,GAAG,EAAg9J,8BAA/8J,SAAwCkL,EAAQvF,EAAKgV,EAAqB/L,EAAe0I,EAAoBnL,GAAelE,GAAoBiD,GAAS,CAACvF,KAAKkF,GAAiBlF,GAAMiJ,eAAeiB,GAAwB8K,EAAqB/L,GAAgBzC,cAAc0D,GAAwByH,EAAoBnL,GAAe8J,OAAO,GAAG,EAAyrJ,oCAAxrJ,SAA8CH,EAAWQ,EAAUH,EAAiByE,EAAgBrE,EAAOC,EAAcJ,EAAmByE,EAAgBpE,EAAOC,GAAezO,GAAoB6N,GAAYG,OAAOzL,KAAK,CAAC8L,UAAUzL,GAAiByL,GAAWH,iBAAiBA,EAAiBI,OAAO1G,GAAwB+K,EAAgBrE,GAAQC,cAAcA,EAAcJ,mBAAmBA,EAAmBK,OAAO5G,GAAwBgL,EAAgBpE,GAAQC,cAAcA,GAAe,EAA+wI,sBAA9wI,SAAgCxL,EAAQvF,GAAkCuE,GAAagB,EAAQ,CAAC4P,QAAO,EAAKnV,KAA9DA,EAAKkF,GAAiBlF,GAAkD,eAAiB,EAAE,aAAe,WAA2B,EAAE,WAAa,SAASwC,EAAYqM,GAAmB,GAAG,EAAglI,cAAgB3B,GAAe,cAA9mI,SAAwB/G,GAAWA,EAAO,IAAG8G,GAAmB9G,GAAQ1E,UAAU,EAAE,EAAyjI,kBAA53H,SAA4BN,EAAKiU,GAA7N,IAA+B7P,EAAuB8P,EAA2Q,YAA1O7f,KAAjC6f,EAAKvS,GAA5ByC,EAA+NpE,KAArJmE,GAAkBqC,sCAA+BqD,GAAYzF,IAAkK+D,IAArGnI,EAA5CkU,GAAkI,qBAAED,GAAgC,EAAowH,MAAnwH,WAAkBhf,IAAO,EAAyvH,sBAAxvH,SAAgCkf,EAAK/f,EAAIggB,GAAKjb,EAAOkb,WAAWF,EAAK/f,EAAIA,EAAIggB,EAAI,EAAstH,uBAAj3G,SAAiCE,GAAeA,KAA8B,EAAE,IAAIC,EAA5Ypb,EAAOa,OAAyarC,EAAO2c,EAAcC,GAAS,IAAIC,EAAY,WAAW,GAAGF,EAAcE,EAA8H,OAAjHld,EAAI,4CAA4Cgd,EAAc,4BAA4BE,EAAY,YAAkB,EAA+B,IAAzB,IAA5rlD9W,EAA6tlD+W,EAAQ,EAAEA,GAAS,EAAEA,GAAS,EAAE,CAAC,IAAIC,EAAkBH,GAAS,EAAE,GAAGE,GAASC,EAAkBlY,KAAKmY,IAAID,EAAkBJ,EAAc,WAAW,IAAIM,EAAQpY,KAAKmY,IAAIH,IAAt4lD9W,EAA05lDlB,KAAKqY,IAAnN,SAAmOP,EAAcI,IAAmB,MAAt7lD,IAAGhX,GAAm7lD,MAAv6lDA,EAAu6lD,OAAr5lDA,IAAg9lD,GAAnC2O,GAA0BuI,GAAyB,OAAO,CAAK,CAAgG,OAA/Ftd,EAAI,gCAAgCid,EAAQ,aAAaK,EAAQ,+BAAqC,CAAK,EAA8nF,SAAv6D,SAAmBE,EAAGC,EAAIC,EAAOC,GAAgB,IAAV,IAAIb,EAAI,EAAU5a,EAAE,EAAEA,EAAEwb,EAAOxb,IAAI,CAA2D,IAA1D,IAAIN,EAAImB,EAAO0a,EAAM,EAAFvb,GAAK,GAAOgB,EAAIH,EAAO0a,GAAO,EAAFvb,EAAI,IAAI,GAAW0b,EAAE,EAAEA,EAAE1a,EAAI0a,IAAK3I,GAASG,UAAUoI,EAAG3b,EAAOD,EAAIgc,IAAId,GAAK5Z,CAAG,CAAqB,OAApBH,EAAO4a,GAAM,GAAGb,EAAW,CAAC,EAAstD,OAASpd,EAAW,gBAAzuD,SAA0Bme,EAAKC,GAAa5gB,EAAO6gB,iBAAgB7gB,EAAO6gB,gBAAgBF,EAAKC,EAAS,EAAoqD,YAAnqD,SAAsBE,GAAqB,GAA6qD1G,IAA1k8C,WAAsB,IAA1iH2G,EAA8iHC,EAAK,CAAC,IAAM1G,GAAc,uBAAyBA,IAAe,SAAS2G,EAAgBC,EAASC,GAAQ,IAAIC,EAAQF,EAASE,QAAQphB,EAAY,IAAEohB,EAA6Dje,EAArDV,EAAUzC,EAAY,IAA6B,0BAAmB,mCAAxrG,SAA6B+gB,GAA8O,GAA1O1Y,IAAqBrI,EAA+B,wBAAGA,EAA+B,uBAAEqI,GAAoB0Y,GAAI5d,EAAOqF,EAAsBuY,WAAYvY,EAAsBuY,IAASje,EAAI,8CAAkE,GAAjBuF,IAA8C,OAAvBC,IAA6B+Y,cAAc/Y,GAAsBA,EAAqB,MAAQC,GAAsB,CAAC,IAAIyC,EAASzC,EAAsBA,EAAsB,KAAKyC,GAAU,CAAE,CAAuvFsW,CAAoB,mBAAmB,CAA71HP,EAA+2H,mBAA32H1Y,IAAqBrI,EAA+B,wBAAGA,EAA+B,uBAAEqI,GAAoB0Y,GAAI5d,GAAQqF,EAAsBuY,IAAKvY,EAAsBuY,GAAI,EAA4B,OAAvBzY,GAAkD,oBAAdiZ,cAA2BjZ,EAAqBiZ,aAAY,WAAW,GAAGle,EAAqE,OAA9Dge,cAAc/Y,QAAsBA,EAAqB,MAAY,IAAIpF,GAAM,EAAM,IAAI,IAAIse,KAAOhZ,EAA2BtF,IAAOA,GAAM,EAAKJ,EAAI,uCAAsCA,EAAI,eAAe0e,GAAQte,GAAOJ,EAAI,gBAAiB,GAAE,OAAWA,EAAI,4CAAq1G,IAAI2e,EAAWzhB,EAAO,SAAS0hB,EAA0B9Y,GAAQzF,EAAOnD,IAASyhB,EAAW,oHAAoHA,EAAW,KAAKR,EAAgBrY,EAAiB,SAAE,CAAC,SAAS+Y,EAAuBC,GAAU,OAAvgCrf,IAAazB,IAAoBC,GAAuC,mBAAR8gB,MAAwQ5hB,QAAQC,UAAU4hB,KAAKjX,IAApQgX,MAAMjX,GAAe,CAACmX,YAAY,gBAAgBD,MAAK,SAASxf,GAAU,IAAIA,EAAa,GAAG,KAAK,uCAAuCsI,GAAe,IAAI,OAAOtI,EAAsB,aAAG,IAAG0f,OAAM,WAAW,OAAOnX,IAAW,KAAmuBiX,MAAK,SAASG,GAAQ,OAAO7e,YAAY8e,YAAYD,EAAOjB,EAAK,IAAGc,KAAKF,GAAS,SAASO,GAAQrf,EAAI,0CAA0Cqf,GAAQ1hB,GAAM0hB,EAAO,GAAE,CAAwjB,GAAGniB,EAAwB,gBAAG,IAAgE,OAAhDA,EAAwB,gBAAEghB,EAAKC,EAA+B,CAAC,MAAMlY,GAAgE,OAA7DjG,EAAI,sDAAsDiG,IAAU,CAAK,EAAtvB,WAA4B,GAAIxG,GAAsD,mBAAnCa,YAAYgf,sBAAoCpY,GAAUY,KAAgC,mBAARiX,MAAwX,OAAOF,EAAuBD,GAAlYG,MAAMjX,GAAe,CAACmX,YAAY,gBAAgBD,MAAK,SAASxf,GAAqE,OAAhDc,YAAYgf,qBAAqB9f,EAAS0e,GAAoBc,KAAKJ,GAA0B,SAASS,GAAuG,OAA/Frf,EAAI,kCAAkCqf,GAAQrf,EAAI,6CAAoD6e,EAAuBD,EAA0B,GAAE,GAAiE,CAAkMW,EAA2B,CAAs84CC,GAAoCtiB,EAA2B,mBAAEoK,GAAoB,sBAAyBmL,GAAMvV,EAAc,MAAEoK,GAAoB,QAAYkL,GAAetV,EAAuB,eAAEoK,GAAoB,iBAAuRmQ,IAAtNva,EAAoD,4CAAEoK,GAAoB,8CAAoEpK,EAA0B,kBAAEoK,GAAoB,oBAAgCpK,EAAgB,QAAEoK,GAAoB,WAAi2mD,SAASmY,GAAI1N,GAAiH,SAAS2N,IAAWnI,KAAiBA,IAAU,EAAKra,EAAkB,WAAE,EAAQqD,IAA57tG8D,IAAmBhE,GAAQ4E,GAAoBA,GAAmB,EAAK+C,GAAqBlD,GAA+BT,IAAmB2D,GAAqBjD,GAA8ztG/H,EAAoBE,GAAWA,EAA6B,sBAAEA,EAA6B,uBAAImD,GAAQnD,EAAc,MAAE,4GAA52tG,WAAsC,GAAnBmH,IAAsBnH,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEwF,QAA6JwK,EAAxIhQ,EAAgB,QAAEiL,QAA0HnD,EAAc2a,QAAQzS,GAAhD,IAAsBA,EAA5GlF,GAAqBhD,EAAc,CAAkutG4a,IAAS,CAA5c7N,EAAKA,GAAM7T,EAAcqH,EAAgB,IAAUnB,IAAtiuG,WAAkB,GAAGlH,EAAe,OAA8E,IAA/C,mBAAlBA,EAAe,SAAcA,EAAe,OAAE,CAACA,EAAe,SAASA,EAAe,OAAEwF,QAAimBwK,EAA7kBhQ,EAAe,OAAEiL,QAAgkBtD,EAAa8a,QAAQzS,GAA9C,IAAqBA,EAAljBlF,GAAqBnD,EAAa,CAA+1tGgb,GAAYta,EAAgB,IAA8WrI,EAAkB,WAAGA,EAAkB,UAAE,cAAc4iB,YAAW,WAAWA,YAAW,WAAW5iB,EAAkB,UAAE,GAAG,GAAE,GAAGwiB,GAAO,GAAE,IAAQA,IAAQrb,KAAkB,CAA0f,GAAl9oDnH,EAAgB,QAAEoK,GAAoB,UAAwBpK,EAAkB,UAAEoK,GAAoB,aAA8BpK,EAAqB,aAAEoK,GAAoB,gBAA+BpK,EAAmB,WAAEoK,GAAoB,cAA4BpK,EAAkB,UAAEoK,GAAoB,YAAsBpK,EAAc,MAAEoK,GAAoB,QAAyBpK,EAAqB,aAAEoK,GAAoB,gBAAoBhK,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,WAASA,EAAc,MAAE,WAAWS,GAAM,mFAAmF,GAAML,OAAOC,yBAAyBL,EAAO,WAASA,EAAc,MAAE,WAAWS,GAAM,mFAAmF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,yLAAyL,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,4LAA4L,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,sLAAsL,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,0LAA0L,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,+LAA+L,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,0LAA0L,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,wLAAwL,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,kLAAkL,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,WAASA,EAAc,MAAE,WAAWS,GAAM,mFAAmF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,WAASA,EAAc,MAAE,WAAWS,GAAM,mFAAmF,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,SAAOA,EAAY,IAAE,WAAWS,GAAM,iFAAiF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,SAAOA,EAAY,IAAE,WAAWS,GAAM,iFAAiF,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,UAAQA,EAAa,KAAE,WAAWS,GAAM,kFAAkF,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,QAAMA,EAAW,GAAE,WAAWS,GAAM,gFAAgF,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,WAASA,EAAc,MAAE,WAAWS,GAAM,mFAAmF,GAAML,OAAOC,yBAAyBL,EAAO,SAAOA,EAAY,IAAE,WAAWS,GAAM,iFAAiF,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,iCAA+BA,EAAoC,4BAAE,WAAWS,GAAM,yGAAyG,GAAML,OAAOC,yBAAyBL,EAAO,QAAMA,EAAW,GAAE,WAAWS,GAAM,gFAAgF,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,mCAAiCA,EAAsC,8BAAE,WAAWS,GAAM,2GAA2G,GAAML,OAAOC,yBAAyBL,EAAO,oCAAkCA,EAAuC,+BAAE,WAAWS,GAAM,4GAA4G,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,oCAAkCA,EAAuC,+BAAE,WAAWS,GAAM,4GAA4G,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,QAAMA,EAAW,GAAE,WAAWS,GAAM,gFAAgF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,SAAOA,EAAY,IAAE,WAAWS,GAAM,iFAAiF,GAAML,OAAOC,yBAAyBL,EAAO,aAAWA,EAAgB,QAAE,WAAWS,GAAM,qFAAqF,GAAML,OAAOC,yBAAyBL,EAAO,UAAQA,EAAa,KAAE,WAAWS,GAAM,kFAAkF,GAAML,OAAOC,yBAAyBL,EAAO,SAAOA,EAAY,IAAE,WAAWS,GAAM,iFAAiF,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,UAAQA,EAAa,KAAE,WAAWS,GAAM,kFAAkF,GAAML,OAAOC,yBAAyBL,EAAO,UAAQA,EAAa,KAAE,WAAWS,GAAM,kFAAkF,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,iCAA+BA,EAAoC,4BAAE,WAAWS,GAAM,yGAAyG,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,mCAAiCA,EAAsC,8BAAE,WAAWS,GAAM,2GAA2G,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,iCAA+BA,EAAoC,4BAAE,WAAWS,GAAM,yGAAyG,GAAML,OAAOC,yBAAyBL,EAAO,8BAA4BA,EAAiC,yBAAE,WAAWS,GAAM,sGAAsG,GAAML,OAAOC,yBAAyBL,EAAO,+BAA6BA,EAAkC,0BAAE,WAAWS,GAAM,uGAAuG,GAAML,OAAOC,yBAAyBL,EAAO,gCAA8BA,EAAmC,2BAAE,WAAWS,GAAM,wGAAwG,GAAML,OAAOC,yBAAyBL,EAAO,oBAAkBA,EAAuB,eAAE,WAAWS,GAAM,4FAA4F,GAAML,OAAOC,yBAAyBL,EAAO,UAAQA,EAAa,KAAE,WAAWS,GAAM,kFAAkF,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,6BAA2BA,EAAgC,wBAAE,WAAWS,GAAM,qGAAqG,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,8BAA4BA,EAAiC,yBAAE,WAAWS,GAAM,sGAAsG,GAAML,OAAOC,yBAAyBL,EAAO,yCAAuCA,EAA4C,oCAAE,WAAWS,GAAM,iHAAiH,GAAML,OAAOC,yBAAyBL,EAAO,4CAA0CA,EAA+C,uCAAE,WAAWS,GAAM,oHAAoH,GAAML,OAAOC,yBAAyBL,EAAO,4BAA0BA,EAA+B,uBAAE,WAAWS,GAAM,oGAAoG,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,kCAAgCA,EAAqC,6BAAE,WAAWS,GAAM,0GAA0G,GAAML,OAAOC,yBAAyBL,EAAO,kCAAgCA,EAAqC,6BAAE,WAAWS,GAAM,0GAA0G,GAAML,OAAOC,yBAAyBL,EAAO,oCAAkCA,EAAuC,+BAAE,WAAWS,GAAM,4GAA4G,GAAML,OAAOC,yBAAyBL,EAAO,oCAAkCA,EAAuC,+BAAE,WAAWS,GAAM,4GAA4G,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,0BAAwBA,EAA6B,qBAAE,WAAWS,GAAM,kGAAkG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,iBAAeA,EAAoB,YAAE,WAAWS,GAAM,yFAAyF,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,iCAA+BA,EAAoC,4BAAE,WAAWS,GAAM,yGAAyG,GAAML,OAAOC,yBAAyBL,EAAO,uBAAqBA,EAA0B,kBAAE,WAAWS,GAAM,+FAA+F,GAAML,OAAOC,yBAAyBL,EAAO,wBAAsBA,EAA2B,mBAAE,WAAWS,GAAM,gGAAgG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,6BAA2BA,EAAgC,wBAAE,WAAWS,GAAM,qGAAqG,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,gCAA8BA,EAAmC,2BAAE,WAAWS,GAAM,wGAAwG,GAAML,OAAOC,yBAAyBL,EAAO,qBAAmBA,EAAwB,gBAAE,WAAWS,GAAM,6FAA6F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,YAAUA,EAAe,OAAE,WAAWS,GAAM,oFAAoF,GAAML,OAAOC,yBAAyBL,EAAO,2BAAyBA,EAA8B,sBAAE,WAAWS,GAAM,mGAAmG,GAAML,OAAOC,yBAAyBL,EAAO,cAAYA,EAAiB,SAAE,WAAWS,GAAM,sFAAsF,GAAML,OAAOC,yBAAyBL,EAAO,eAAaA,EAAkB,UAAE,WAAWS,GAAM,uFAAuF,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,gBAAcA,EAAmB,WAAE,WAAWS,GAAM,wFAAwF,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,mBAAiBA,EAAsB,cAAE,WAAWS,GAAM,2FAA2F,GAAML,OAAOC,yBAAyBL,EAAO,sBAAoBA,EAAyB,iBAAE,WAAWS,GAAM,8FAA8F,GAAML,OAAOC,yBAAyBL,EAAO,kBAAgBA,EAAqB,aAAE,WAAWS,GAAM,0FAA0F,GAAML,OAAOC,yBAAyBL,EAAO,yBAAuBA,EAA4B,oBAAE,WAAWS,GAAM,iGAAiG,GAAET,EAAyB,iBAAEkH,EAAiBlH,EAAyB,iBAAEmH,EAAqB/G,OAAOC,yBAAyBL,EAAO,iBAAgBI,OAAOE,eAAeN,EAAO,eAAe,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,0FAA0F,IAAQL,OAAOC,yBAAyBL,EAAO,gBAAeI,OAAOE,eAAeN,EAAO,cAAc,CAACO,cAAa,EAAKC,IAAI,WAAWC,GAAM,yFAAyF,IAAgJ8H,EAAsB,SAASsa,IAAgBxI,IAAUkI,KAAUlI,KAAU9R,EAAsBsa,EAAS,EAAgpB7iB,EAAY,IAAEuiB,GAA8eviB,EAAgB,QAAiF,IAAjD,mBAAnBA,EAAgB,UAAcA,EAAgB,QAAE,CAACA,EAAgB,UAASA,EAAgB,QAAEwF,OAAO,GAAGxF,EAAgB,QAAE8M,KAAlB9M,GAGpujI,OAHkxjIuiB,KAG3wjI9iB,EAAmBqjB,KAE5B,GAGM3B,EAAOC,QAAU3hB,ICfnBsjB,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBpjB,IAAjBqjB,EACH,OAAOA,EAAa9B,QAGrB,IAAID,EAAS4B,EAAyBE,GAAY,CAGjD7B,QAAS,CAAC,GAOX,OAHA+B,EAAoBF,GAAU9B,EAAQA,EAAOC,QAAS4B,GAG/C7B,EAAOC,OACf,CCrBA4B,EAAoBI,EAAKjC,IACxB,IAAIlG,EAASkG,GAAUA,EAAOkC,WAC7B,IAAOlC,EAAiB,QACxB,IAAM,EAEP,OADA6B,EAAoBM,EAAErI,EAAQ,CAAEmD,EAAGnD,IAC5BA,CAAM,ECLd+H,EAAoBM,EAAI,CAAClC,EAASmC,KACjC,IAAI,IAAI5iB,KAAO4iB,EACXP,EAAoB9J,EAAEqK,EAAY5iB,KAASqiB,EAAoB9J,EAAEkI,EAASzgB,IAC5EP,OAAOE,eAAe8gB,EAASzgB,EAAK,CAAE6iB,YAAY,EAAMhjB,IAAK+iB,EAAW5iB,IAE1E,ECNDqiB,EAAoB9J,EAAI,CAAChJ,EAAKuT,IAAUrjB,OAAOkH,UAAUzG,eAAemU,KAAK9E,EAAKuT,sBCKlF,MAAMC,EAAcC,OAAO,iBACrBC,EAAiBD,OAAO,oBACxBE,EAAeF,OAAO,wBACtBG,EAAYH,OAAO,qBACnBI,EAAcJ,OAAO,kBACrBK,EAAYC,GAAwB,iBAARA,GAA4B,OAARA,GAAgC,mBAARA,EAgDxEC,EAAmB,IAAIC,IAAI,CAC7B,CAAC,QA7CwB,CACzBC,UAAYH,GAAQD,EAASC,IAAQA,EAAIP,GACzC,SAAAW,CAAUnU,GACN,MAAM,MAAEoU,EAAK,MAAEC,GAAU,IAAIC,eAE7B,OADAC,EAAOvU,EAAKoU,GACL,CAACC,EAAO,CAACA,GACpB,EACAG,YAAYC,IACRA,EAAKC,QAiJFC,EAhJSF,EAgJO,GADTG,cA1Gd,CAAC,QA/BwB,CACzBV,UAAY9X,GAAU0X,EAAS1X,IAAUyX,KAAezX,EACxD,SAAA+X,EAAU,MAAE/X,IACR,IAAIyY,EAcJ,OAZIA,EADAzY,aAAiBhL,MACJ,CACT0jB,SAAS,EACT1Y,MAAO,CACH0B,QAAS1B,EAAM0B,QACf3D,KAAMiC,EAAMjC,KACZvB,MAAOwD,EAAMxD,QAKR,CAAEkc,SAAS,EAAO1Y,SAE5B,CAACyY,EAAY,GACxB,EACA,WAAAL,CAAYK,GACR,GAAIA,EAAWC,QACX,MAAM5kB,OAAO6kB,OAAO,IAAI3jB,MAAMyjB,EAAWzY,MAAM0B,SAAU+W,EAAWzY,OAExE,MAAMyY,EAAWzY,KACrB,MAoBJ,SAASmY,EAAOvU,EAAKgV,EAAKC,WAAYC,EAAiB,CAAC,MACpDF,EAAGG,iBAAiB,WAAW,SAASra,EAASsa,GAC7C,IAAKA,IAAOA,EAAGvH,KACX,OAEJ,IAhBR,SAAyBqH,EAAgBG,GACrC,IAAK,MAAMC,KAAiBJ,EAAgB,CACxC,GAAIG,IAAWC,GAAmC,MAAlBA,EAC5B,OAAO,EAEX,GAAIA,aAAyBC,QAAUD,EAAcE,KAAKH,GACtD,OAAO,CAEf,CACA,OAAO,CACX,CAMaI,CAAgBP,EAAgBE,EAAGC,QAEpC,YADA5iB,QAAQI,KAAK,mBAAmBuiB,EAAGC,6BAGvC,MAAM,GAAExE,EAAE,KAAEvV,EAAI,KAAEb,GAASvK,OAAO6kB,OAAO,CAAEta,KAAM,IAAM2a,EAAGvH,MACpD9G,GAAgBqO,EAAGvH,KAAK9G,cAAgB,IAAIpB,IAAI+P,GACtD,IAAIC,EACJ,IACI,MAAMC,EAASnb,EAAK0S,MAAM,GAAI,GAAG0I,QAAO,CAAC7V,EAAKuT,IAASvT,EAAIuT,IAAOvT,GAC5D8V,EAAWrb,EAAKob,QAAO,CAAC7V,EAAKuT,IAASvT,EAAIuT,IAAOvT,GACvD,OAAQ1E,GACJ,IAAK,MAEGqa,EAAcG,EAElB,MACJ,IAAK,MAEGF,EAAOnb,EAAK0S,OAAO,GAAG,IAAMuI,EAAcN,EAAGvH,KAAKzR,OAClDuZ,GAAc,EAElB,MACJ,IAAK,QAEGA,EAAcG,EAASvb,MAAMqb,EAAQ7O,GAEzC,MACJ,IAAK,YAGG4O,EA6KxB,SAAe3V,GACX,OAAO9P,OAAO6kB,OAAO/U,EAAK,CAAE,CAACwT,IAAc,GAC/C,CA/KsCuC,CADA,IAAID,KAAY/O,IAGlC,MACJ,IAAK,WACD,CACI,MAAM,MAAEqN,EAAK,MAAEC,GAAU,IAAIC,eAC7BC,EAAOvU,EAAKqU,GACZsB,EAkKxB,SAAkB3V,EAAKgW,GAEnB,OADAC,EAAczlB,IAAIwP,EAAKgW,GAChBhW,CACX,CArKsCkW,CAAS9B,EAAO,CAACA,GACnC,CACA,MACJ,IAAK,UAEGuB,OAAchmB,EAElB,MACJ,QACI,OAEZ,CACA,MAAOyM,GACHuZ,EAAc,CAAEvZ,QAAO,CAACyX,GAAc,EAC1C,CACA9jB,QAAQC,QAAQ2lB,GACX7D,OAAO1V,IACD,CAAEA,QAAO,CAACyX,GAAc,MAE9BjC,MAAM+D,IACP,MAAOQ,EAAWC,GAAiBC,EAAYV,GAC/CX,EAAGsB,YAAYpmB,OAAO6kB,OAAO7kB,OAAO6kB,OAAO,CAAC,EAAGoB,GAAY,CAAEtF,OAAOuF,GACvD,YAAT9a,IAEA0Z,EAAGuB,oBAAoB,UAAWzb,GAClC0b,EAAcxB,GACVpB,KAAa5T,GAAiC,mBAAnBA,EAAI4T,IAC/B5T,EAAI4T,KAEZ,IAEC9B,OAAOnZ,IAER,MAAOwd,EAAWC,GAAiBC,EAAY,CAC3Cja,MAAO,IAAI+C,UAAU,+BACrB,CAAC0U,GAAc,IAEnBmB,EAAGsB,YAAYpmB,OAAO6kB,OAAO7kB,OAAO6kB,OAAO,CAAC,EAAGoB,GAAY,CAAEtF,OAAOuF,EAAc,GAE1F,IACIpB,EAAGN,OACHM,EAAGN,OAEX,CAIA,SAAS8B,EAAcC,IAHvB,SAAuBA,GACnB,MAAqC,gBAA9BA,EAASzY,YAAY7D,IAChC,EAEQuc,CAAcD,IACdA,EAASE,OACjB,CAIA,SAASC,EAAqBC,GAC1B,GAAIA,EACA,MAAM,IAAIzlB,MAAM,6CAExB,CACA,SAAS0lB,EAAgB9B,GACrB,OAAO+B,EAAuB/B,EAAI,CAC9B1Z,KAAM,YACPsW,MAAK,KACJ4E,EAAcxB,EAAG,GAEzB,CACA,MAAMgC,EAAe,IAAIC,QACnBC,EAAkB,yBAA0BjC,YAC9C,IAAIkC,sBAAsBnC,IACtB,MAAMoC,GAAYJ,EAAa1mB,IAAI0kB,IAAO,GAAK,EAC/CgC,EAAaxmB,IAAIwkB,EAAIoC,GACJ,IAAbA,GACAN,EAAgB9B,EACpB,IAcR,SAASL,EAAYK,EAAIva,EAAO,GAAIma,EAAS,WAAc,GACvD,IAAIyC,GAAkB,EACtB,MAAMtB,EAAQ,IAAIuB,MAAM1C,EAAQ,CAC5B,GAAAtkB,CAAIinB,EAAShE,GAET,GADAqD,EAAqBS,GACjB9D,IAASI,EACT,MAAO,MAXvB,SAAyBoC,GACjBmB,GACAA,EAAgB9V,WAAW2U,EAEnC,CAQoByB,CAAgBzB,GAChBe,EAAgB9B,GAChBqC,GAAkB,CAAI,EAG9B,GAAa,SAAT9D,EAAiB,CACjB,GAAoB,IAAhB9Y,EAAKnF,OACL,MAAO,CAAEsc,KAAM,IAAMmE,GAEzB,MAAM9O,EAAI8P,EAAuB/B,EAAI,CACjC1Z,KAAM,MACNb,KAAMA,EAAKkL,KAAK8R,GAAMA,EAAEtjB,eACzByd,KAAK8D,GACR,OAAOzO,EAAE2K,KAAKjf,KAAKsU,EACvB,CACA,OAAO0N,EAAYK,EAAI,IAAIva,EAAM8Y,GACrC,EACA,GAAA/iB,CAAI+mB,EAAShE,EAAMuC,GACfc,EAAqBS,GAGrB,MAAOjb,EAAOga,GAAiBC,EAAYP,GAC3C,OAAOiB,EAAuB/B,EAAI,CAC9B1Z,KAAM,MACNb,KAAM,IAAIA,EAAM8Y,GAAM5N,KAAK8R,GAAMA,EAAEtjB,aACnCiI,SACDga,GAAexE,KAAK8D,EAC3B,EACA,KAAAnb,CAAMgd,EAASG,EAAUC,GACrBf,EAAqBS,GACrB,MAAMO,EAAOnd,EAAKA,EAAKnF,OAAS,GAChC,GAAIsiB,IAASlE,EACT,OAAOqD,EAAuB/B,EAAI,CAC9B1Z,KAAM,aACPsW,KAAK8D,GAGZ,GAAa,SAATkC,EACA,OAAOjD,EAAYK,EAAIva,EAAK0S,MAAM,GAAI,IAE1C,MAAOpG,EAAcqP,GAAiByB,EAAiBF,GACvD,OAAOZ,EAAuB/B,EAAI,CAC9B1Z,KAAM,QACNb,KAAMA,EAAKkL,KAAK8R,GAAMA,EAAEtjB,aACxB4S,gBACDqP,GAAexE,KAAK8D,EAC3B,EACA,SAAAoC,CAAUP,EAASI,GACff,EAAqBS,GACrB,MAAOtQ,EAAcqP,GAAiByB,EAAiBF,GACvD,OAAOZ,EAAuB/B,EAAI,CAC9B1Z,KAAM,YACNb,KAAMA,EAAKkL,KAAK8R,GAAMA,EAAEtjB,aACxB4S,gBACDqP,GAAexE,KAAK8D,EAC3B,IAGJ,OA7EJ,SAAuBK,EAAOf,GAC1B,MAAMoC,GAAYJ,EAAa1mB,IAAI0kB,IAAO,GAAK,EAC/CgC,EAAaxmB,IAAIwkB,EAAIoC,GACjBF,GACAA,EAAgB/V,SAAS4U,EAAOf,EAAIe,EAE5C,CAsEIgC,CAAchC,EAAOf,GACde,CACX,CAIA,SAAS8B,EAAiB9Q,GACtB,MAAMiR,EAAYjR,EAAapB,IAAI0Q,GACnC,MAAO,CAAC2B,EAAUrS,KAAK2B,GAAMA,EAAE,MALnB2Q,EAK+BD,EAAUrS,KAAK2B,GAAMA,EAAE,KAJ3D1I,MAAMxH,UAAUyN,OAAOtK,MAAM,GAAI0d,KAD5C,IAAgBA,CAMhB,CACA,MAAMhC,EAAgB,IAAIgB,QAe1B,SAASZ,EAAYja,GACjB,IAAK,MAAOjC,EAAM+d,KAAYlE,EAC1B,GAAIkE,EAAQhE,UAAU9X,GAAQ,CAC1B,MAAO+b,EAAiB/B,GAAiB8B,EAAQ/D,UAAU/X,GAC3D,MAAO,CACH,CACId,KAAM,UACNnB,OACAiC,MAAO+b,GAEX/B,EAER,CAEJ,MAAO,CACH,CACI9a,KAAM,MACNc,SAEJ6Z,EAAc3lB,IAAI8L,IAAU,GAEpC,CACA,SAASsZ,EAActZ,GACnB,OAAQA,EAAMd,MACV,IAAK,UACD,OAAO0Y,EAAiB1jB,IAAI8L,EAAMjC,MAAMqa,YAAYpY,EAAMA,OAC9D,IAAK,MACD,OAAOA,EAAMA,MAEzB,CACA,SAAS2a,EAAuB/B,EAAIoD,EAAKpC,GACrC,OAAO,IAAIjmB,SAASC,IAChB,MAAM6gB,EAeH,IAAIjS,MAAM,GACZyZ,KAAK,GACL1S,KAAI,IAAM7N,KAAKwgB,MAAMxgB,KAAKygB,SAAWC,OAAOC,kBAAkBtkB,SAAS,MACvEyR,KAAK,KAjBNoP,EAAGG,iBAAiB,WAAW,SAASuD,EAAEtD,GACjCA,EAAGvH,MAASuH,EAAGvH,KAAKgD,IAAMuE,EAAGvH,KAAKgD,KAAOA,IAG9CmE,EAAGuB,oBAAoB,UAAWmC,GAClC1oB,EAAQolB,EAAGvH,MACf,IACImH,EAAGN,OACHM,EAAGN,QAEPM,EAAGsB,YAAYpmB,OAAO6kB,OAAO,CAAElE,MAAMuH,GAAMpC,EAAU,GAE7D,cC5UAzB,ECJmB,CAAChlB,IAChB,IAAIopB,EAAU,OACd,OAAO,MAOH,WAAA3a,CAAY4a,EAAQC,EAAYlI,GAC5BvV,KAAK0d,OAAS,KACd1d,KAAK2d,QAAS,EACdJ,EAAUC,GAAU,MAAO,GAC3Bxd,KAAK4d,cAAe,EAIpB5d,KAAK6d,OAAS,GACd,IAAIC,EAAS,CAAC,EACVvI,IAAiBuI,EAAS,IAAIA,EAAQvI,oBAC1C,MAAMwI,EAAQC,IACVF,EAAS,IAAIA,EAAQL,WAAY,CAAEpe,EAAM4e,IAAUD,GAAgB,gBAAT3e,EAA0B2e,EAAMC,EAAI5e,GAC9FlL,EAAmB2pB,GAAQtH,MAAK0H,IAAIle,KAAKme,aAAaD,EAAC,GAAG,EAE9D,GAAIT,EAAY,CACZ,MAAMW,EAAKX,EAAW,cAAe,IACjCW,GAAIA,EAAG5H,KAAM4H,EAAG5H,KAAKuH,GACpBA,EAAKK,EACd,MAAML,GACV,CAEA,YAAAI,CAAaE,GACTre,KAAK0d,OAASW,EACdre,KAAK2d,QAAS,EACXJ,GAASA,GAChB,CAEA,WAAAe,GACIte,KAAK0d,OAAOY,cACZte,KAAK6d,OAAS,GACd7d,KAAK4d,cAAe,CACxB,CAcA,OAAAW,CAAQC,EAASC,EAAUC,EAAQ,KAAMC,EAAO,KAAMC,OAAQrqB,EAAWsqB,GAAa,EAAOC,GAAY,EAAOC,EAAO,GACnH,IAAI/e,KAAK2d,SAAW3d,KAAK4d,aAAc,KAAM,qBAC7C,MAAMoB,EAAWhf,KAAK0d,OAAOuB,WAAWR,EAASvkB,OAAS,EAAGskB,EAAQtkB,OAAmB,MAAXwkB,GAAmBG,EAAsB,MAAVF,GAAkBG,GAC9H9e,KAAK0d,OAAO7iB,QAAQzF,IAAIopB,EAASQ,EAASE,YAAY,GAEtD,MAAMC,EAAK,IAAI5jB,aAAa,IAAIkjB,IAChC,GAAW,IAARM,EAAW,CACU,iBAAVA,IAAoBA,EAAQ,CAACA,EAAOA,EAAOA,IACrD,IAAK,IAAIrlB,EAAI,EAAG4jB,EAAI6B,EAAGjlB,OAAQR,EAAI4jB,EAAG5jB,GAAG,EACrCylB,EAAGzlB,IAAMqlB,EAAM,GACfI,EAAGzlB,EAAE,IAAMqlB,EAAM,GACjBI,EAAGzlB,EAAE,IAAMqlB,EAAM,EAEzB,CAEA/e,KAAK0d,OAAO5iB,QAAQ1F,IAAI+pB,EAAIH,EAASI,eAAe,GACtC,MAAXV,GAAmBG,GAAY7e,KAAK0d,OAAO5iB,QAAQ1F,IAAIspB,EAASM,EAASK,aAAa,GAC5E,MAAVV,GAAkBG,GAAW9e,KAAK0d,OAAO5iB,QAAQ1F,IAAIupB,EAAQK,EAASM,SAAS,GAClF,MAAMC,EAAavf,KAAK0d,OAAOa,UAG/B,GAAkB,IAAfgB,EAEC,OADAloB,QAAQC,IAAI,sBAAuBioB,GAC5B,KAEX,MAAMrb,EAAM,CACRsb,OAAQR,EAASQ,OACjBZ,QAASA,EACTH,SAAUA,EACVC,QAASA,GAAW,KACpBF,QAASA,GAAW,KACpBG,OAAQA,GAAU,MAGtB,OADA3e,KAAK6d,OAAOja,KAAKM,GACVA,CACX,CASA,UAAA+a,CAAWQ,EAAaC,EAAYhB,EAASC,GACzC,OAAO3e,KAAK0d,OAAOuB,WAAWQ,EAAaC,EAAYhB,EAASC,EACpE,CAYA,aAAAgB,CAAcC,EAAcC,EAAaC,GAAe,GACpD,IAAI9f,KAAK2d,SAAW3d,KAAK4d,aAAc,KAAM,qBAC7C,GAAG5d,KAAK6d,OAAO3jB,OAAS,EAAG,KAAM,mBAIjC,OAHA0lB,EAAe,IAAK5f,KAAK+f,yBAA0BH,GACnDC,EAAc,IAAK7f,KAAKggB,wBAAyBH,GACjD7f,KAAK0d,OAAOiC,cAAcC,EAAcC,GACpCC,EACG9f,KAAKigB,WADa,EAE7B,CAEA,QAAAA,GACI,MAAMC,EAAY,GAClB,IAAIlgB,KAAK2d,SAAW3d,KAAK4d,aAAc,KAAM,qBAC7C,MAAMuC,EAASngB,KAAK0d,OAAOuC,WACrBG,EAAQ,CACVC,MAAOF,EAAOE,MACdC,OAAQH,EAAOG,OACfC,WAAYJ,EAAOI,WACnBC,UAAWL,EAAOK,UAIlBC,cAAeN,EAAOM,eAG1B,IAAK,MAAM,OAACjB,EAAM,QAAEZ,EAAO,SAAEH,EAAQ,QAAEC,EAAO,OAAEC,KAAW3e,KAAK6d,OAAQ,CACpE,MAAM3Z,EAAMlE,KAAK0gB,YAAYlB,GACvBmB,EAASzc,EAAI0c,eACbC,EAAQ,IAAIvlB,YAAY0E,KAAK0d,OAAO7iB,QAAQpC,SAASyL,EAAIgb,YAAc,EAAGhb,EAAIgb,YAAc,EAAIhb,EAAI4c,gBACpGC,EAAa,IAAIzlB,YAAY0E,KAAK0d,OAAO7iB,QAAQpC,SAASyL,EAAI8c,oBAAsB,EAAG9c,EAAI8c,oBAAsB,EAAIL,IAErHM,EAAU,IAAI1lB,aAAayE,KAAK0d,OAAO5iB,QAAQrC,SAASyL,EAAIob,SAAW,EAAGpb,EAAIob,SAAW,EAAa,EAATqB,IAE7FO,EAAY,GAClB,IAAK,IAAIxnB,EAAI,EAAGoe,EAAE5T,EAAIgd,UAAUpd,OAAQpK,EAAIoe,EAAGpe,IAC3CwnB,EAAUtd,KAAKM,EAAIgd,UAAUhsB,IAAIwE,IAErCsG,KAAK0d,OAAOyD,gBAAgBjd,GAC5BA,EAAIgd,UAAUE,SAEd,MAAMC,EAAS,CAAC,EAChBA,EAAO5C,SAAW,IAAIljB,aAAsB,EAATolB,GACnCU,EAAOC,QAAUL,EACbvC,IACA2C,EAAO3C,QAAU,IAAInjB,aAAsB,EAATolB,IAElCU,EAAO1C,OADPA,EACgB,IAAIpjB,aAAsB,EAATolB,GAChBU,EAAOC,QAE5B,IAAK,IAAI5nB,EAAI,EAAG4jB,EAAIqD,EAAQjnB,EAAI4jB,EAAG5jB,IAAK,CACpC,MAAM6nB,EAAWR,EAAWrnB,GAC5B2nB,EAAO5C,SAAS,EAAI/kB,EAAI,GAAK+kB,EAAS,EAAI8C,EAAW,GACrDF,EAAO5C,SAAS,EAAI/kB,EAAI,GAAK+kB,EAAS,EAAI8C,EAAW,GACrDF,EAAO5C,SAAS,EAAI/kB,EAAI,GAAK+kB,EAAS,EAAI8C,EAAW,GACjDF,EAAO3C,SAAWA,IAClB2C,EAAO3C,QAAQ,EAAIhlB,EAAI,GAAKglB,EAAQ,EAAI6C,EAAW,GACnDF,EAAO3C,QAAQ,EAAIhlB,EAAI,GAAKglB,EAAQ,EAAI6C,EAAW,GACnDF,EAAO3C,QAAQ,EAAIhlB,EAAI,GAAKglB,EAAQ,EAAI6C,EAAW,IAEnDF,EAAO1C,QAAUA,IACjB0C,EAAO1C,OAAO,EAAIjlB,EAAI,GAAKilB,EAAO,EAAI4C,EAAW,GACjDF,EAAO1C,OAAO,EAAIjlB,EAAI,GAAKilB,EAAO,EAAI4C,EAAW,GAEzD,CACArB,EAAUtc,KAAK,CACXid,MAAOA,EACPQ,OAAQA,EACRG,KAAM5C,EACNa,YAAakB,EACbI,WAAYA,EACZG,UAAWA,GAEnB,CACA,MAAO,IAAId,EAAOvC,OAAQqC,EAE9B,CAEA,mBAAAH,GACI,MAAO,CACH0B,YAAY,EACZC,kBAAmB,EACnBC,aAAc,EACdC,QAAS,EACTC,cAAe,EACfC,sBAAuB,EACvBC,iBAAkB,EAClBC,gBAAiB,oBACjBC,mBAAoB,EACpBC,kBAAmB,GACnBC,iBAAiB,EAEzB,CAEA,kBAAAnC,GACI,MAAO,CACHoC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,aAAc,EACdC,QAAS,EACTC,WAAY,EACZC,cAAc,EACdC,oBAAoB,EACpBnC,cAAe,EAEvB,CAEA,kBAAAoC,CAAmBC,GACf9iB,KAAK0d,OAAOmF,mBAAmBC,EACnC,CAMA,WAAApC,CAAYlB,GACR,OAAOxf,KAAK0d,OAAOgD,YAAYlB,EACnC,CAMA,eAAA2B,CAAgB1O,GACZzS,KAAK0d,OAAOyD,gBAAgB1O,EAChC,CAEA,YAAAsQ,GACI/iB,KAAK4d,cAAe,EACpB5d,KAAK0d,OAAOqF,eACZ/iB,KAAK6d,OAAS,GACd7d,KAAK0d,OAAOsF,aAChB,EAEJ,EDpPGC,OAAI", "sources": ["webpack://XAtlas/./source/web/build/xatlas.js", "webpack://XAtlas/webpack/bootstrap", "webpack://XAtlas/webpack/runtime/compat get default export", "webpack://XAtlas/webpack/runtime/define property getters", "webpack://XAtlas/webpack/runtime/hasOwnProperty shorthand", "webpack://XAtlas/./node_modules/comlink/dist/esm/comlink.mjs", "webpack://XAtlas/./source/web/index.js", "webpack://XAtlas/./source/web/api.mjs"], "sourcesContent": ["\nvar createXAtlasModule = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(createXAtlasModule) {\n  createXAtlasModule = createXAtlasModule || {};\n\nvar Module=typeof createXAtlasModule!==\"undefined\"?createXAtlasModule:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_sbrk\")){Object.defineProperty(Module[\"ready\"],\"_sbrk\",{configurable:true,get:function(){abort(\"You are getting _sbrk on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_sbrk\",{configurable:true,set:function(){abort(\"You are setting _sbrk on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_stackSave\")){Object.defineProperty(Module[\"ready\"],\"_stackSave\",{configurable:true,get:function(){abort(\"You are getting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_stackSave\",{configurable:true,set:function(){abort(\"You are setting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_stackRestore\")){Object.defineProperty(Module[\"ready\"],\"_stackRestore\",{configurable:true,get:function(){abort(\"You are getting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_stackRestore\",{configurable:true,set:function(){abort(\"You are setting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_stackAlloc\")){Object.defineProperty(Module[\"ready\"],\"_stackAlloc\",{configurable:true,get:function(){abort(\"You are getting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_stackAlloc\",{configurable:true,set:function(){abort(\"You are setting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"___data_end\")){Object.defineProperty(Module[\"ready\"],\"___data_end\",{configurable:true,get:function(){abort(\"You are getting ___data_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"___data_end\",{configurable:true,set:function(){abort(\"You are setting ___data_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"___wasm_call_ctors\")){Object.defineProperty(Module[\"ready\"],\"___wasm_call_ctors\",{configurable:true,get:function(){abort(\"You are getting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"___wasm_call_ctors\",{configurable:true,set:function(){abort(\"You are setting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_fflush\")){Object.defineProperty(Module[\"ready\"],\"_fflush\",{configurable:true,get:function(){abort(\"You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_fflush\",{configurable:true,set:function(){abort(\"You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"___errno_location\")){Object.defineProperty(Module[\"ready\"],\"___errno_location\",{configurable:true,get:function(){abort(\"You are getting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"___errno_location\",{configurable:true,set:function(){abort(\"You are setting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_malloc\")){Object.defineProperty(Module[\"ready\"],\"_malloc\",{configurable:true,get:function(){abort(\"You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_malloc\",{configurable:true,set:function(){abort(\"You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_free\")){Object.defineProperty(Module[\"ready\"],\"_free\",{configurable:true,get:function(){abort(\"You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_free\",{configurable:true,set:function(){abort(\"You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"_setThrew\")){Object.defineProperty(Module[\"ready\"],\"_setThrew\",{configurable:true,get:function(){abort(\"You are getting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"_setThrew\",{configurable:true,set:function(){abort(\"You are setting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}if(!Object.getOwnPropertyDescriptor(Module[\"ready\"],\"onRuntimeInitialized\")){Object.defineProperty(Module[\"ready\"],\"onRuntimeInitialized\",{configurable:true,get:function(){abort(\"You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}});Object.defineProperty(Module[\"ready\"],\"onRuntimeInitialized\",{configurable:true,set:function(){abort(\"You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js\")}})}var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window===\"object\";ENVIRONMENT_IS_WORKER=typeof importScripts===\"function\";ENVIRONMENT_IS_NODE=typeof process===\"object\"&&typeof process.versions===\"object\"&&typeof process.versions.node===\"string\";ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(Module[\"ENVIRONMENT\"]){throw new Error(\"Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)\")}var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}if(!(typeof window===\"object\"||typeof importScripts===\"function\"))throw new Error(\"not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)\");{read_=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{throw new Error(\"environment detection error\")}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(!Object.getOwnPropertyDescriptor(Module,\"arguments\"))Object.defineProperty(Module,\"arguments\",{configurable:true,get:function(){abort(\"Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(!Object.getOwnPropertyDescriptor(Module,\"thisProgram\"))Object.defineProperty(Module,\"thisProgram\",{configurable:true,get:function(){abort(\"Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(Module[\"quit\"])quit_=Module[\"quit\"];if(!Object.getOwnPropertyDescriptor(Module,\"quit\"))Object.defineProperty(Module,\"quit\",{configurable:true,get:function(){abort(\"Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});assert(typeof Module[\"memoryInitializerPrefixURL\"]===\"undefined\",\"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead\");assert(typeof Module[\"pthreadMainPrefixURL\"]===\"undefined\",\"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead\");assert(typeof Module[\"cdInitializerPrefixURL\"]===\"undefined\",\"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead\");assert(typeof Module[\"filePackagePrefixURL\"]===\"undefined\",\"Module.filePackagePrefixURL option was removed, use Module.locateFile instead\");assert(typeof Module[\"read\"]===\"undefined\",\"Module.read option was removed (modify read_ in JS)\");assert(typeof Module[\"readAsync\"]===\"undefined\",\"Module.readAsync option was removed (modify readAsync in JS)\");assert(typeof Module[\"readBinary\"]===\"undefined\",\"Module.readBinary option was removed (modify readBinary in JS)\");assert(typeof Module[\"setWindowTitle\"]===\"undefined\",\"Module.setWindowTitle option was removed (modify setWindowTitle in JS)\");assert(typeof Module[\"TOTAL_MEMORY\"]===\"undefined\",\"Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY\");if(!Object.getOwnPropertyDescriptor(Module,\"read\"))Object.defineProperty(Module,\"read\",{configurable:true,get:function(){abort(\"Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(!Object.getOwnPropertyDescriptor(Module,\"readAsync\"))Object.defineProperty(Module,\"readAsync\",{configurable:true,get:function(){abort(\"Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(!Object.getOwnPropertyDescriptor(Module,\"readBinary\"))Object.defineProperty(Module,\"readBinary\",{configurable:true,get:function(){abort(\"Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(!Object.getOwnPropertyDescriptor(Module,\"setWindowTitle\"))Object.defineProperty(Module,\"setWindowTitle\",{configurable:true,get:function(){abort(\"Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});var STACK_ALIGN=16;function warnOnce(text){if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}}function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function===\"function\"){var typeNames={\"i\":\"i32\",\"j\":\"i64\",\"f\":\"f32\",\"d\":\"f64\"};var type={parameters:[],results:sig[0]==\"v\"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={\"i\":127,\"j\":126,\"f\":125,\"d\":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet==\"v\"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{\"e\":{\"f\":func}});var wrappedFunc=instance.exports[\"f\"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function addFunctionWasm(func,sig){var table=wasmTable;if(!functionsInTableMap){functionsInTableMap=new WeakMap;for(var i=0;i<table.length;i++){var item=table.get(i);if(item){functionsInTableMap.set(item,i)}}}if(functionsInTableMap.has(func)){return functionsInTableMap.get(func)}var ret;if(freeTableIndexes.length){ret=freeTableIndexes.pop()}else{ret=table.length;try{table.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw\"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.\"}}try{table.set(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}assert(typeof sig!==\"undefined\",\"Missing signature argument to addFunction\");var wrapped=convertJsFunctionToWasm(func,sig);table.set(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}function removeFunctionWasm(index){functionsInTableMap.delete(wasmTable.get(index));freeTableIndexes.push(index)}var tempRet0=0;var setTempRet0=function(value){tempRet0=value};var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];if(!Object.getOwnPropertyDescriptor(Module,\"wasmBinary\"))Object.defineProperty(Module,\"wasmBinary\",{configurable:true,get:function(){abort(\"Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});var noExitRuntime;if(Module[\"noExitRuntime\"])noExitRuntime=Module[\"noExitRuntime\"];if(!Object.getOwnPropertyDescriptor(Module,\"noExitRuntime\"))Object.defineProperty(Module,\"noExitRuntime\",{configurable:true,get:function(){abort(\"Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var wasmTable;var ABORT=false;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}function getCFunc(ident){var func=Module[\"_\"+ident];assert(func,\"Cannot call unknown function \"+ident+\", make sure it is exported\");return func}function ccall(ident,returnType,argTypes,args,opts){var toC={\"string\":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},\"array\":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType===\"string\")return UTF8ToString(ret);if(returnType===\"boolean\")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;assert(returnType!==\"array\",'Return type should not be \"array\".');if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);ret=convertReturnValue(ret);if(stack!==0)stackRestore(stack);return ret}var ALLOC_STACK=1;var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{if((u0&248)!=240)warnOnce(\"Invalid UTF-8 leading byte 0x\"+u0.toString(16)+\" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!\");u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;if(u>=2097152)warnOnce(\"Invalid Unicode code point 0x\"+u.toString(16)+\" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF).\");heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){assert(typeof maxBytesToWrite==\"number\",\"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!\");return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var UTF16Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf-16le\"):undefined;function UTF16ToString(ptr,maxBytesToRead){assert(ptr%2==0,\"Pointer passed to UTF16ToString must be aligned to two bytes!\");var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder){return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr))}else{var i=0;var str=\"\";while(1){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0||i==maxBytesToRead/2)return str;++i;str+=String.fromCharCode(codeUnit)}}}function stringToUTF16(str,outPtr,maxBytesToWrite){assert(outPtr%2==0,\"Pointer passed to stringToUTF16 must be aligned to two bytes!\");assert(typeof maxBytesToWrite==\"number\",\"stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!\");if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr}function lengthBytesUTF16(str){return str.length*2}function UTF32ToString(ptr,maxBytesToRead){assert(ptr%4==0,\"Pointer passed to UTF32ToString must be aligned to four bytes!\");var i=0;var str=\"\";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str}function stringToUTF32(str,outPtr,maxBytesToWrite){assert(outPtr%4==0,\"Pointer passed to stringToUTF32 must be aligned to four bytes!\");assert(typeof maxBytesToWrite==\"number\",\"stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!\");if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr}function lengthBytesUTF32(str){var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len}function writeArrayToMemory(array,buffer){assert(array.length>=0,\"writeArrayToMemory array must have a length (should be an array or typed array)\");HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){assert(str.charCodeAt(i)===str.charCodeAt(i)&255);HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}var WASM_PAGE_SIZE=65536;function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var STACK_BASE=5256992,STACK_MAX=14112;assert(STACK_BASE%16===0,\"stack must start aligned\");var TOTAL_STACK=5242880;if(Module[\"TOTAL_STACK\"])assert(TOTAL_STACK===Module[\"TOTAL_STACK\"],\"the stack size can no longer be determined at runtime\");var INITIAL_INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;if(!Object.getOwnPropertyDescriptor(Module,\"INITIAL_MEMORY\"))Object.defineProperty(Module,\"INITIAL_MEMORY\",{configurable:true,get:function(){abort(\"Module.INITIAL_MEMORY has been replaced with plain INITIAL_INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)\")}});assert(INITIAL_INITIAL_MEMORY>=TOTAL_STACK,\"INITIAL_MEMORY should be larger than TOTAL_STACK, was \"+INITIAL_INITIAL_MEMORY+\"! (TOTAL_STACK=\"+TOTAL_STACK+\")\");assert(typeof Int32Array!==\"undefined\"&&typeof Float64Array!==\"undefined\"&&Int32Array.prototype.subarray!==undefined&&Int32Array.prototype.set!==undefined,\"JS engine does not provide full typed array support\");if(Module[\"wasmMemory\"]){wasmMemory=Module[\"wasmMemory\"]}else{wasmMemory=new WebAssembly.Memory({\"initial\":INITIAL_INITIAL_MEMORY/WASM_PAGE_SIZE,\"maximum\":2147483648/WASM_PAGE_SIZE})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_INITIAL_MEMORY=buffer.byteLength;assert(INITIAL_INITIAL_MEMORY%WASM_PAGE_SIZE===0);assert(65536%WASM_PAGE_SIZE===0);updateGlobalBufferAndViews(buffer);function writeStackCookie(){assert((STACK_MAX&3)==0);HEAPU32[(STACK_MAX>>2)+1]=34821223;HEAPU32[(STACK_MAX>>2)+2]=2310721022;HEAP32[0]=1668509029}function checkStackCookie(){if(ABORT)return;var cookie1=HEAPU32[(STACK_MAX>>2)+1];var cookie2=HEAPU32[(STACK_MAX>>2)+2];if(cookie1!=34821223||cookie2!=2310721022){abort(\"Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x\"+cookie2.toString(16)+\" \"+cookie1.toString(16))}if(HEAP32[0]!==1668509029)abort(\"Runtime error: The application has corrupted its heap memory area (address zero)!\")}(function(){var h16=new Int16Array(1);var h8=new Int8Array(h16.buffer);h16[0]=25459;if(h8[0]!==115||h8[1]!==99)throw\"Runtime error: expected the system to be little-endian!\"})();var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){checkStackCookie();assert(!runtimeInitialized);runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){checkStackCookie();callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){checkStackCookie();runtimeExited=true}function postRun(){checkStackCookie();if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}assert(Math.imul,\"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill\");assert(Math.fround,\"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill\");assert(Math.clz32,\"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill\");assert(Math.trunc,\"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill\");var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;var runDependencyTracking={};function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(id){assert(!runDependencyTracking[id]);runDependencyTracking[id]=1;if(runDependencyWatcher===null&&typeof setInterval!==\"undefined\"){runDependencyWatcher=setInterval(function(){if(ABORT){clearInterval(runDependencyWatcher);runDependencyWatcher=null;return}var shown=false;for(var dep in runDependencyTracking){if(!shown){shown=true;err(\"still waiting on run dependencies:\")}err(\"dependency: \"+dep)}if(shown){err(\"(end of list)\")}},1e4)}}else{err(\"warning: run dependency added without ID\")}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(id){assert(runDependencyTracking[id]);delete runDependencyTracking[id]}else{err(\"warning: run dependency removed without ID\")}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what+=\"\";err(what);ABORT=true;EXITSTATUS=1;var output=\"abort(\"+what+\") at \"+stackTrace();what=output;var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var FS={error:function(){abort(\"Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1\")},init:function(){FS.error()},createDataFile:function(){FS.error()},createPreloadedFile:function(){FS.error()},createLazyFile:function(){FS.error()},open:function(){FS.error()},mkdev:function(){FS.error()},registerDevice:function(){FS.error()},analyzePath:function(){FS.error()},loadFilesFromDB:function(){FS.error()},ErrnoError:function ErrnoError(){FS.error()}};Module[\"FS_createDataFile\"]=FS.createDataFile;Module[\"FS_createPreloadedFile\"]=FS.createPreloadedFile;function hasPrefix(str,prefix){return String.prototype.startsWith?str.startsWith(prefix):str.indexOf(prefix)===0}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return hasPrefix(filename,dataURIPrefix)}var fileURIPrefix=\"file://\";function createExportWrapper(name,fixedasm){return function(){var displayName=name;var asm=fixedasm;if(!fixedasm){asm=Module[\"asm\"]}assert(runtimeInitialized,\"native function `\"+displayName+\"` called before runtime initialization\");assert(!runtimeExited,\"native function `\"+displayName+\"` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)\");if(!asm[name]){assert(asm[name],\"exported native function `\"+displayName+\"` not found\")}return asm[name].apply(null,arguments)}}var wasmBinaryFile=\"xatlas.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(){try{if(wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(wasmBinaryFile)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary()})}return Promise.resolve().then(getBinary)}function createWasm(){var info={\"env\":asmLibraryArg,\"wasi_snapshot_preview1\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmTable=Module[\"asm\"][\"__indirect_function_table\"];assert(wasmTable,\"table not found in wasm exports\");removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");var trueModule=Module;function receiveInstantiatedSource(output){assert(Module===trueModule,\"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?\");trueModule=null;receiveInstance(output[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&typeof fetch===\"function\"){fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiatedSource,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiatedSource)})})}else{return instantiateArrayBuffer(receiveInstantiatedSource)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync();return{}}var tempDouble;var tempI64;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){wasmTable.get(func)()}else{wasmTable.get(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function demangle(func){warnOnce(\"warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling\");return func}function demangleAll(text){var regex=/\\b_Z[\\w\\d_]+/g;return text.replace(regex,function(x){var y=demangle(x);return x===y?x:y+\" [\"+x+\"]\"})}function dynCallLegacy(sig,ptr,args){assert(\"dynCall_\"+sig in Module,\"bad function pointer type - no table for sig '\"+sig+\"'\");if(args&&args.length){assert(args.length===sig.substring(1).replace(/j/g,\"--\").length)}else{assert(sig.length==1)}if(args&&args.length){return Module[\"dynCall_\"+sig].apply(null,[ptr].concat(args))}return Module[\"dynCall_\"+sig].call(null,ptr)}function dynCall(sig,ptr,args){if(sig.indexOf(\"j\")!=-1){return dynCallLegacy(sig,ptr,args)}return wasmTable.get(ptr).apply(null,args)}function jsStackTrace(){var error=new Error;if(!error.stack){try{throw new Error}catch(e){error=e}if(!error.stack){return\"(no stack trace available)\"}}return error.stack.toString()}function stackTrace(){var js=jsStackTrace();if(Module[\"extraStackTrace\"])js+=\"\\n\"+Module[\"extraStackTrace\"]();return demangleAll(js)}var ExceptionInfoAttrs={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function ___cxa_allocate_exception(size){return _malloc(size+ExceptionInfoAttrs.SIZE)+ExceptionInfoAttrs.SIZE}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-ExceptionInfoAttrs.SIZE;this.set_type=function(type){HEAP32[this.ptr+ExceptionInfoAttrs.TYPE_OFFSET>>2]=type};this.get_type=function(){return HEAP32[this.ptr+ExceptionInfoAttrs.TYPE_OFFSET>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+ExceptionInfoAttrs.DESTRUCTOR_OFFSET>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+ExceptionInfoAttrs.DESTRUCTOR_OFFSET>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr+ExceptionInfoAttrs.REFCOUNT_OFFSET>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+ExceptionInfoAttrs.CAUGHT_OFFSET>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+ExceptionInfoAttrs.CAUGHT_OFFSET>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+ExceptionInfoAttrs.RETHROWN_OFFSET>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+ExceptionInfoAttrs.RETHROWN_OFFSET>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr+ExceptionInfoAttrs.REFCOUNT_OFFSET>>2];HEAP32[this.ptr+ExceptionInfoAttrs.REFCOUNT_OFFSET>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr+ExceptionInfoAttrs.REFCOUNT_OFFSET>>2];HEAP32[this.ptr+ExceptionInfoAttrs.REFCOUNT_OFFSET>>2]=prev-1;assert(prev>0);return prev===1}}var exceptionLast=0;function __ZSt18uncaught_exceptionv(){return __ZSt18uncaught_exceptionv.uncaught_exceptions>0}function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;if(!(\"uncaught_exception\"in __ZSt18uncaught_exceptionv)){__ZSt18uncaught_exceptionv.uncaught_exceptions=1}else{__ZSt18uncaught_exceptionv.uncaught_exceptions++}throw ptr+\" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.\"}var structRegistrations={};function runDestructors(destructors){while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}}function simpleReadValueFromPointer(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var char_0=48;var char_9=57;function makeLegalFunctionName(name){if(undefined===name){return\"_unknown\"}name=name.replace(/[^a-zA-Z0-9_]/g,\"$\");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return\"_\"+name}else{return name}}function createNamedFunction(name,body){name=makeLegalFunctionName(name);return new Function(\"body\",\"return function \"+name+\"() {\\n\"+'    \"use strict\";'+\"    return body.apply(this, arguments);\\n\"+\"};\\n\")(body)}function extendError(baseErrorType,errorName){var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+\"\\n\"+stack.replace(/^Error(:[^\\n]*)?\\n/,\"\")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return this.name+\": \"+this.message}};return errorClass}var InternalError=undefined;function throwInternalError(message){throw new InternalError(message)}function whenDependentTypesAreResolved(myTypes,dependentTypes,getTypeConverters){myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError(\"Mismatched type converter count\")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach(function(dt,i){if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(function(){typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}}function __embind_finalize_value_object(structType){var reg=structRegistrations[structType];delete structRegistrations[structType];var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;var fieldRecords=reg.fields;var fieldTypes=fieldRecords.map(function(field){return field.getterReturnType}).concat(fieldRecords.map(function(field){return field.setterArgumentType}));whenDependentTypesAreResolved([structType],fieldTypes,function(fieldTypes){var fields={};fieldRecords.forEach(function(field,i){var fieldName=field.fieldName;var getterReturnType=fieldTypes[i];var getter=field.getter;var getterContext=field.getterContext;var setterArgumentType=fieldTypes[i+fieldRecords.length];var setter=field.setter;var setterContext=field.setterContext;fields[fieldName]={read:function(ptr){return getterReturnType[\"fromWireType\"](getter(getterContext,ptr))},write:function(ptr,o){var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}}});return[{name:reg.name,\"fromWireType\":function(ptr){var rv={};for(var i in fields){rv[i]=fields[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":function(destructors,o){for(var fieldName in fields){if(!(fieldName in o)){throw new TypeError('Missing field:  \"'+fieldName+'\"')}}var ptr=rawConstructor();for(fieldName in fields){fields[fieldName].write(ptr,o[fieldName])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":8,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})}function getShiftFromSize(size){switch(size){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(\"Unknown type size: \"+size)}}function embind_init_charCodes(){var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes}var embind_charCodes=undefined;function readLatin1String(ptr){var ret=\"\";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret}var BindingError=undefined;function throwBindingError(message){throw new BindingError(message)}function registerType(rawType,registeredInstance,options){options=options||{};if(!(\"argPackAdvance\"in registeredInstance)){throw new TypeError(\"registerType registeredInstance requires argPackAdvance\")}var name=registeredInstance.name;if(!rawType){throwBindingError('type \"'+name+'\" must have a positive integer typeid pointer')}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(\"Cannot register type '\"+name+\"' twice\")}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(function(cb){cb()})}}function __embind_register_bool(rawType,name,size,trueValue,falseValue){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(wt){return!!wt},\"toWireType\":function(destructors,o){return o?trueValue:falseValue},\"argPackAdvance\":8,\"readValueFromPointer\":function(pointer){var heap;if(size===1){heap=HEAP8}else if(size===2){heap=HEAP16}else if(size===4){heap=HEAP32}else{throw new TypeError(\"Unknown boolean type size: \"+name)}return this[\"fromWireType\"](heap[pointer>>shift])},destructorFunction:null})}function ClassHandle_isAliasOf(other){if(!(this instanceof ClassHandle)){return false}if(!(other instanceof ClassHandle)){return false}var leftClass=this.$$.ptrType.registeredClass;var left=this.$$.ptr;var rightClass=other.$$.ptrType.registeredClass;var right=other.$$.ptr;while(leftClass.baseClass){left=leftClass.upcast(left);leftClass=leftClass.baseClass}while(rightClass.baseClass){right=rightClass.upcast(right);rightClass=rightClass.baseClass}return leftClass===rightClass&&left===right}function shallowCopyInternalPointer(o){return{count:o.count,deleteScheduled:o.deleteScheduled,preservePointerOnDelete:o.preservePointerOnDelete,ptr:o.ptr,ptrType:o.ptrType,smartPtr:o.smartPtr,smartPtrType:o.smartPtrType}}function throwInstanceAlreadyDeleted(obj){function getInstanceTypeName(handle){return handle.$$.ptrType.registeredClass.name}throwBindingError(getInstanceTypeName(obj)+\" instance already deleted\")}var finalizationGroup=false;function detachFinalizer(handle){}function runDestructor($$){if($$.smartPtr){$$.smartPtrType.rawDestructor($$.smartPtr)}else{$$.ptrType.registeredClass.rawDestructor($$.ptr)}}function releaseClassHandle($$){$$.count.value-=1;var toDelete=0===$$.count.value;if(toDelete){runDestructor($$)}}function attachFinalizer(handle){if(\"undefined\"===typeof FinalizationGroup){attachFinalizer=function(handle){return handle};return handle}finalizationGroup=new FinalizationGroup(function(iter){for(var result=iter.next();!result.done;result=iter.next()){var $$=result.value;if(!$$.ptr){console.warn(\"object already deleted: \"+$$.ptr)}else{releaseClassHandle($$)}}});attachFinalizer=function(handle){finalizationGroup.register(handle,handle.$$,handle.$$);return handle};detachFinalizer=function(handle){finalizationGroup.unregister(handle.$$)};return attachFinalizer(handle)}function ClassHandle_clone(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.preservePointerOnDelete){this.$$.count.value+=1;return this}else{var clone=attachFinalizer(Object.create(Object.getPrototypeOf(this),{$$:{value:shallowCopyInternalPointer(this.$$)}}));clone.$$.count.value+=1;clone.$$.deleteScheduled=false;return clone}}function ClassHandle_delete(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}detachFinalizer(this);releaseClassHandle(this.$$);if(!this.$$.preservePointerOnDelete){this.$$.smartPtr=undefined;this.$$.ptr=undefined}}function ClassHandle_isDeleted(){return!this.$$.ptr}var delayFunction=undefined;var deletionQueue=[];function flushPendingDeletes(){while(deletionQueue.length){var obj=deletionQueue.pop();obj.$$.deleteScheduled=false;obj[\"delete\"]()}}function ClassHandle_deleteLater(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}deletionQueue.push(this);if(deletionQueue.length===1&&delayFunction){delayFunction(flushPendingDeletes)}this.$$.deleteScheduled=true;return this}function init_ClassHandle(){ClassHandle.prototype[\"isAliasOf\"]=ClassHandle_isAliasOf;ClassHandle.prototype[\"clone\"]=ClassHandle_clone;ClassHandle.prototype[\"delete\"]=ClassHandle_delete;ClassHandle.prototype[\"isDeleted\"]=ClassHandle_isDeleted;ClassHandle.prototype[\"deleteLater\"]=ClassHandle_deleteLater}function ClassHandle(){}var registeredPointers={};function ensureOverloadTable(proto,methodName,humanName){if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(\"Function '\"+humanName+\"' called with an invalid number of arguments (\"+arguments.length+\") - expects one of (\"+proto[methodName].overloadTable+\")!\")}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}}function exposePublicSymbol(name,value,numArguments){if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(\"Cannot register public name '\"+name+\"' twice\")}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(\"Cannot register multiple overloads of a function with the same number of arguments (\"+numArguments+\")!\")}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}}function RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast){this.name=name;this.constructor=constructor;this.instancePrototype=instancePrototype;this.rawDestructor=rawDestructor;this.baseClass=baseClass;this.getActualType=getActualType;this.upcast=upcast;this.downcast=downcast;this.pureVirtualFunctions=[]}function upcastPointer(ptr,ptrClass,desiredClass){while(ptrClass!==desiredClass){if(!ptrClass.upcast){throwBindingError(\"Expected null or instance of \"+desiredClass.name+\", got an instance of \"+ptrClass.name)}ptr=ptrClass.upcast(ptr);ptrClass=ptrClass.baseClass}return ptr}function constNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(\"null is not a valid \"+this.name)}return 0}if(!handle.$$){throwBindingError('Cannot pass \"'+_embind_repr(handle)+'\" as a '+this.name)}if(!handle.$$.ptr){throwBindingError(\"Cannot pass deleted object as a pointer of type \"+this.name)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function genericPointerToWireType(destructors,handle){var ptr;if(handle===null){if(this.isReference){throwBindingError(\"null is not a valid \"+this.name)}if(this.isSmartPointer){ptr=this.rawConstructor();if(destructors!==null){destructors.push(this.rawDestructor,ptr)}return ptr}else{return 0}}if(!handle.$$){throwBindingError('Cannot pass \"'+_embind_repr(handle)+'\" as a '+this.name)}if(!handle.$$.ptr){throwBindingError(\"Cannot pass deleted object as a pointer of type \"+this.name)}if(!this.isConst&&handle.$$.ptrType.isConst){throwBindingError(\"Cannot convert argument of type \"+(handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name)+\" to parameter type \"+this.name)}var handleClass=handle.$$.ptrType.registeredClass;ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);if(this.isSmartPointer){if(undefined===handle.$$.smartPtr){throwBindingError(\"Passing raw pointer to smart pointer is illegal\")}switch(this.sharingPolicy){case 0:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{throwBindingError(\"Cannot convert argument of type \"+(handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name)+\" to parameter type \"+this.name)}break;case 1:ptr=handle.$$.smartPtr;break;case 2:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{var clonedHandle=handle[\"clone\"]();ptr=this.rawShare(ptr,__emval_register(function(){clonedHandle[\"delete\"]()}));if(destructors!==null){destructors.push(this.rawDestructor,ptr)}}break;default:throwBindingError(\"Unsupporting sharing policy\")}}return ptr}function nonConstNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(\"null is not a valid \"+this.name)}return 0}if(!handle.$$){throwBindingError('Cannot pass \"'+_embind_repr(handle)+'\" as a '+this.name)}if(!handle.$$.ptr){throwBindingError(\"Cannot pass deleted object as a pointer of type \"+this.name)}if(handle.$$.ptrType.isConst){throwBindingError(\"Cannot convert argument of type \"+handle.$$.ptrType.name+\" to parameter type \"+this.name)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function RegisteredPointer_getPointee(ptr){if(this.rawGetPointee){ptr=this.rawGetPointee(ptr)}return ptr}function RegisteredPointer_destructor(ptr){if(this.rawDestructor){this.rawDestructor(ptr)}}function RegisteredPointer_deleteObject(handle){if(handle!==null){handle[\"delete\"]()}}function downcastPointer(ptr,ptrClass,desiredClass){if(ptrClass===desiredClass){return ptr}if(undefined===desiredClass.baseClass){return null}var rv=downcastPointer(ptr,ptrClass,desiredClass.baseClass);if(rv===null){return null}return desiredClass.downcast(rv)}function getInheritedInstanceCount(){return Object.keys(registeredInstances).length}function getLiveInheritedInstances(){var rv=[];for(var k in registeredInstances){if(registeredInstances.hasOwnProperty(k)){rv.push(registeredInstances[k])}}return rv}function setDelayFunction(fn){delayFunction=fn;if(deletionQueue.length&&delayFunction){delayFunction(flushPendingDeletes)}}function init_embind(){Module[\"getInheritedInstanceCount\"]=getInheritedInstanceCount;Module[\"getLiveInheritedInstances\"]=getLiveInheritedInstances;Module[\"flushPendingDeletes\"]=flushPendingDeletes;Module[\"setDelayFunction\"]=setDelayFunction}var registeredInstances={};function getBasestPointer(class_,ptr){if(ptr===undefined){throwBindingError(\"ptr should not be undefined\")}while(class_.baseClass){ptr=class_.upcast(ptr);class_=class_.baseClass}return ptr}function getInheritedInstance(class_,ptr){ptr=getBasestPointer(class_,ptr);return registeredInstances[ptr]}function makeClassHandle(prototype,record){if(!record.ptrType||!record.ptr){throwInternalError(\"makeClassHandle requires ptr and ptrType\")}var hasSmartPtrType=!!record.smartPtrType;var hasSmartPtr=!!record.smartPtr;if(hasSmartPtrType!==hasSmartPtr){throwInternalError(\"Both smartPtrType and smartPtr must be specified\")}record.count={value:1};return attachFinalizer(Object.create(prototype,{$$:{value:record}}))}function RegisteredPointer_fromWireType(ptr){var rawPointer=this.getPointee(ptr);if(!rawPointer){this.destructor(ptr);return null}var registeredInstance=getInheritedInstance(this.registeredClass,rawPointer);if(undefined!==registeredInstance){if(0===registeredInstance.$$.count.value){registeredInstance.$$.ptr=rawPointer;registeredInstance.$$.smartPtr=ptr;return registeredInstance[\"clone\"]()}else{var rv=registeredInstance[\"clone\"]();this.destructor(ptr);return rv}}function makeDefaultHandle(){if(this.isSmartPointer){return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:rawPointer,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this,ptr:ptr})}}var actualType=this.registeredClass.getActualType(rawPointer);var registeredPointerRecord=registeredPointers[actualType];if(!registeredPointerRecord){return makeDefaultHandle.call(this)}var toType;if(this.isConst){toType=registeredPointerRecord.constPointerType}else{toType=registeredPointerRecord.pointerType}var dp=downcastPointer(rawPointer,this.registeredClass,toType.registeredClass);if(dp===null){return makeDefaultHandle.call(this)}if(this.isSmartPointer){return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp})}}function init_RegisteredPointer(){RegisteredPointer.prototype.getPointee=RegisteredPointer_getPointee;RegisteredPointer.prototype.destructor=RegisteredPointer_destructor;RegisteredPointer.prototype[\"argPackAdvance\"]=8;RegisteredPointer.prototype[\"readValueFromPointer\"]=simpleReadValueFromPointer;RegisteredPointer.prototype[\"deleteObject\"]=RegisteredPointer_deleteObject;RegisteredPointer.prototype[\"fromWireType\"]=RegisteredPointer_fromWireType}function RegisteredPointer(name,registeredClass,isReference,isConst,isSmartPointer,pointeeType,sharingPolicy,rawGetPointee,rawConstructor,rawShare,rawDestructor){this.name=name;this.registeredClass=registeredClass;this.isReference=isReference;this.isConst=isConst;this.isSmartPointer=isSmartPointer;this.pointeeType=pointeeType;this.sharingPolicy=sharingPolicy;this.rawGetPointee=rawGetPointee;this.rawConstructor=rawConstructor;this.rawShare=rawShare;this.rawDestructor=rawDestructor;if(!isSmartPointer&&registeredClass.baseClass===undefined){if(isConst){this[\"toWireType\"]=constNoSmartPtrRawPointerToWireType;this.destructorFunction=null}else{this[\"toWireType\"]=nonConstNoSmartPtrRawPointerToWireType;this.destructorFunction=null}}else{this[\"toWireType\"]=genericPointerToWireType}}function replacePublicSymbol(name,value,numArguments){if(!Module.hasOwnProperty(name)){throwInternalError(\"Replacing nonexistant public symbol\")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}}function getDynCaller(sig,ptr){assert(sig.indexOf(\"j\")>=0,\"getDynCaller should only be called with i64 sigs\");var argCache=[];return function(){argCache.length=arguments.length;for(var i=0;i<arguments.length;i++){argCache[i]=arguments[i]}return dynCall(sig,ptr,argCache)}}function embind__requireFunction(signature,rawFunction){signature=readLatin1String(signature);function makeDynCaller(){if(signature.indexOf(\"j\")!=-1){return getDynCaller(signature,rawFunction)}return wasmTable.get(rawFunction)}var fp=makeDynCaller();if(typeof fp!==\"function\"){throwBindingError(\"unknown function pointer with signature \"+signature+\": \"+rawFunction)}return fp}var UnboundTypeError=undefined;function getTypeName(type){var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv}function throwUnboundTypeError(message,types){var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(message+\": \"+unboundTypes.map(getTypeName).join([\", \"]))}function __embind_register_class(rawType,rawPointerType,rawConstPointerType,baseClassRawType,getActualTypeSignature,getActualType,upcastSignature,upcast,downcastSignature,downcast,name,destructorSignature,rawDestructor){name=readLatin1String(name);getActualType=embind__requireFunction(getActualTypeSignature,getActualType);if(upcast){upcast=embind__requireFunction(upcastSignature,upcast)}if(downcast){downcast=embind__requireFunction(downcastSignature,downcast)}rawDestructor=embind__requireFunction(destructorSignature,rawDestructor);var legalFunctionName=makeLegalFunctionName(name);exposePublicSymbol(legalFunctionName,function(){throwUnboundTypeError(\"Cannot construct \"+name+\" due to unbound types\",[baseClassRawType])});whenDependentTypesAreResolved([rawType,rawPointerType,rawConstPointerType],baseClassRawType?[baseClassRawType]:[],function(base){base=base[0];var baseClass;var basePrototype;if(baseClassRawType){baseClass=base.registeredClass;basePrototype=baseClass.instancePrototype}else{basePrototype=ClassHandle.prototype}var constructor=createNamedFunction(legalFunctionName,function(){if(Object.getPrototypeOf(this)!==instancePrototype){throw new BindingError(\"Use 'new' to construct \"+name)}if(undefined===registeredClass.constructor_body){throw new BindingError(name+\" has no accessible constructor\")}var body=registeredClass.constructor_body[arguments.length];if(undefined===body){throw new BindingError(\"Tried to invoke ctor of \"+name+\" with invalid number of parameters (\"+arguments.length+\") - expected (\"+Object.keys(registeredClass.constructor_body).toString()+\") parameters instead!\")}return body.apply(this,arguments)});var instancePrototype=Object.create(basePrototype,{constructor:{value:constructor}});constructor.prototype=instancePrototype;var registeredClass=new RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast);var referenceConverter=new RegisteredPointer(name,registeredClass,true,false,false);var pointerConverter=new RegisteredPointer(name+\"*\",registeredClass,false,false,false);var constPointerConverter=new RegisteredPointer(name+\" const*\",registeredClass,false,true,false);registeredPointers[rawType]={pointerType:pointerConverter,constPointerType:constPointerConverter};replacePublicSymbol(legalFunctionName,constructor);return[referenceConverter,pointerConverter,constPointerConverter]})}function heap32VectorToArray(count,firstElement){var array=[];for(var i=0;i<count;i++){array.push(HEAP32[(firstElement>>2)+i])}return array}function __embind_register_class_constructor(rawClassType,argCount,rawArgTypesAddr,invokerSignature,invoker,rawConstructor){assert(argCount>0);var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);invoker=embind__requireFunction(invokerSignature,invoker);var args=[rawConstructor];var destructors=[];whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=\"constructor \"+classType.name;if(undefined===classType.registeredClass.constructor_body){classType.registeredClass.constructor_body=[]}if(undefined!==classType.registeredClass.constructor_body[argCount-1]){throw new BindingError(\"Cannot register multiple constructors with identical number of parameters (\"+(argCount-1)+\") for class '\"+classType.name+\"'! Overload resolution is currently only performed using the parameter count, not actual type info!\")}classType.registeredClass.constructor_body[argCount-1]=function unboundTypeHandler(){throwUnboundTypeError(\"Cannot construct \"+classType.name+\" due to unbound types\",rawArgTypes)};whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){classType.registeredClass.constructor_body[argCount-1]=function constructor_body(){if(arguments.length!==argCount-1){throwBindingError(humanName+\" called with \"+arguments.length+\" arguments, expected \"+(argCount-1))}destructors.length=0;args.length=argCount;for(var i=1;i<argCount;++i){args[i]=argTypes[i][\"toWireType\"](destructors,arguments[i-1])}var ptr=invoker.apply(null,args);runDestructors(destructors);return argTypes[0][\"fromWireType\"](ptr)};return[]});return[]})}function new_(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(\"new_ called with constructor type \"+typeof constructor+\" which is not a function\")}var dummy=createNamedFunction(constructor.name||\"unknownFunctionName\",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc){var argCount=argTypes.length;if(argCount<2){throwBindingError(\"argTypes array size mismatch! Must at least get return value and 'this' types!\")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!==\"void\";var argsList=\"\";var argsListWired=\"\";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?\", \":\"\")+\"arg\"+i;argsListWired+=(i!==0?\", \":\"\")+\"arg\"+i+\"Wired\"}var invokerFnBody=\"return function \"+makeLegalFunctionName(humanName)+\"(\"+argsList+\") {\\n\"+\"if (arguments.length !== \"+(argCount-2)+\") {\\n\"+\"throwBindingError('function \"+humanName+\" called with ' + arguments.length + ' arguments, expected \"+(argCount-2)+\" args!');\\n\"+\"}\\n\";if(needsDestructorStack){invokerFnBody+=\"var destructors = [];\\n\"}var dtorStack=needsDestructorStack?\"destructors\":\"null\";var args1=[\"throwBindingError\",\"invoker\",\"fn\",\"runDestructors\",\"retType\",\"classParam\"];var args2=[throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];if(isClassMethodFunc){invokerFnBody+=\"var thisWired = classParam.toWireType(\"+dtorStack+\", this);\\n\"}for(var i=0;i<argCount-2;++i){invokerFnBody+=\"var arg\"+i+\"Wired = argType\"+i+\".toWireType(\"+dtorStack+\", arg\"+i+\"); // \"+argTypes[i+2].name+\"\\n\";args1.push(\"argType\"+i);args2.push(argTypes[i+2])}if(isClassMethodFunc){argsListWired=\"thisWired\"+(argsListWired.length>0?\", \":\"\")+argsListWired}invokerFnBody+=(returns?\"var rv = \":\"\")+\"invoker(fn\"+(argsListWired.length>0?\", \":\"\")+argsListWired+\");\\n\";if(needsDestructorStack){invokerFnBody+=\"runDestructors(destructors);\\n\"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?\"thisWired\":\"arg\"+(i-2)+\"Wired\";if(argTypes[i].destructorFunction!==null){invokerFnBody+=paramName+\"_dtor(\"+paramName+\"); // \"+argTypes[i].name+\"\\n\";args1.push(paramName+\"_dtor\");args2.push(argTypes[i].destructorFunction)}}}if(returns){invokerFnBody+=\"var ret = retType.fromWireType(rv);\\n\"+\"return ret;\\n\"}else{}invokerFnBody+=\"}\\n\";args1.push(invokerFnBody);var invokerFunction=new_(Function,args1).apply(null,args2);return invokerFunction}function __embind_register_class_function(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,context,isPureVirtual){var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=classType.name+\".\"+methodName;if(isPureVirtual){classType.registeredClass.pureVirtualFunctions.push(methodName)}function unboundTypesHandler(){throwUnboundTypeError(\"Cannot call \"+humanName+\" due to unbound types\",rawArgTypes)}var proto=classType.registeredClass.instancePrototype;var method=proto[methodName];if(undefined===method||undefined===method.overloadTable&&method.className!==classType.name&&method.argCount===argCount-2){unboundTypesHandler.argCount=argCount-2;unboundTypesHandler.className=classType.name;proto[methodName]=unboundTypesHandler}else{ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-2]=unboundTypesHandler}whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){var memberFunction=craftInvokerFunction(humanName,argTypes,classType,rawInvoker,context);if(undefined===proto[methodName].overloadTable){memberFunction.argCount=argCount-2;proto[methodName]=memberFunction}else{proto[methodName].overloadTable[argCount-2]=memberFunction}return[]});return[]})}var emval_free_list=[];var emval_handle_array=[{},{value:undefined},{value:null},{value:true},{value:false}];function __emval_decref(handle){if(handle>4&&0===--emval_handle_array[handle].refcount){emval_handle_array[handle]=undefined;emval_free_list.push(handle)}}function count_emval_handles(){var count=0;for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){++count}}return count}function get_first_emval(){for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){return emval_handle_array[i]}}return null}function init_emval(){Module[\"count_emval_handles\"]=count_emval_handles;Module[\"get_first_emval\"]=get_first_emval}function __emval_register(value){switch(value){case undefined:{return 1}case null:{return 2}case true:{return 3}case false:{return 4}default:{var handle=emval_free_list.length?emval_free_list.pop():emval_handle_array.length;emval_handle_array[handle]={refcount:1,value:value};return handle}}}function __embind_register_emval(rawType,name){name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(handle){var rv=emval_handle_array[handle].value;__emval_decref(handle);return rv},\"toWireType\":function(destructors,value){return __emval_register(value)},\"argPackAdvance\":8,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:null})}function _embind_repr(v){if(v===null){return\"null\"}var t=typeof v;if(t===\"object\"||t===\"array\"||t===\"function\"){return v.toString()}else{return\"\"+v}}function floatReadValueFromPointer(name,shift){switch(shift){case 2:return function(pointer){return this[\"fromWireType\"](HEAPF32[pointer>>2])};case 3:return function(pointer){return this[\"fromWireType\"](HEAPF64[pointer>>3])};default:throw new TypeError(\"Unknown float type: \"+name)}}function __embind_register_float(rawType,name,size){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(value){return value},\"toWireType\":function(destructors,value){if(typeof value!==\"number\"&&typeof value!==\"boolean\"){throw new TypeError('Cannot convert \"'+_embind_repr(value)+'\" to '+this.name)}return value},\"argPackAdvance\":8,\"readValueFromPointer\":floatReadValueFromPointer(name,shift),destructorFunction:null})}function __embind_register_function(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn){var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(\"Cannot call \"+name+\" due to unbound types\",argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn),argCount-1);return[]})}function integerReadValueFromPointer(name,shift,signed){switch(shift){case 0:return signed?function readS8FromPointer(pointer){return HEAP8[pointer]}:function readU8FromPointer(pointer){return HEAPU8[pointer]};case 1:return signed?function readS16FromPointer(pointer){return HEAP16[pointer>>1]}:function readU16FromPointer(pointer){return HEAPU16[pointer>>1]};case 2:return signed?function readS32FromPointer(pointer){return HEAP32[pointer>>2]}:function readU32FromPointer(pointer){return HEAPU32[pointer>>2]};default:throw new TypeError(\"Unknown integer type: \"+name)}}function __embind_register_integer(primitiveType,name,size,minRange,maxRange){name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var shift=getShiftFromSize(size);var fromWireType=function(value){return value};if(minRange===0){var bitshift=32-8*size;fromWireType=function(value){return value<<bitshift>>>bitshift}}var isUnsignedType=name.indexOf(\"unsigned\")!=-1;registerType(primitiveType,{name:name,\"fromWireType\":fromWireType,\"toWireType\":function(destructors,value){if(typeof value!==\"number\"&&typeof value!==\"boolean\"){throw new TypeError('Cannot convert \"'+_embind_repr(value)+'\" to '+this.name)}if(value<minRange||value>maxRange){throw new TypeError('Passing a number \"'+_embind_repr(value)+'\" from JS side to C/C++ side to an argument of type \"'+name+'\", which is outside the valid range ['+minRange+\", \"+maxRange+\"]!\")}return isUnsignedType?value>>>0:value|0},\"argPackAdvance\":8,\"readValueFromPointer\":integerReadValueFromPointer(name,shift,minRange!==0),destructorFunction:null})}function __embind_register_memory_view(rawType,dataTypeIndex,name){var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){handle=handle>>2;var heap=HEAPU32;var size=heap[handle];var data=heap[handle+1];return new TA(buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":decodeMemoryView,\"argPackAdvance\":8,\"readValueFromPointer\":decodeMemoryView},{ignoreDuplicateRegistrations:true})}function __embind_register_std_string(rawType,name){name=readLatin1String(name);var stdStringIsUTF8=name===\"std::string\";registerType(rawType,{name:name,\"fromWireType\":function(value){var length=HEAPU32[value>>2];var str;if(stdStringIsUTF8){var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[value+4+i])}str=a.join(\"\")}_free(value);return str},\"toWireType\":function(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var getLength;var valueIsOfTypeString=typeof value===\"string\";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError(\"Cannot pass non-string to std::string\")}if(stdStringIsUTF8&&valueIsOfTypeString){getLength=function(){return lengthBytesUTF8(value)}}else{getLength=function(){return value.length}}var length=getLength();var ptr=_malloc(4+length+1);HEAPU32[ptr>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr+4,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError(\"String has UTF-16 code units that do not fit in 8 bits\")}HEAPU8[ptr+4+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+4+i]=value[i]}}}if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":8,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_std_wstring(rawType,charSize,name){name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=function(){return HEAPU16};shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=function(){return HEAPU32};shift=2}registerType(rawType,{name:name,\"fromWireType\":function(value){var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},\"toWireType\":function(destructors,value){if(!(typeof value===\"string\")){throwBindingError(\"Cannot pass non-string to C++ string type \"+name)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":8,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_value_object(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor){structRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),fields:[]}}function __embind_register_value_object_field(structType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext){structRegistrations[structType].fields.push({fieldName:readLatin1String(fieldName),getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})}function __embind_register_void(rawType,name){name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,\"argPackAdvance\":0,\"fromWireType\":function(){return undefined},\"toWireType\":function(destructors,o){return undefined}})}function __emval_incref(handle){if(handle>4){emval_handle_array[handle].refcount+=1}}function requireRegisteredType(rawType,humanName){var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+\" has unknown type \"+getTypeName(rawType))}return impl}function __emval_take_value(type,argv){type=requireRegisteredType(type,\"_emval_take_value\");var v=type[\"readValueFromPointer\"](argv);return __emval_register(v)}function _abort(){abort()}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function _emscripten_get_heap_size(){return HEAPU8.length}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){console.error(\"emscripten_realloc_buffer: Attempted to grow heap from \"+buffer.byteLength+\" bytes to \"+size+\" bytes, but got error: \"+e)}}function _emscripten_resize_heap(requestedSize){requestedSize=requestedSize>>>0;var oldSize=_emscripten_get_heap_size();assert(requestedSize>oldSize);var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){err(\"Cannot enlarge memory, asked to go up to \"+requestedSize+\" bytes, but the limit is \"+maxHeapSize+\" bytes!\");return false}var minHeapSize=16777216;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(minHeapSize,requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}err(\"Failed to grow the heap from \"+oldSize+\" bytes to \"+newSize+\" bytes, not enough memory!\");return false}function flush_NO_FILESYSTEM(){if(typeof _fflush!==\"undefined\")_fflush(0);var buffers=SYSCALLS.buffers;if(buffers[1].length)SYSCALLS.printChar(1,10);if(buffers[2].length)SYSCALLS.printChar(2,10)}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];assert(buffer);if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){assert(SYSCALLS.varargs!=undefined);SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){if(low>=0)assert(high===0);else assert(high===-1);return low}};function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function _onAtlasProgress(mode,progress){if(Module.onAtlasProgress)Module.onAtlasProgress(mode,progress)}function _setTempRet0($i){setTempRet0($i|0)}InternalError=Module[\"InternalError\"]=extendError(Error,\"InternalError\");embind_init_charCodes();BindingError=Module[\"BindingError\"]=extendError(Error,\"BindingError\");init_ClassHandle();init_RegisteredPointer();init_embind();UnboundTypeError=Module[\"UnboundTypeError\"]=extendError(Error,\"UnboundTypeError\");init_emval();var ASSERTIONS=true;__ATINIT__.push({func:function(){___wasm_call_ctors()}});var asmLibraryArg={\"__cxa_allocate_exception\":___cxa_allocate_exception,\"__cxa_throw\":___cxa_throw,\"_embind_finalize_value_object\":__embind_finalize_value_object,\"_embind_register_bool\":__embind_register_bool,\"_embind_register_class\":__embind_register_class,\"_embind_register_class_constructor\":__embind_register_class_constructor,\"_embind_register_class_function\":__embind_register_class_function,\"_embind_register_emval\":__embind_register_emval,\"_embind_register_float\":__embind_register_float,\"_embind_register_function\":__embind_register_function,\"_embind_register_integer\":__embind_register_integer,\"_embind_register_memory_view\":__embind_register_memory_view,\"_embind_register_std_string\":__embind_register_std_string,\"_embind_register_std_wstring\":__embind_register_std_wstring,\"_embind_register_value_object\":__embind_register_value_object,\"_embind_register_value_object_field\":__embind_register_value_object_field,\"_embind_register_void\":__embind_register_void,\"_emval_decref\":__emval_decref,\"_emval_incref\":__emval_incref,\"_emval_take_value\":__emval_take_value,\"abort\":_abort,\"emscripten_memcpy_big\":_emscripten_memcpy_big,\"emscripten_resize_heap\":_emscripten_resize_heap,\"fd_write\":_fd_write,\"memory\":wasmMemory,\"onAtlasProgress\":_onAtlasProgress,\"setTempRet0\":_setTempRet0};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=createExportWrapper(\"__wasm_call_ctors\");var _free=Module[\"_free\"]=createExportWrapper(\"free\");var ___getTypeName=Module[\"___getTypeName\"]=createExportWrapper(\"__getTypeName\");var ___embind_register_native_and_builtin_types=Module[\"___embind_register_native_and_builtin_types\"]=createExportWrapper(\"__embind_register_native_and_builtin_types\");var ___errno_location=Module[\"___errno_location\"]=createExportWrapper(\"__errno_location\");var _malloc=Module[\"_malloc\"]=createExportWrapper(\"malloc\");var _fflush=Module[\"_fflush\"]=createExportWrapper(\"fflush\");var stackSave=Module[\"stackSave\"]=createExportWrapper(\"stackSave\");var stackRestore=Module[\"stackRestore\"]=createExportWrapper(\"stackRestore\");var stackAlloc=Module[\"stackAlloc\"]=createExportWrapper(\"stackAlloc\");var _setThrew=Module[\"_setThrew\"]=createExportWrapper(\"setThrew\");var _sbrk=Module[\"_sbrk\"]=createExportWrapper(\"sbrk\");var dynCall_jiji=Module[\"dynCall_jiji\"]=createExportWrapper(\"dynCall_jiji\");if(!Object.getOwnPropertyDescriptor(Module,\"intArrayFromString\"))Module[\"intArrayFromString\"]=function(){abort(\"'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"intArrayToString\"))Module[\"intArrayToString\"]=function(){abort(\"'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ccall\"))Module[\"ccall\"]=function(){abort(\"'ccall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"cwrap\"))Module[\"cwrap\"]=function(){abort(\"'cwrap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"setValue\"))Module[\"setValue\"]=function(){abort(\"'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getValue\"))Module[\"getValue\"]=function(){abort(\"'getValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"allocate\"))Module[\"allocate\"]=function(){abort(\"'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UTF8ArrayToString\"))Module[\"UTF8ArrayToString\"]=function(){abort(\"'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UTF8ToString\"))Module[\"UTF8ToString\"]=function(){abort(\"'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToUTF8Array\"))Module[\"stringToUTF8Array\"]=function(){abort(\"'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToUTF8\"))Module[\"stringToUTF8\"]=function(){abort(\"'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"lengthBytesUTF8\"))Module[\"lengthBytesUTF8\"]=function(){abort(\"'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stackTrace\"))Module[\"stackTrace\"]=function(){abort(\"'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addOnPreRun\"))Module[\"addOnPreRun\"]=function(){abort(\"'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addOnInit\"))Module[\"addOnInit\"]=function(){abort(\"'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addOnPreMain\"))Module[\"addOnPreMain\"]=function(){abort(\"'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addOnExit\"))Module[\"addOnExit\"]=function(){abort(\"'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addOnPostRun\"))Module[\"addOnPostRun\"]=function(){abort(\"'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeStringToMemory\"))Module[\"writeStringToMemory\"]=function(){abort(\"'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeArrayToMemory\"))Module[\"writeArrayToMemory\"]=function(){abort(\"'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeAsciiToMemory\"))Module[\"writeAsciiToMemory\"]=function(){abort(\"'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addRunDependency\"))Module[\"addRunDependency\"]=function(){abort(\"'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"removeRunDependency\"))Module[\"removeRunDependency\"]=function(){abort(\"'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createFolder\"))Module[\"FS_createFolder\"]=function(){abort(\"'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createPath\"))Module[\"FS_createPath\"]=function(){abort(\"'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createDataFile\"))Module[\"FS_createDataFile\"]=function(){abort(\"'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createPreloadedFile\"))Module[\"FS_createPreloadedFile\"]=function(){abort(\"'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createLazyFile\"))Module[\"FS_createLazyFile\"]=function(){abort(\"'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createLink\"))Module[\"FS_createLink\"]=function(){abort(\"'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_createDevice\"))Module[\"FS_createDevice\"]=function(){abort(\"'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS_unlink\"))Module[\"FS_unlink\"]=function(){abort(\"'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you\")};if(!Object.getOwnPropertyDescriptor(Module,\"getLEB\"))Module[\"getLEB\"]=function(){abort(\"'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getFunctionTables\"))Module[\"getFunctionTables\"]=function(){abort(\"'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"alignFunctionTables\"))Module[\"alignFunctionTables\"]=function(){abort(\"'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registerFunctions\"))Module[\"registerFunctions\"]=function(){abort(\"'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"addFunction\"))Module[\"addFunction\"]=function(){abort(\"'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"removeFunction\"))Module[\"removeFunction\"]=function(){abort(\"'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getFuncWrapper\"))Module[\"getFuncWrapper\"]=function(){abort(\"'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"prettyPrint\"))Module[\"prettyPrint\"]=function(){abort(\"'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"makeBigInt\"))Module[\"makeBigInt\"]=function(){abort(\"'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"dynCall\"))Module[\"dynCall\"]=function(){abort(\"'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getCompilerSetting\"))Module[\"getCompilerSetting\"]=function(){abort(\"'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"print\"))Module[\"print\"]=function(){abort(\"'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"printErr\"))Module[\"printErr\"]=function(){abort(\"'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getTempRet0\"))Module[\"getTempRet0\"]=function(){abort(\"'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"setTempRet0\"))Module[\"setTempRet0\"]=function(){abort(\"'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"callMain\"))Module[\"callMain\"]=function(){abort(\"'callMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"abort\"))Module[\"abort\"]=function(){abort(\"'abort' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToNewUTF8\"))Module[\"stringToNewUTF8\"]=function(){abort(\"'stringToNewUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emscripten_realloc_buffer\"))Module[\"emscripten_realloc_buffer\"]=function(){abort(\"'emscripten_realloc_buffer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ENV\"))Module[\"ENV\"]=function(){abort(\"'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ERRNO_CODES\"))Module[\"ERRNO_CODES\"]=function(){abort(\"'ERRNO_CODES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ERRNO_MESSAGES\"))Module[\"ERRNO_MESSAGES\"]=function(){abort(\"'ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"setErrNo\"))Module[\"setErrNo\"]=function(){abort(\"'setErrNo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"DNS\"))Module[\"DNS\"]=function(){abort(\"'DNS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getHostByName\"))Module[\"getHostByName\"]=function(){abort(\"'getHostByName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GAI_ERRNO_MESSAGES\"))Module[\"GAI_ERRNO_MESSAGES\"]=function(){abort(\"'GAI_ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"Protocols\"))Module[\"Protocols\"]=function(){abort(\"'Protocols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"Sockets\"))Module[\"Sockets\"]=function(){abort(\"'Sockets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getRandomDevice\"))Module[\"getRandomDevice\"]=function(){abort(\"'getRandomDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"traverseStack\"))Module[\"traverseStack\"]=function(){abort(\"'traverseStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UNWIND_CACHE\"))Module[\"UNWIND_CACHE\"]=function(){abort(\"'UNWIND_CACHE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"withBuiltinMalloc\"))Module[\"withBuiltinMalloc\"]=function(){abort(\"'withBuiltinMalloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"readAsmConstArgsArray\"))Module[\"readAsmConstArgsArray\"]=function(){abort(\"'readAsmConstArgsArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"readAsmConstArgs\"))Module[\"readAsmConstArgs\"]=function(){abort(\"'readAsmConstArgs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"mainThreadEM_ASM\"))Module[\"mainThreadEM_ASM\"]=function(){abort(\"'mainThreadEM_ASM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"jstoi_q\"))Module[\"jstoi_q\"]=function(){abort(\"'jstoi_q' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"jstoi_s\"))Module[\"jstoi_s\"]=function(){abort(\"'jstoi_s' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getExecutableName\"))Module[\"getExecutableName\"]=function(){abort(\"'getExecutableName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"listenOnce\"))Module[\"listenOnce\"]=function(){abort(\"'listenOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"autoResumeAudioContext\"))Module[\"autoResumeAudioContext\"]=function(){abort(\"'autoResumeAudioContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"dynCallLegacy\"))Module[\"dynCallLegacy\"]=function(){abort(\"'dynCallLegacy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getDynCaller\"))Module[\"getDynCaller\"]=function(){abort(\"'getDynCaller' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"dynCall\"))Module[\"dynCall\"]=function(){abort(\"'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"callRuntimeCallbacks\"))Module[\"callRuntimeCallbacks\"]=function(){abort(\"'callRuntimeCallbacks' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"abortStackOverflow\"))Module[\"abortStackOverflow\"]=function(){abort(\"'abortStackOverflow' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"reallyNegative\"))Module[\"reallyNegative\"]=function(){abort(\"'reallyNegative' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"unSign\"))Module[\"unSign\"]=function(){abort(\"'unSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"reSign\"))Module[\"reSign\"]=function(){abort(\"'reSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"formatString\"))Module[\"formatString\"]=function(){abort(\"'formatString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"PATH\"))Module[\"PATH\"]=function(){abort(\"'PATH' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"PATH_FS\"))Module[\"PATH_FS\"]=function(){abort(\"'PATH_FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SYSCALLS\"))Module[\"SYSCALLS\"]=function(){abort(\"'SYSCALLS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"syscallMmap2\"))Module[\"syscallMmap2\"]=function(){abort(\"'syscallMmap2' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"syscallMunmap\"))Module[\"syscallMunmap\"]=function(){abort(\"'syscallMunmap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"flush_NO_FILESYSTEM\"))Module[\"flush_NO_FILESYSTEM\"]=function(){abort(\"'flush_NO_FILESYSTEM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"JSEvents\"))Module[\"JSEvents\"]=function(){abort(\"'JSEvents' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"specialHTMLTargets\"))Module[\"specialHTMLTargets\"]=function(){abort(\"'specialHTMLTargets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"maybeCStringToJsString\"))Module[\"maybeCStringToJsString\"]=function(){abort(\"'maybeCStringToJsString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"findEventTarget\"))Module[\"findEventTarget\"]=function(){abort(\"'findEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"findCanvasEventTarget\"))Module[\"findCanvasEventTarget\"]=function(){abort(\"'findCanvasEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"polyfillSetImmediate\"))Module[\"polyfillSetImmediate\"]=function(){abort(\"'polyfillSetImmediate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"demangle\"))Module[\"demangle\"]=function(){abort(\"'demangle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"demangleAll\"))Module[\"demangleAll\"]=function(){abort(\"'demangleAll' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"jsStackTrace\"))Module[\"jsStackTrace\"]=function(){abort(\"'jsStackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stackTrace\"))Module[\"stackTrace\"]=function(){abort(\"'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getEnvStrings\"))Module[\"getEnvStrings\"]=function(){abort(\"'getEnvStrings' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"checkWasiClock\"))Module[\"checkWasiClock\"]=function(){abort(\"'checkWasiClock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeI53ToI64\"))Module[\"writeI53ToI64\"]=function(){abort(\"'writeI53ToI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeI53ToI64Clamped\"))Module[\"writeI53ToI64Clamped\"]=function(){abort(\"'writeI53ToI64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeI53ToI64Signaling\"))Module[\"writeI53ToI64Signaling\"]=function(){abort(\"'writeI53ToI64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeI53ToU64Clamped\"))Module[\"writeI53ToU64Clamped\"]=function(){abort(\"'writeI53ToU64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeI53ToU64Signaling\"))Module[\"writeI53ToU64Signaling\"]=function(){abort(\"'writeI53ToU64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"readI53FromI64\"))Module[\"readI53FromI64\"]=function(){abort(\"'readI53FromI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"readI53FromU64\"))Module[\"readI53FromU64\"]=function(){abort(\"'readI53FromU64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"convertI32PairToI53\"))Module[\"convertI32PairToI53\"]=function(){abort(\"'convertI32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"convertU32PairToI53\"))Module[\"convertU32PairToI53\"]=function(){abort(\"'convertU32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"exceptionLast\"))Module[\"exceptionLast\"]=function(){abort(\"'exceptionLast' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"exceptionCaught\"))Module[\"exceptionCaught\"]=function(){abort(\"'exceptionCaught' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ExceptionInfoAttrs\"))Module[\"ExceptionInfoAttrs\"]=function(){abort(\"'ExceptionInfoAttrs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ExceptionInfo\"))Module[\"ExceptionInfo\"]=function(){abort(\"'ExceptionInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"CatchInfo\"))Module[\"CatchInfo\"]=function(){abort(\"'CatchInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"exception_addRef\"))Module[\"exception_addRef\"]=function(){abort(\"'exception_addRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"exception_decRef\"))Module[\"exception_decRef\"]=function(){abort(\"'exception_decRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"Browser\"))Module[\"Browser\"]=function(){abort(\"'Browser' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"funcWrappers\"))Module[\"funcWrappers\"]=function(){abort(\"'funcWrappers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getFuncWrapper\"))Module[\"getFuncWrapper\"]=function(){abort(\"'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"setMainLoop\"))Module[\"setMainLoop\"]=function(){abort(\"'setMainLoop' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"FS\"))Module[\"FS\"]=function(){abort(\"'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"mmapAlloc\"))Module[\"mmapAlloc\"]=function(){abort(\"'mmapAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"MEMFS\"))Module[\"MEMFS\"]=function(){abort(\"'MEMFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"TTY\"))Module[\"TTY\"]=function(){abort(\"'TTY' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"PIPEFS\"))Module[\"PIPEFS\"]=function(){abort(\"'PIPEFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SOCKFS\"))Module[\"SOCKFS\"]=function(){abort(\"'SOCKFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"tempFixedLengthArray\"))Module[\"tempFixedLengthArray\"]=function(){abort(\"'tempFixedLengthArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"miniTempWebGLFloatBuffers\"))Module[\"miniTempWebGLFloatBuffers\"]=function(){abort(\"'miniTempWebGLFloatBuffers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"heapObjectForWebGLType\"))Module[\"heapObjectForWebGLType\"]=function(){abort(\"'heapObjectForWebGLType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"heapAccessShiftForWebGLHeap\"))Module[\"heapAccessShiftForWebGLHeap\"]=function(){abort(\"'heapAccessShiftForWebGLHeap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GL\"))Module[\"GL\"]=function(){abort(\"'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emscriptenWebGLGet\"))Module[\"emscriptenWebGLGet\"]=function(){abort(\"'emscriptenWebGLGet' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"computeUnpackAlignedImageSize\"))Module[\"computeUnpackAlignedImageSize\"]=function(){abort(\"'computeUnpackAlignedImageSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emscriptenWebGLGetTexPixelData\"))Module[\"emscriptenWebGLGetTexPixelData\"]=function(){abort(\"'emscriptenWebGLGetTexPixelData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emscriptenWebGLGetUniform\"))Module[\"emscriptenWebGLGetUniform\"]=function(){abort(\"'emscriptenWebGLGetUniform' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emscriptenWebGLGetVertexAttrib\"))Module[\"emscriptenWebGLGetVertexAttrib\"]=function(){abort(\"'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"writeGLArray\"))Module[\"writeGLArray\"]=function(){abort(\"'writeGLArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"AL\"))Module[\"AL\"]=function(){abort(\"'AL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SDL_unicode\"))Module[\"SDL_unicode\"]=function(){abort(\"'SDL_unicode' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SDL_ttfContext\"))Module[\"SDL_ttfContext\"]=function(){abort(\"'SDL_ttfContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SDL_audio\"))Module[\"SDL_audio\"]=function(){abort(\"'SDL_audio' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SDL\"))Module[\"SDL\"]=function(){abort(\"'SDL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"SDL_gfx\"))Module[\"SDL_gfx\"]=function(){abort(\"'SDL_gfx' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GLUT\"))Module[\"GLUT\"]=function(){abort(\"'GLUT' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"EGL\"))Module[\"EGL\"]=function(){abort(\"'EGL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GLFW_Window\"))Module[\"GLFW_Window\"]=function(){abort(\"'GLFW_Window' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GLFW\"))Module[\"GLFW\"]=function(){abort(\"'GLFW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"GLEW\"))Module[\"GLEW\"]=function(){abort(\"'GLEW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"IDBStore\"))Module[\"IDBStore\"]=function(){abort(\"'IDBStore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"runAndAbortIfError\"))Module[\"runAndAbortIfError\"]=function(){abort(\"'runAndAbortIfError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_handle_array\"))Module[\"emval_handle_array\"]=function(){abort(\"'emval_handle_array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_free_list\"))Module[\"emval_free_list\"]=function(){abort(\"'emval_free_list' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_symbols\"))Module[\"emval_symbols\"]=function(){abort(\"'emval_symbols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"init_emval\"))Module[\"init_emval\"]=function(){abort(\"'init_emval' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"count_emval_handles\"))Module[\"count_emval_handles\"]=function(){abort(\"'count_emval_handles' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"get_first_emval\"))Module[\"get_first_emval\"]=function(){abort(\"'get_first_emval' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getStringOrSymbol\"))Module[\"getStringOrSymbol\"]=function(){abort(\"'getStringOrSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"requireHandle\"))Module[\"requireHandle\"]=function(){abort(\"'requireHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_newers\"))Module[\"emval_newers\"]=function(){abort(\"'emval_newers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"craftEmvalAllocator\"))Module[\"craftEmvalAllocator\"]=function(){abort(\"'craftEmvalAllocator' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_get_global\"))Module[\"emval_get_global\"]=function(){abort(\"'emval_get_global' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"emval_methodCallers\"))Module[\"emval_methodCallers\"]=function(){abort(\"'emval_methodCallers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"InternalError\"))Module[\"InternalError\"]=function(){abort(\"'InternalError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"BindingError\"))Module[\"BindingError\"]=function(){abort(\"'BindingError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UnboundTypeError\"))Module[\"UnboundTypeError\"]=function(){abort(\"'UnboundTypeError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"PureVirtualError\"))Module[\"PureVirtualError\"]=function(){abort(\"'PureVirtualError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"init_embind\"))Module[\"init_embind\"]=function(){abort(\"'init_embind' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"throwInternalError\"))Module[\"throwInternalError\"]=function(){abort(\"'throwInternalError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"throwBindingError\"))Module[\"throwBindingError\"]=function(){abort(\"'throwBindingError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"throwUnboundTypeError\"))Module[\"throwUnboundTypeError\"]=function(){abort(\"'throwUnboundTypeError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ensureOverloadTable\"))Module[\"ensureOverloadTable\"]=function(){abort(\"'ensureOverloadTable' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"exposePublicSymbol\"))Module[\"exposePublicSymbol\"]=function(){abort(\"'exposePublicSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"replacePublicSymbol\"))Module[\"replacePublicSymbol\"]=function(){abort(\"'replacePublicSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"extendError\"))Module[\"extendError\"]=function(){abort(\"'extendError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"createNamedFunction\"))Module[\"createNamedFunction\"]=function(){abort(\"'createNamedFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registeredInstances\"))Module[\"registeredInstances\"]=function(){abort(\"'registeredInstances' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getBasestPointer\"))Module[\"getBasestPointer\"]=function(){abort(\"'getBasestPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registerInheritedInstance\"))Module[\"registerInheritedInstance\"]=function(){abort(\"'registerInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"unregisterInheritedInstance\"))Module[\"unregisterInheritedInstance\"]=function(){abort(\"'unregisterInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getInheritedInstance\"))Module[\"getInheritedInstance\"]=function(){abort(\"'getInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getInheritedInstanceCount\"))Module[\"getInheritedInstanceCount\"]=function(){abort(\"'getInheritedInstanceCount' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getLiveInheritedInstances\"))Module[\"getLiveInheritedInstances\"]=function(){abort(\"'getLiveInheritedInstances' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registeredTypes\"))Module[\"registeredTypes\"]=function(){abort(\"'registeredTypes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"awaitingDependencies\"))Module[\"awaitingDependencies\"]=function(){abort(\"'awaitingDependencies' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"typeDependencies\"))Module[\"typeDependencies\"]=function(){abort(\"'typeDependencies' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registeredPointers\"))Module[\"registeredPointers\"]=function(){abort(\"'registeredPointers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"registerType\"))Module[\"registerType\"]=function(){abort(\"'registerType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"whenDependentTypesAreResolved\"))Module[\"whenDependentTypesAreResolved\"]=function(){abort(\"'whenDependentTypesAreResolved' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"embind_charCodes\"))Module[\"embind_charCodes\"]=function(){abort(\"'embind_charCodes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"embind_init_charCodes\"))Module[\"embind_init_charCodes\"]=function(){abort(\"'embind_init_charCodes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"readLatin1String\"))Module[\"readLatin1String\"]=function(){abort(\"'readLatin1String' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getTypeName\"))Module[\"getTypeName\"]=function(){abort(\"'getTypeName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"heap32VectorToArray\"))Module[\"heap32VectorToArray\"]=function(){abort(\"'heap32VectorToArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"requireRegisteredType\"))Module[\"requireRegisteredType\"]=function(){abort(\"'requireRegisteredType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"getShiftFromSize\"))Module[\"getShiftFromSize\"]=function(){abort(\"'getShiftFromSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"integerReadValueFromPointer\"))Module[\"integerReadValueFromPointer\"]=function(){abort(\"'integerReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"enumReadValueFromPointer\"))Module[\"enumReadValueFromPointer\"]=function(){abort(\"'enumReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"floatReadValueFromPointer\"))Module[\"floatReadValueFromPointer\"]=function(){abort(\"'floatReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"simpleReadValueFromPointer\"))Module[\"simpleReadValueFromPointer\"]=function(){abort(\"'simpleReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"runDestructors\"))Module[\"runDestructors\"]=function(){abort(\"'runDestructors' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"new_\"))Module[\"new_\"]=function(){abort(\"'new_' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"craftInvokerFunction\"))Module[\"craftInvokerFunction\"]=function(){abort(\"'craftInvokerFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"embind__requireFunction\"))Module[\"embind__requireFunction\"]=function(){abort(\"'embind__requireFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"tupleRegistrations\"))Module[\"tupleRegistrations\"]=function(){abort(\"'tupleRegistrations' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"structRegistrations\"))Module[\"structRegistrations\"]=function(){abort(\"'structRegistrations' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"genericPointerToWireType\"))Module[\"genericPointerToWireType\"]=function(){abort(\"'genericPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"constNoSmartPtrRawPointerToWireType\"))Module[\"constNoSmartPtrRawPointerToWireType\"]=function(){abort(\"'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"nonConstNoSmartPtrRawPointerToWireType\"))Module[\"nonConstNoSmartPtrRawPointerToWireType\"]=function(){abort(\"'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"init_RegisteredPointer\"))Module[\"init_RegisteredPointer\"]=function(){abort(\"'init_RegisteredPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredPointer\"))Module[\"RegisteredPointer\"]=function(){abort(\"'RegisteredPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredPointer_getPointee\"))Module[\"RegisteredPointer_getPointee\"]=function(){abort(\"'RegisteredPointer_getPointee' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredPointer_destructor\"))Module[\"RegisteredPointer_destructor\"]=function(){abort(\"'RegisteredPointer_destructor' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredPointer_deleteObject\"))Module[\"RegisteredPointer_deleteObject\"]=function(){abort(\"'RegisteredPointer_deleteObject' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredPointer_fromWireType\"))Module[\"RegisteredPointer_fromWireType\"]=function(){abort(\"'RegisteredPointer_fromWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"runDestructor\"))Module[\"runDestructor\"]=function(){abort(\"'runDestructor' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"releaseClassHandle\"))Module[\"releaseClassHandle\"]=function(){abort(\"'releaseClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"finalizationGroup\"))Module[\"finalizationGroup\"]=function(){abort(\"'finalizationGroup' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"detachFinalizer_deps\"))Module[\"detachFinalizer_deps\"]=function(){abort(\"'detachFinalizer_deps' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"detachFinalizer\"))Module[\"detachFinalizer\"]=function(){abort(\"'detachFinalizer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"attachFinalizer\"))Module[\"attachFinalizer\"]=function(){abort(\"'attachFinalizer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"makeClassHandle\"))Module[\"makeClassHandle\"]=function(){abort(\"'makeClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"init_ClassHandle\"))Module[\"init_ClassHandle\"]=function(){abort(\"'init_ClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle\"))Module[\"ClassHandle\"]=function(){abort(\"'ClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle_isAliasOf\"))Module[\"ClassHandle_isAliasOf\"]=function(){abort(\"'ClassHandle_isAliasOf' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"throwInstanceAlreadyDeleted\"))Module[\"throwInstanceAlreadyDeleted\"]=function(){abort(\"'throwInstanceAlreadyDeleted' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle_clone\"))Module[\"ClassHandle_clone\"]=function(){abort(\"'ClassHandle_clone' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle_delete\"))Module[\"ClassHandle_delete\"]=function(){abort(\"'ClassHandle_delete' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"deletionQueue\"))Module[\"deletionQueue\"]=function(){abort(\"'deletionQueue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle_isDeleted\"))Module[\"ClassHandle_isDeleted\"]=function(){abort(\"'ClassHandle_isDeleted' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"ClassHandle_deleteLater\"))Module[\"ClassHandle_deleteLater\"]=function(){abort(\"'ClassHandle_deleteLater' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"flushPendingDeletes\"))Module[\"flushPendingDeletes\"]=function(){abort(\"'flushPendingDeletes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"delayFunction\"))Module[\"delayFunction\"]=function(){abort(\"'delayFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"setDelayFunction\"))Module[\"setDelayFunction\"]=function(){abort(\"'setDelayFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"RegisteredClass\"))Module[\"RegisteredClass\"]=function(){abort(\"'RegisteredClass' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"shallowCopyInternalPointer\"))Module[\"shallowCopyInternalPointer\"]=function(){abort(\"'shallowCopyInternalPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"downcastPointer\"))Module[\"downcastPointer\"]=function(){abort(\"'downcastPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"upcastPointer\"))Module[\"upcastPointer\"]=function(){abort(\"'upcastPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"validateThis\"))Module[\"validateThis\"]=function(){abort(\"'validateThis' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"char_0\"))Module[\"char_0\"]=function(){abort(\"'char_0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"char_9\"))Module[\"char_9\"]=function(){abort(\"'char_9' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"makeLegalFunctionName\"))Module[\"makeLegalFunctionName\"]=function(){abort(\"'makeLegalFunctionName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"warnOnce\"))Module[\"warnOnce\"]=function(){abort(\"'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stackSave\"))Module[\"stackSave\"]=function(){abort(\"'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stackRestore\"))Module[\"stackRestore\"]=function(){abort(\"'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stackAlloc\"))Module[\"stackAlloc\"]=function(){abort(\"'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"AsciiToString\"))Module[\"AsciiToString\"]=function(){abort(\"'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToAscii\"))Module[\"stringToAscii\"]=function(){abort(\"'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UTF16ToString\"))Module[\"UTF16ToString\"]=function(){abort(\"'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToUTF16\"))Module[\"stringToUTF16\"]=function(){abort(\"'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"lengthBytesUTF16\"))Module[\"lengthBytesUTF16\"]=function(){abort(\"'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"UTF32ToString\"))Module[\"UTF32ToString\"]=function(){abort(\"'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"stringToUTF32\"))Module[\"stringToUTF32\"]=function(){abort(\"'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"lengthBytesUTF32\"))Module[\"lengthBytesUTF32\"]=function(){abort(\"'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"allocateUTF8\"))Module[\"allocateUTF8\"]=function(){abort(\"'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};if(!Object.getOwnPropertyDescriptor(Module,\"allocateUTF8OnStack\"))Module[\"allocateUTF8OnStack\"]=function(){abort(\"'allocateUTF8OnStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")};Module[\"writeStackCookie\"]=writeStackCookie;Module[\"checkStackCookie\"]=checkStackCookie;if(!Object.getOwnPropertyDescriptor(Module,\"ALLOC_NORMAL\"))Object.defineProperty(Module,\"ALLOC_NORMAL\",{configurable:true,get:function(){abort(\"'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")}});if(!Object.getOwnPropertyDescriptor(Module,\"ALLOC_STACK\"))Object.defineProperty(Module,\"ALLOC_STACK\",{configurable:true,get:function(){abort(\"'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)\")}});var calledRun;function ExitStatus(status){this.name=\"ExitStatus\";this.message=\"Program terminated with exit(\"+status+\")\";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}writeStackCookie();preRun();if(runDependencies>0)return;function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();preMain();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();assert(!Module[\"_main\"],'compiled without a main, but one is present. if you added it from JS, use Module[\"onRuntimeInitialized\"]');postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}checkStackCookie()}Module[\"run\"]=run;function checkUnflushedContent(){var print=out;var printErr=err;var has=false;out=err=function(x){has=true};try{var flush=flush_NO_FILESYSTEM;if(flush)flush()}catch(e){}out=print;err=printErr;if(has){warnOnce(\"stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.\");warnOnce(\"(this may also be due to not including full filesystem support - try building with -s FORCE_FILESYSTEM=1)\")}}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}noExitRuntime=true;run();\n\n\n  return createXAtlasModule.ready\n}\n);\n})();\nif (typeof exports === 'object' && typeof module === 'object')\n      module.exports = createXAtlasModule;\n    else if (typeof define === 'function' && define['amd'])\n      define([], function() { return createXAtlasModule; });\n    else if (typeof exports === 'object')\n      exports[\"createXAtlasModule\"] = createXAtlasModule;\n    ", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\nconst proxyMarker = Symbol(\"Comlink.proxy\");\nconst createEndpoint = Symbol(\"Comlink.endpoint\");\nconst releaseProxy = Symbol(\"Comlink.releaseProxy\");\nconst finalizer = Symbol(\"Comlink.finalizer\");\nconst throwMarker = Symbol(\"Comlink.thrown\");\nconst isObject = (val) => (typeof val === \"object\" && val !== null) || typeof val === \"function\";\n/**\n * Internal transfer handle to handle objects marked to proxy.\n */\nconst proxyTransferHandler = {\n    canHandle: (val) => isObject(val) && val[proxyMarker],\n    serialize(obj) {\n        const { port1, port2 } = new MessageChannel();\n        expose(obj, port1);\n        return [port2, [port2]];\n    },\n    deserialize(port) {\n        port.start();\n        return wrap(port);\n    },\n};\n/**\n * Internal transfer handler to handle thrown exceptions.\n */\nconst throwTransferHandler = {\n    canHandle: (value) => isObject(value) && throwMarker in value,\n    serialize({ value }) {\n        let serialized;\n        if (value instanceof Error) {\n            serialized = {\n                isError: true,\n                value: {\n                    message: value.message,\n                    name: value.name,\n                    stack: value.stack,\n                },\n            };\n        }\n        else {\n            serialized = { isError: false, value };\n        }\n        return [serialized, []];\n    },\n    deserialize(serialized) {\n        if (serialized.isError) {\n            throw Object.assign(new Error(serialized.value.message), serialized.value);\n        }\n        throw serialized.value;\n    },\n};\n/**\n * Allows customizing the serialization of certain values.\n */\nconst transferHandlers = new Map([\n    [\"proxy\", proxyTransferHandler],\n    [\"throw\", throwTransferHandler],\n]);\nfunction isAllowedOrigin(allowedOrigins, origin) {\n    for (const allowedOrigin of allowedOrigins) {\n        if (origin === allowedOrigin || allowedOrigin === \"*\") {\n            return true;\n        }\n        if (allowedOrigin instanceof RegExp && allowedOrigin.test(origin)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction expose(obj, ep = globalThis, allowedOrigins = [\"*\"]) {\n    ep.addEventListener(\"message\", function callback(ev) {\n        if (!ev || !ev.data) {\n            return;\n        }\n        if (!isAllowedOrigin(allowedOrigins, ev.origin)) {\n            console.warn(`Invalid origin '${ev.origin}' for comlink proxy`);\n            return;\n        }\n        const { id, type, path } = Object.assign({ path: [] }, ev.data);\n        const argumentList = (ev.data.argumentList || []).map(fromWireValue);\n        let returnValue;\n        try {\n            const parent = path.slice(0, -1).reduce((obj, prop) => obj[prop], obj);\n            const rawValue = path.reduce((obj, prop) => obj[prop], obj);\n            switch (type) {\n                case \"GET\" /* MessageType.GET */:\n                    {\n                        returnValue = rawValue;\n                    }\n                    break;\n                case \"SET\" /* MessageType.SET */:\n                    {\n                        parent[path.slice(-1)[0]] = fromWireValue(ev.data.value);\n                        returnValue = true;\n                    }\n                    break;\n                case \"APPLY\" /* MessageType.APPLY */:\n                    {\n                        returnValue = rawValue.apply(parent, argumentList);\n                    }\n                    break;\n                case \"CONSTRUCT\" /* MessageType.CONSTRUCT */:\n                    {\n                        const value = new rawValue(...argumentList);\n                        returnValue = proxy(value);\n                    }\n                    break;\n                case \"ENDPOINT\" /* MessageType.ENDPOINT */:\n                    {\n                        const { port1, port2 } = new MessageChannel();\n                        expose(obj, port2);\n                        returnValue = transfer(port1, [port1]);\n                    }\n                    break;\n                case \"RELEASE\" /* MessageType.RELEASE */:\n                    {\n                        returnValue = undefined;\n                    }\n                    break;\n                default:\n                    return;\n            }\n        }\n        catch (value) {\n            returnValue = { value, [throwMarker]: 0 };\n        }\n        Promise.resolve(returnValue)\n            .catch((value) => {\n            return { value, [throwMarker]: 0 };\n        })\n            .then((returnValue) => {\n            const [wireValue, transferables] = toWireValue(returnValue);\n            ep.postMessage(Object.assign(Object.assign({}, wireValue), { id }), transferables);\n            if (type === \"RELEASE\" /* MessageType.RELEASE */) {\n                // detach and deactive after sending release response above.\n                ep.removeEventListener(\"message\", callback);\n                closeEndPoint(ep);\n                if (finalizer in obj && typeof obj[finalizer] === \"function\") {\n                    obj[finalizer]();\n                }\n            }\n        })\n            .catch((error) => {\n            // Send Serialization Error To Caller\n            const [wireValue, transferables] = toWireValue({\n                value: new TypeError(\"Unserializable return value\"),\n                [throwMarker]: 0,\n            });\n            ep.postMessage(Object.assign(Object.assign({}, wireValue), { id }), transferables);\n        });\n    });\n    if (ep.start) {\n        ep.start();\n    }\n}\nfunction isMessagePort(endpoint) {\n    return endpoint.constructor.name === \"MessagePort\";\n}\nfunction closeEndPoint(endpoint) {\n    if (isMessagePort(endpoint))\n        endpoint.close();\n}\nfunction wrap(ep, target) {\n    return createProxy(ep, [], target);\n}\nfunction throwIfProxyReleased(isReleased) {\n    if (isReleased) {\n        throw new Error(\"Proxy has been released and is not useable\");\n    }\n}\nfunction releaseEndpoint(ep) {\n    return requestResponseMessage(ep, {\n        type: \"RELEASE\" /* MessageType.RELEASE */,\n    }).then(() => {\n        closeEndPoint(ep);\n    });\n}\nconst proxyCounter = new WeakMap();\nconst proxyFinalizers = \"FinalizationRegistry\" in globalThis &&\n    new FinalizationRegistry((ep) => {\n        const newCount = (proxyCounter.get(ep) || 0) - 1;\n        proxyCounter.set(ep, newCount);\n        if (newCount === 0) {\n            releaseEndpoint(ep);\n        }\n    });\nfunction registerProxy(proxy, ep) {\n    const newCount = (proxyCounter.get(ep) || 0) + 1;\n    proxyCounter.set(ep, newCount);\n    if (proxyFinalizers) {\n        proxyFinalizers.register(proxy, ep, proxy);\n    }\n}\nfunction unregisterProxy(proxy) {\n    if (proxyFinalizers) {\n        proxyFinalizers.unregister(proxy);\n    }\n}\nfunction createProxy(ep, path = [], target = function () { }) {\n    let isProxyReleased = false;\n    const proxy = new Proxy(target, {\n        get(_target, prop) {\n            throwIfProxyReleased(isProxyReleased);\n            if (prop === releaseProxy) {\n                return () => {\n                    unregisterProxy(proxy);\n                    releaseEndpoint(ep);\n                    isProxyReleased = true;\n                };\n            }\n            if (prop === \"then\") {\n                if (path.length === 0) {\n                    return { then: () => proxy };\n                }\n                const r = requestResponseMessage(ep, {\n                    type: \"GET\" /* MessageType.GET */,\n                    path: path.map((p) => p.toString()),\n                }).then(fromWireValue);\n                return r.then.bind(r);\n            }\n            return createProxy(ep, [...path, prop]);\n        },\n        set(_target, prop, rawValue) {\n            throwIfProxyReleased(isProxyReleased);\n            // FIXME: ES6 Proxy Handler `set` methods are supposed to return a\n            // boolean. To show good will, we return true asynchronously ¯\\_(ツ)_/¯\n            const [value, transferables] = toWireValue(rawValue);\n            return requestResponseMessage(ep, {\n                type: \"SET\" /* MessageType.SET */,\n                path: [...path, prop].map((p) => p.toString()),\n                value,\n            }, transferables).then(fromWireValue);\n        },\n        apply(_target, _thisArg, rawArgumentList) {\n            throwIfProxyReleased(isProxyReleased);\n            const last = path[path.length - 1];\n            if (last === createEndpoint) {\n                return requestResponseMessage(ep, {\n                    type: \"ENDPOINT\" /* MessageType.ENDPOINT */,\n                }).then(fromWireValue);\n            }\n            // We just pretend that `bind()` didn’t happen.\n            if (last === \"bind\") {\n                return createProxy(ep, path.slice(0, -1));\n            }\n            const [argumentList, transferables] = processArguments(rawArgumentList);\n            return requestResponseMessage(ep, {\n                type: \"APPLY\" /* MessageType.APPLY */,\n                path: path.map((p) => p.toString()),\n                argumentList,\n            }, transferables).then(fromWireValue);\n        },\n        construct(_target, rawArgumentList) {\n            throwIfProxyReleased(isProxyReleased);\n            const [argumentList, transferables] = processArguments(rawArgumentList);\n            return requestResponseMessage(ep, {\n                type: \"CONSTRUCT\" /* MessageType.CONSTRUCT */,\n                path: path.map((p) => p.toString()),\n                argumentList,\n            }, transferables).then(fromWireValue);\n        },\n    });\n    registerProxy(proxy, ep);\n    return proxy;\n}\nfunction myFlat(arr) {\n    return Array.prototype.concat.apply([], arr);\n}\nfunction processArguments(argumentList) {\n    const processed = argumentList.map(toWireValue);\n    return [processed.map((v) => v[0]), myFlat(processed.map((v) => v[1]))];\n}\nconst transferCache = new WeakMap();\nfunction transfer(obj, transfers) {\n    transferCache.set(obj, transfers);\n    return obj;\n}\nfunction proxy(obj) {\n    return Object.assign(obj, { [proxyMarker]: true });\n}\nfunction windowEndpoint(w, context = globalThis, targetOrigin = \"*\") {\n    return {\n        postMessage: (msg, transferables) => w.postMessage(msg, targetOrigin, transferables),\n        addEventListener: context.addEventListener.bind(context),\n        removeEventListener: context.removeEventListener.bind(context),\n    };\n}\nfunction toWireValue(value) {\n    for (const [name, handler] of transferHandlers) {\n        if (handler.canHandle(value)) {\n            const [serializedValue, transferables] = handler.serialize(value);\n            return [\n                {\n                    type: \"HANDLER\" /* WireValueType.HANDLER */,\n                    name,\n                    value: serializedValue,\n                },\n                transferables,\n            ];\n        }\n    }\n    return [\n        {\n            type: \"RAW\" /* WireValueType.RAW */,\n            value,\n        },\n        transferCache.get(value) || [],\n    ];\n}\nfunction fromWireValue(value) {\n    switch (value.type) {\n        case \"HANDLER\" /* WireValueType.HANDLER */:\n            return transferHandlers.get(value.name).deserialize(value.value);\n        case \"RAW\" /* WireValueType.RAW */:\n            return value.value;\n    }\n}\nfunction requestResponseMessage(ep, msg, transfers) {\n    return new Promise((resolve) => {\n        const id = generateUUID();\n        ep.addEventListener(\"message\", function l(ev) {\n            if (!ev.data || !ev.data.id || ev.data.id !== id) {\n                return;\n            }\n            ep.removeEventListener(\"message\", l);\n            resolve(ev.data);\n        });\n        if (ep.start) {\n            ep.start();\n        }\n        ep.postMessage(Object.assign({ id }, msg), transfers);\n    });\n}\nfunction generateUUID() {\n    return new Array(4)\n        .fill(0)\n        .map(() => Math.floor(Math.random() * Number.MAX_SAFE_INTEGER).toString(16))\n        .join(\"-\");\n}\n\nexport { createEndpoint, expose, finalizer, proxy, proxyMarker, releaseProxy, transfer, transferHandlers, windowEndpoint, wrap };\n//# sourceMappingURL=comlink.mjs.map\n", "import {expose} from \"comlink\";\nimport {Api} from \"./api.mjs\"\nimport createXAtlasModule from \"./build/xatlas.js\"\n\nexpose(Api(createXAtlasModule));\n", "export const Api = (createXAtlasModule)=>{\n    let _onLoad = ()=>{} //  we cannot put it in the object, otherwise we cannot access it from the outside\n    return class XatlasApi {\n\n        /**\n         * @param onLoad {Function}\n         * @param locateFile {Function} - should return path for xatlas.wasm, default is root of domain\n         * @param onAtlasProgress {Function} - called on progress update with mode {ProgressCategory} and counter\n         */\n        constructor(onLoad, locateFile, onAtlasProgress) {\n            this.xatlas = null;\n            this.loaded = false;\n            _onLoad = onLoad || (()=>{});\n            this.atlasCreated = false;\n            /**\n             * @type {{meshId: number, vertices: Float32Array, normals: Float32Array|null, coords: Float32Array|null, meshObj: any}[]}\n             */\n            this.meshes = [];\n            let params = {};\n            if (onAtlasProgress) params = {...params, onAtlasProgress};\n            const ctor = (loc)=>{\n                params = {...params, locateFile: ((path, dir)=> ( (loc && path === \"xatlas.wasm\") ? loc : dir+path) ) };\n                createXAtlasModule(params).then(m=>{this.moduleLoaded(m)});\n            }\n            if (locateFile) {\n                const pp = locateFile(\"xatlas.wasm\", \"\") // separately because it can return a promise\n                if (pp&&pp.then) pp.then(ctor);\n                else ctor(pp);\n            }else ctor()\n        }\n\n        moduleLoaded(mod){\n            this.xatlas = mod;\n            this.loaded = true;\n            if(_onLoad) _onLoad();\n        }\n\n        createAtlas(){\n            this.xatlas.createAtlas();\n            this.meshes = [];\n            this.atlasCreated = true;\n        }\n\n        /**\n         *\n         * @param indexes {Uint16Array}\n         * @param vertices {Float32Array}\n         * @param normals {Float32Array}\n         * @param coords {Float32Array}\n         * @param meshObj {any} - identifier for the mesh (uuid)\n         * @param useNormals {boolean}\n         * @param useCoords {boolean}\n         * @param scale {number|[number, number, number]}\n         * @return {null | {indexes: (Float32Array | null), vertices: Float32Array, normals: (Float32Array | null), meshId: number, coords: (Float32Array | null), meshObj: any}}\n         */\n        addMesh(indexes, vertices, normals=null, coords=null, meshObj=undefined, useNormals = false, useCoords = false, scale =1){\n            if(!this.loaded || !this.atlasCreated) throw \"Create atlas first\";\n            const meshDesc = this.xatlas.createMesh(vertices.length / 3, indexes.length, normals != null && useNormals, coords != null && useCoords)\n            this.xatlas.HEAPU32.set(indexes, meshDesc.indexOffset/4);\n\n            const vs = new Float32Array([...vertices])\n            if(scale!==1) {\n                if(typeof scale === \"number\") scale = [scale, scale, scale]\n                for (let i = 0, l = vs.length; i < l; i+=3) {\n                    vs[i] *= scale[0];\n                    vs[i+1] *= scale[1];\n                    vs[i+2] *= scale[2];\n                }\n            }\n\n            this.xatlas.HEAPF32.set(vs, meshDesc.positionOffset/4);\n            if(normals != null && useNormals) this.xatlas.HEAPF32.set(normals, meshDesc.normalOffset/4);\n            if(coords != null && useCoords) this.xatlas.HEAPF32.set(coords, meshDesc.uvOffset/4);\n            const addMeshRes = this.xatlas.addMesh()\n            // this.xatlas._free(meshDesc.indexOffset); // should be done on c++ side\n            // this.xatlas._free(meshDesc.positionOffset);\n            if(addMeshRes !== 0) {\n                console.log(\"Error adding mesh: \", addMeshRes);\n                return null;\n            }\n            const ret = {\n                meshId: meshDesc.meshId,\n                meshObj: meshObj,\n                vertices: vertices,\n                normals: normals || null,\n                indexes: indexes || null,\n                coords: coords || null,\n            }\n            this.meshes.push(ret);\n            return ret;\n        }\n\n        /**\n         * @param vertexCount\n         * @param indexCount\n         * @param normals\n         * @param coords\n         * @return {{meshId: number, indexOffset: number, positionOffset: number, normalOffset: number, uvOffset: number, meshObj: any}}\n         */\n        createMesh(vertexCount, indexCount, normals, coords){\n            return this.xatlas.createMesh(vertexCount, indexCount, normals, coords);\n        }\n\n        // createUvMesh(vertexCount, indexCount){\n        //     return this.xatlas.createUvMesh(vertexCount, indexCount);\n        // }\n\n        /**\n         * Result in coords1, input coords in coords\n         * @param chartOptions {{maxIterations: number, straightnessWeight: number, textureSeamWeight: number, maxChartArea: number, normalDeviationWeight: number, roundnessWeight: number, maxCost: number, maxBoundaryLength: number, normalSeamWeight: number}}\n         * @param packOptions {{maxChartSize: number, padding: number, bilinear: boolean, createImage: boolean, blockAlign: boolean, resolution: number, bruteForce: boolean, texelsPerUnit: number}}\n         * @param returnMeshes {boolean} - default = true\n         */\n        generateAtlas(chartOptions, packOptions, returnMeshes = true){\n            if(!this.loaded || !this.atlasCreated) throw \"Create atlas first\";\n            if(this.meshes.length < 1) throw \"Add meshes first\";\n            chartOptions = { ...this.defaultChartOptions(), ...chartOptions};\n            packOptions = { ...this.defaultPackOptions(), ...packOptions };\n            this.xatlas.generateAtlas(chartOptions, packOptions);\n            if(!returnMeshes) return [];\n            return this.getAtlas()\n        }\n\n        getAtlas() {\n            const returnVal = []\n            if(!this.loaded || !this.atlasCreated) throw \"Create atlas first\";\n            const _atlas = this.xatlas.getAtlas()\n            const atlas = {\n                width: _atlas.width,\n                height: _atlas.height,\n                atlasCount: _atlas.atlasCount,\n                meshCount: _atlas.meshCount,\n                // chartCount: atlas.chartCount,\n                // utilization: atlas.utilization,\n                // meshes: this.getMeshData\n                texelsPerUnit: _atlas.texelsPerUnit,\n                // image:\n            }\n            for (const {meshId, meshObj, vertices, normals, coords} of this.meshes) {\n                const ret = this.getMeshData(meshId)\n                const vCount = ret.newVertexCount;\n                const index = new Uint32Array(this.xatlas.HEAPU32.subarray(ret.indexOffset / 4, ret.indexOffset / 4 + ret.newIndexCount))\n                const oldIndexes = new Uint32Array(this.xatlas.HEAPU32.subarray(ret.originalIndexOffset / 4, ret.originalIndexOffset / 4 + vCount))\n                // const atlasIndexes = atlas.atlasCount > 1 ? new Uint16Array(this.xatlas.HEAPU32.subarray(ret.atlasIndexOffset / 4, ret.atlasIndexOffset / 4 + ret.newIndexCount)) : null;\n                const xcoords = new Float32Array(this.xatlas.HEAPF32.subarray(ret.uvOffset / 4, ret.uvOffset / 4 + vCount * 2))\n\n                const subMeshes = [];\n                for (let i = 0, n=ret.subMeshes.size(); i < n; i++) {\n                    subMeshes.push(ret.subMeshes.get(i));\n                }\n                this.xatlas.destroyMeshData(ret);\n                ret.subMeshes.delete();\n\n                const vertex = {}\n                vertex.vertices = new Float32Array(vCount * 3);\n                vertex.coords1 = xcoords;\n                if (normals)\n                    vertex.normals = new Float32Array(vCount * 3);\n                if (coords)\n                    vertex.coords = new Float32Array(vCount * 2);\n                else vertex.coords = vertex.coords1;\n\n                for (let i = 0, l = vCount; i < l; i++) {\n                    const oldIndex = oldIndexes[i]\n                    vertex.vertices[3 * i + 0] = vertices[3 * oldIndex + 0];\n                    vertex.vertices[3 * i + 1] = vertices[3 * oldIndex + 1];\n                    vertex.vertices[3 * i + 2] = vertices[3 * oldIndex + 2];\n                    if (vertex.normals && normals) {\n                        vertex.normals[3 * i + 0] = normals[3 * oldIndex + 0];\n                        vertex.normals[3 * i + 1] = normals[3 * oldIndex + 1];\n                        vertex.normals[3 * i + 2] = normals[3 * oldIndex + 2];\n                    }\n                    if (vertex.coords && coords) {\n                        vertex.coords[2 * i + 0] = coords[2 * oldIndex + 0];\n                        vertex.coords[2 * i + 1] = coords[2 * oldIndex + 1];\n                    }\n                }\n                returnVal.push({\n                    index: index,\n                    vertex: vertex,\n                    mesh: meshObj,\n                    vertexCount: vCount,\n                    oldIndexes: oldIndexes,\n                    subMeshes: subMeshes\n                });\n            }\n            return {...atlas, meshes: returnVal};\n            // return returnVal;\n        }\n\n        defaultChartOptions() {\n            return {\n                fixWinding: false,\n                maxBoundaryLength: 0,\n                maxChartArea: 0,\n                maxCost: 2,\n                maxIterations: 1,\n                normalDeviationWeight: 2,\n                normalSeamWeight: 4,\n                roundnessWeight: 0.009999999776482582,\n                straightnessWeight: 6,\n                textureSeamWeight: 0.5,\n                useInputMeshUvs: false,\n            };\n        }\n\n        defaultPackOptions() {\n            return {\n                bilinear: true,\n                blockAlign: false,\n                bruteForce: false,\n                createImage: false,\n                maxChartSize: 0,\n                padding: 0,\n                resolution: 0,\n                rotateCharts: true,\n                rotateChartsToAxis: true,\n                texelsPerUnit: 0\n            };\n        }\n\n        setProgressLogging(flag){\n            this.xatlas.setProgressLogging(flag);\n        }\n\n        /**\n         * @param meshId\n         * @return {{newVertexCount: number, newIndexCount: number, indexOffset: number, originalIndexOffset: number, uvOffset: number, atlasIndexOffset: number}}\n         */\n        getMeshData(meshId){\n            return this.xatlas.getMeshData(meshId);\n        }\n\n        /**\n         * @param data {{newVertexCount: number, newIndexCount: number, indexOffset: number, originalIndexOffset: number, uvOffset: number, atlasIndexOffset: number}}\n         * @return {*}\n         */\n        destroyMeshData(data){\n            this.xatlas.destroyMeshData(data);\n        }\n\n        destroyAtlas(){\n            this.atlasCreated = false;\n            this.xatlas.destroyAtlas();\n            this.meshes = [];\n            this.xatlas.doLeakCheck();\n        }\n\n    }\n};\n"], "names": ["_scriptDir", "createXAtlasModule", "document", "currentScript", "src", "undefined", "readyPromiseResolve", "readyPromiseReject", "<PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "Object", "getOwnPropertyDescriptor", "defineProperty", "configurable", "get", "abort", "set", "key", "moduleOverrides", "hasOwnProperty", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "arguments_", "window", "importScripts", "process", "versions", "node", "Error", "readBinary", "scriptDirectory", "self", "location", "href", "indexOf", "substr", "lastIndexOf", "url", "xhr", "XMLHttpRequest", "open", "responseType", "send", "Uint8Array", "response", "wasmBinary", "was<PERSON><PERSON><PERSON><PERSON>", "wasmTable", "out", "console", "log", "bind", "err", "warn", "warnOnce", "text", "shown", "assert", "WebAssembly", "ABORT", "condition", "UTF8Decoder", "TextDecoder", "UTF8ArrayToString", "heap", "idx", "maxBytesToRead", "endIdx", "endPtr", "subarray", "decode", "str", "u0", "u1", "u2", "toString", "String", "fromCharCode", "ch", "UTF8ToString", "ptr", "HEAPU8", "UTF16Decoder", "UTF16ToString", "maxIdx", "HEAPU16", "i", "codeUnit", "HEAP16", "stringToUTF16", "outPtr", "maxBytesToWrite", "startPtr", "numCharsToWrite", "length", "charCodeAt", "lengthBytesUTF16", "UTF32ToString", "utf32", "HEAP32", "stringToUTF32", "lengthBytesUTF32", "len", "buffer", "HEAP8", "HEAPU32", "HEAPF32", "HEAPF64", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "STACK_MAX", "TOTAL_STACK", "INITIAL_INITIAL_MEMORY", "writeStackCookie", "checkStackCookie", "cookie1", "cookie2", "prototype", "Memory", "byteLength", "h16", "h8", "__ATPRERUN__", "__ATINIT__", "__ATMAIN__", "__ATPOSTRUN__", "runtimeInitialized", "Math", "imul", "fround", "clz32", "trunc", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "runDependencyTracking", "what", "regex", "js", "output", "error", "stack", "e", "jsStackTrace", "replace", "x", "func", "y", "RuntimeError", "FS", "init", "createDataFile", "createPreloadedFile", "createLazyFile", "mkdev", "registerDevice", "analyzePath", "loadFilesFromDB", "ErrnoError", "isDataURI", "filename", "prefix", "startsWith", "createExportWrapper", "name", "fixedasm", "displayName", "asm", "apply", "arguments", "path", "wasmBinaryFile", "getBinary", "callRuntimeCallbacks", "callbacks", "callback", "shift", "arg", "ExceptionInfoAttrs", "ExceptionInfo", "excPtr", "this", "set_type", "type", "get_type", "set_destructor", "destructor", "get_destructor", "set_refcount", "refcount", "set_caught", "caught", "get_caught", "set_rethrown", "rethrown", "get_rethrown", "add_ref", "value", "release_ref", "prev", "__ZSt18uncaught_exceptionv", "uncaught_exceptions", "structRegistrations", "runDestructors", "destructors", "pop", "del", "simpleReadValueFromPointer", "pointer", "awaitingDependencies", "registeredTypes", "typeDependencies", "char_0", "char_9", "makeLegalFunctionName", "f", "createNamedFunction", "body", "Function", "extendError", "baseErrorType", "errorName", "errorClass", "message", "create", "constructor", "InternalError", "throwInternalError", "whenDependentTypesAreResolved", "myTypes", "dependentTypes", "getTypeConverters", "onComplete", "typeConverters", "myTypeConverters", "registerType", "for<PERSON>ach", "Array", "unregisteredTypes", "registered", "dt", "push", "getShiftFromSize", "size", "TypeError", "embind_charCodes", "readLatin1String", "ret", "c", "BindingError", "throwBindingError", "rawType", "registeredInstance", "options", "ignoreDuplicateRegistrations", "cb", "throwInstanceAlreadyDeleted", "obj", "$$", "ptrType", "registeredClass", "finalizationGroup", "detachFinalizer", "handle", "releaseClassHandle", "count", "smartPtr", "smartPtrType", "rawDestructor", "runDestructor", "attachFinalizer", "FinalizationGroup", "iter", "result", "next", "done", "register", "unregister", "delayFunction", "deletionQueue", "flushPendingDeletes", "deleteScheduled", "ClassHandle", "registeredPointers", "ensureOverloadTable", "proto", "methodName", "humanName", "overloadTable", "prevFunc", "argCount", "exposePublicSymbol", "numArguments", "RegisteredClass", "instancePrototype", "baseClass", "getActualType", "upcast", "downcast", "pureVirtualFunctions", "upcastPointer", "ptrClass", "desiredClass", "constNoSmartPtrRawPointerToWireType", "isReference", "_embind_repr", "handleClass", "genericPointerToWireType", "isSmartPointer", "rawConstructor", "isConst", "sharingPolicy", "cloned<PERSON><PERSON><PERSON>", "rawShare", "__emval_register", "nonConstNoSmartPtrRawPointerToWireType", "downcastPointer", "rv", "registeredInstances", "makeClassHandle", "record", "RegisteredPointer", "pointeeType", "rawGetPointee", "destructorFunction", "replacePublicSymbol", "embind__requireFunction", "signature", "rawFunction", "fp", "sig", "<PERSON><PERSON><PERSON><PERSON>", "args", "substring", "concat", "call", "dynCallLegacy", "dynCall", "getDynCaller", "UnboundTypeError", "getTypeName", "___getTypeName", "_free", "throwUnboundTypeError", "types", "unboundTypes", "seen", "visit", "map", "join", "heap32VectorToArray", "firstElement", "array", "craftInvokerFunction", "argTypes", "classType", "cppInvokerFunc", "cppTargetFunc", "isClassMethodFunc", "needsDestructorStack", "returns", "argsList", "argsListWired", "invokerFnBody", "dtorStack", "args1", "args2", "paramName", "argumentList", "dummy", "r", "new_", "emval_free_list", "emval_handle_array", "__emval_decref", "v", "t", "floatReadValueFromPointer", "integerReadValueFromPointer", "signed", "emscripten_realloc_buffer", "grow", "SYSCALLS", "mappings", "buffers", "printChar", "stream", "curr", "varargs", "getStr", "get64", "low", "high", "codes", "embind_init_charCodes", "other", "leftClass", "left", "rightClass", "right", "preservePointerOnDelete", "o", "clone", "getPrototypeOf", "getPointee", "rawPointer", "class_", "getB<PERSON>tPoint<PERSON>", "getInheritedInstance", "makeDefaultHandle", "toType", "actualType", "registeredPointerRecord", "constPointerType", "pointerType", "dp", "keys", "k", "fn", "___wasm_call_ctors", "calledRun", "asmLibraryArg", "_malloc", "structType", "reg", "fieldRecords", "fields", "field", "getterReturnType", "setterArgumentType", "fieldTypes", "fieldName", "getter", "getterContext", "setter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read", "write", "trueValue", "falseValue", "wt", "rawPointerType", "rawConstPointerType", "baseClassRawType", "getActualTypeSignature", "upcastSignature", "downcastSignature", "destructorSignature", "legalFunctionName", "base", "basePrototype", "constructor_body", "referenceConverter", "pointerConverter", "constPointerConverter", "rawClassType", "rawArgTypesAddr", "invokerSignature", "invoker", "rawArgTypes", "rawInvoker", "context", "isPureVirtual", "unboundTypes<PERSON><PERSON>ler", "method", "className", "memberFunction", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "primitiveType", "minRange", "max<PERSON><PERSON><PERSON>", "fromWireType", "bitshift", "isUnsignedType", "dataTypeIndex", "TA", "decodeMemoryView", "data", "stdStringIsUTF8", "decodeStartPtr", "currentBytePtr", "stringSegment", "a", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueIsOfTypeString", "Uint8ClampedArray", "u", "lengthBytesUTF8", "outIdx", "startIdx", "stringToUTF8Array", "charCode", "charSize", "decodeString", "encodeString", "getHeap", "lengthBytesUTF", "HEAP", "constructorSignature", "getterSignature", "setterSignature", "isVoid", "argv", "impl", "dest", "num", "copyWithin", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "min", "newSize", "max", "fd", "iov", "iovcnt", "pnum", "j", "mode", "progress", "onAtlasProgress", "$i", "id", "info", "receiveInstance", "instance", "module", "exports", "clearInterval", "removeRunDependency", "setInterval", "dep", "trueModule", "receiveInstantiatedSource", "instantiateArrayBuffer", "receiver", "fetch", "then", "credentials", "catch", "binary", "instantiate", "reason", "instantiateStreaming", "instantiateAsync", "createWasm", "run", "doRun", "unshift", "postRun", "preRun", "setTimeout", "runCaller", "ready", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "n", "__esModule", "d", "definition", "enumerable", "prop", "proxy<PERSON><PERSON><PERSON>", "Symbol", "createEndpoint", "releaseProxy", "finalizer", "<PERSON><PERSON><PERSON><PERSON>", "isObject", "val", "transferHandlers", "Map", "canHandle", "serialize", "port1", "port2", "MessageChannel", "expose", "deserialize", "port", "start", "createProxy", "target", "serialized", "isError", "assign", "ep", "globalThis", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "ev", "origin", "<PERSON><PERSON><PERSON><PERSON>", "RegExp", "test", "isAllowedOrigin", "fromWireValue", "returnValue", "parent", "reduce", "rawValue", "proxy", "transfers", "transferCache", "transfer", "wireValue", "transferables", "toWireValue", "postMessage", "removeEventListener", "closeEndPoint", "endpoint", "isMessagePort", "close", "throwIfProxyReleased", "isReleased", "releaseEndpoint", "requestResponseMessage", "proxyCounter", "WeakMap", "proxyFinalizers", "FinalizationRegistry", "newCount", "isProxyReleased", "Proxy", "_target", "unregisterProxy", "p", "_thisArg", "rawArgumentList", "last", "processArguments", "construct", "registerProxy", "processed", "arr", "handler", "serializedValue", "msg", "fill", "floor", "random", "Number", "MAX_SAFE_INTEGER", "l", "_onLoad", "onLoad", "locateFile", "xatlas", "loaded", "atlasCreated", "meshes", "params", "ctor", "loc", "dir", "m", "moduleLoaded", "pp", "mod", "createAtlas", "<PERSON><PERSON><PERSON>", "indexes", "vertices", "normals", "coords", "meshObj", "useNormals", "useCoords", "scale", "meshDesc", "<PERSON><PERSON><PERSON>", "indexOffset", "vs", "positionOffset", "normalOffset", "uvOffset", "addMeshRes", "meshId", "vertexCount", "indexCount", "generateAtlas", "chartOptions", "packOptions", "<PERSON><PERSON><PERSON><PERSON>", "defaultChartOptions", "defaultPackOptions", "getAtlas", "returnVal", "_atlas", "atlas", "width", "height", "atlasCount", "meshCount", "texelsPerUnit", "getMeshData", "vCount", "newVertexCount", "index", "newIndexCount", "oldIndexes", "originalIndexOffset", "xcoords", "subMeshes", "destroyMeshData", "delete", "vertex", "coords1", "oldIndex", "mesh", "fixWinding", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "maxCost", "maxIterations", "normalDeviationWeight", "normalSeamWeight", "roundnessWeight", "straightnessWeight", "textureSeamWeight", "useInputMeshUvs", "bilinear", "blockAlign", "bruteForce", "createImage", "maxChartSize", "padding", "resolution", "<PERSON><PERSON><PERSON><PERSON>", "rotateChartsToAxis", "setProgressLogging", "flag", "destroyAtlas", "doLeakCheck", "Api"], "sourceRoot": ""}