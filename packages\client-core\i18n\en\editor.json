{"loading": "Loading Project", "loadingNode": "Loading {{name}} Editor...", "loadingError": "Error loading Project", "loadingErrorMsg": "An error occurred when loading the Project. Please try again or contact support.", "selectSceneMsg": "Select a Scene in the Project to start", "error": "Error", "errorMsg": "An unknown error occurred. Please try again or contact support.", "warning": "Warning", "warningMsg": "I don't think you should do that.", "generateScreenshot": "Generating Project screenshot", "newScene": "Add Scene", "saving": "Saving Project", "savingError": "Error saving Project", "savingErrorMsg": "An error occurred when saving the Project. Please try again or contact support.", "savingSceneErrorMsg": "The Scene did not load properly. Please try reloading the Scene.", "savingLoadingSceneErrorMsg": "The Scene is not finished loading. Please wait for it to finish loading before saving.", "duplicating": "Duplicating Project", "exporting": "Exporting Project", "exportingError": "Error exporting the Project", "exportingErrorMsg": "An error occurred when exporting the Project. Please try again or contact support.", "importLegacy": "Import Legacy World", "importLegacyMsg": "Warning! This will overwrite your existing Scene. Are you sure you wish to continue?", "publishingError": "Error publishing Project", "publishingErrorMsg": "An unknown error occurred. Please try again or contact support.", "publishingScene": "Publishing Scene", "publishingSceneMsg": "Publishing Scene...", "uploadingThumbnailMsg": "Uploading Thumbnail...", "uploadingSceneMsg": "Uploading Scene: {{percentage}}%", "envMapError": "Error loading Cubemap images {{files}}", "lbl-return": "Return", "loadingAssets": "Loading Assets", "loadingScenes": "Loading Scenes", "loadingScenesWithProgress": "Scene Loading... {{progress}}% ({{assetsLeft}} assets left)", "help": "Customer Service Live Chat", "menubar": {"newScene": "New Scene", "newEmptyScene": "New Empty Scene", "saveScene": "Save Scene", "saveAs": "Save As", "importAsset": "Import Asset", "importSettings": "Import Settings", "exportGLB": "Export as binary glTF (.glb) ...", "exportLookdev": "Export Lookdev Prefab", "documentation": "Documentation", "importScene": "Import Scene", "exportScene": "Export Scene", "quit": "Quit to Dashboard"}, "errors": {"fileNotSupported": "File \"{{file}}\" is unsupported. Reason: {{errorMessage}}", "addObject": "addObject: Before object not found", "removeObject": "removeObject: Node not found. The node is not present in the Scene.", "noParent": "{{node}} \"{{name}}\" has no parent. Reparenting only works on objects that are currently in the Scene.", "undefinedParent": "editor.reparent: new<PERSON><PERSON>nt is undefined", "notAuthenticated": "Not Authenticated", "fetchingProjectError": "Error fetching Projects: {{error}}", "fetchingRouteError": "Error fetching Routes: {{error}}", "unknownError": "Unknown error", "resolveURL": "Error resolving URL \"{{url}}\":\n", "mediaSearchAborted": "Media search aborted", "methodNotImplemented": "Method not implemented.", "saveProjectAborted": "Save Scene aborted", "sceneAlreadyExists": "A Scene with this name already exists.", "unsavedChanges": "Unsaved Changes", "unsavedChangesMsg": "The Scene has been modified. Please save your changes.", "projectCreationFail": "Project creation failed. {{reason}}", "projectDeletionFail": "Project deletion failed. {{reason}}", "savingProjectFail": "Saving Project failed. {{reason}}", "sceneTooLarge": "The Scene is too large ({{size}} MB) to publish. Maximum size is {{maxSize}} MB.", "sceneCreationFail": "Scene creation failed. {{reason}}", "uploadAborted": "Upload aborted", "uploadFailed": "Upload failed {{reason}}.", "fileUploadFailed": "File upload failed; {{reason}}", "fileUploadFailedMultiple": "Mulitple file uploads failed.. {{reason}}", "fileNameInvalidMultiple": "<PERSON><PERSON>ple invalid file names.. {{reason}}", "assetDeletionFail": "Asset deletion failed. {{reason}}", "projectAssetDeletionFail": "Project Asset deletion failed. {{reason}}", "networkError": "Network Error: {{status}}. {{text}}", "unknownStatus": "Unknown Status", "CORS": "Possibly a CORS error", "urlFetchError": "Failed to fetch \"{{url}}\"", "invalidSceneName": "Scene name must be 4-64 characters long, using only alphanumeric characters, underscores, hyphens, and periods, and begin and end with an alphanumeric.", "maxUploadFileSizeExceed": "That file size is too large. Please upload a file under {{maxFileSizeToUploadMB}}MB. The following files are too large: {{filesNames}}", "unsupported-3D-file": "Please upload either a .gltf or a .glb.", "unsupported-image-file": "Please upload a .png, .jpg, .jpeg, or .ktx2.", "unsupported-audio-file": "Please upload a .mp3, .mpeg, .m4a, or .wav.", "unsupported-video-file": "Please upload a .mp4, .mkv, or .avi.", "unsupported-unknown-file": "Please upload a valid 3D, Image, Audio, or Video file."}, "warnings": {"addChildToGlbError": "Re-parenting was canceled - Adding children to glb model prefabs is currently not supported.", "rigidbodyDropWarning": "Re-parenting was canceled - Entities with a rigidbody are not allowed to have an ancestor with a rigidbody. Please remove one of the rigidbodies and try again.", "sceneComplexity": "Scene complexity is {{sceneComplexity}}. Consider optimizing your Scene for better performance. Refer to our docs for optimization guidelines."}, "viewport": {"title": "Viewport", "command": {"movecamera": "Move Camera", "flyFast": "Fly faster", "orbit": "Orbit / Select", "pan": "Pan", "fly": "Fly", "focus": "Focus", "rotateLeft": "Rotate Left", "rotateRight": "Rotate Right", "cancelPlacement": "Cancel Placement", "placeDuplicate": "Place Duplicate", "deselectAll": "Deselect All"}, "state": {"header": "Stats", "memory": "Memory", "geometries": "Geometries", "textures": "Textures", "texturesMB": "Textures MB", "render": "Render", "FPS": "FPS", "frameTime": "Frame Time", "calls": "Calls", "sceneTriangles": "Scene Triangles", "triangles": "Render Triangles", "trianglesTooltip": "Number of triangles currently being rendered. Shadows and post processing effects will affect this number.", "points": "Points", "lines": "Lines", "shaderComplexity": "Shader Complexity", "sceneComplexity": "Scene Complexity", "lights": "Lights"}}, "toolbar": {"lbl-publish": "Publish & Save", "lbl-published": "Published", "lbl-simple": "Simple", "lbl-advanced": "Advanced", "gizmo": {"pointer": "Pointer", "marquee": "Disable orbit camera and enable selection box", "description": "Transform Gizmo", "translate": "[W] Translate", "rotate": "[E] Rotate", "scale": "[R] Scale", "combined": "[U] Combined Transform"}, "transformSpace": {"description": "Sets your transform control to be oriented to the object (selection) or World", "lbl-world": "World", "lbl-space": "Space", "lbl-global": "[Z] Global", "lbl-local": "[Z] Local", "info-world": "World Space relates to the entire Scene's orientation", "lbl-selection": "Object", "info-selection": "Object Space (your selection) relates to transforms made to a specific object in relation to the World Space.", "lbl-toggleTransformSpace": "[Z] Toggle Transform Space"}, "transformPivot": {"toggleTransformPivot": "[X] Toggle Transform Pivot", "lbl-pivot": "Pivot", "lbl-singleOrigin": "Single Origin", "info-singleOrigin": "The center pivot of the first entity you selected in the sequence.", "lbl-center": "Bounding Box Center", "info-center": "A pivot that uses the center point of a bounding box containing all selected entities' positions.", "lbl-floor": "Bounding Box Floor Center", "info-floor": "A pivot that sits at the center of the bottom face of a bounding box containing all selected entities' positions.", "lbl-worldOrigin": "World Origin", "info-worldOrigin": "Sets the pivot mode to the world origin (0,0,0)."}, "transformSnapTool": {"lbl-snaps": "Snaps", "info-snaps": "Move, Scale, and Rotate snapping with adjustable increments", "lbl-translate": "Translate", "info-translate": "Snap translation size (meters)", "lbl-scale": "Scale", "info-scale": "Snap scale size", "lbl-rotate": "Rotate", "info-rotate": "Snap rotation angle", "toggleBBoxSnap": "[B] Toggle Bounding Box Snap", "toggleSnapMode": "[C] Toggle <PERSON><PERSON>nap"}, "placement": {"click": "Point & Click Placement", "drag": "Drag & Drop Placement"}, "command": {"translate": "Translate", "rotate": "Rotate", "scale": "Scale", "toggleSpace": "Toggle Transform Space", "togglePivot": "Toggle Transform Pivot", "toggleSnapMode": "Toggle Snap Mode", "toggleGrid": "Toggle Grid Visibility", "increaseGridSize": "Increase Grid <PERSON>", "descreaseGridSize": "Decrease <PERSON><PERSON>", "lbl-stopPreview": "Stop Previewing Scene", "info-stopPreview": "Remove your Avatar from the Scene", "lbl-playPreview": "Preview Scene", "info-playPreview": "Spawns you into the Scene"}, "instance": {"none": "None", "users": "Users", "user": "User"}, "render-settings": {"lbl": "Render Settings", "info": "How you view Materials in the Engine", "lbl-usePostProcessing": "Use Post Processing", "info-usePostProcessing": "Enables Post Processing", "lbl-shadowMapResolution": "Shadow Map Resolution", "info-shadowMapResolution": "Set Shadow Map resolution", "lbl-shadows": "Shadows", "lbl-renderMode": "Render Mode", "info-renderMode": "<PERSON><PERSON>"}, "grid": {"info-toggleGridVisibility": "Toggle Grid Visibility", "info-gridSpacing": "Set Grid Spacing (meters)", "info-gridHeight": "Set Grid Height (meters)", "info-incrementHeight": "Increment Grid Height", "info-decrementHeight": "Decrement Grid Height"}, "stats": {"lbl": "Toggle Stats", "info": "Show stats about the Scene and provide an overview of how optimized your Scene is."}, "helpersToggle": {"lbl-helpers": "Helpers", "info-helpers": "View hidden information about your Scene, e.g., colliders.", "lbl-nodeHelpers": "Toggle Node Helpers", "info-nodeHelpers": "Helper geometry that helps components have visibility in the scene when inactive.", "lbl-nodeIcons": "Toggle Node Icons", "lbl-nodeVolume": "Toggle Node Volume", "info-nodeVolume": "Helper geometry that helps visualize bounding box of assets in the scene", "lbl-helperIcons": "Helpers Icons", "lbl-helperVolumes": "Helpers Volumes", "info-helperVolumes": "Helper geometry that helps components have visibility in the Scene when inactive.", "lbl-axisHelpers": "Axis Helpers", "lbl-colliderHelpers": "Collider Helpers", "lbl-directManipulation": "Direct Manipulation"}, "sceneScreenshot": {"lbl": "Screenshot", "info": "Takes a screenshot of your Scene at the current view."}, "publishLocation": {"title": "Publish", "create": "Publish Space", "update": "Update Published Space", "unpublish": "Unpublish", "saveAndPublish": "Save and Publish", "locationLinkCopied": "Space link copied", "publishDate": "✓ Published {{formattedDate}} at {{formattedTime}}", "notYetPublished": "Not Published Yet", "multiplayerFeatures": "Multiplayer Features", "multiplayerDescription": "Choose which features visitors can use when visiting your Published World with other users.", "copy": "Copy", "copyPublicUrl": "Copy Public URL", "publicUrl": "Public URL", "publishSuccess": "Space Published Successfully", "noThumbnailPlaceholder": "Publish Scene to Generate a Preview Image", "createCompressedScenePublish": "Compress and Publish", "createCompressedScenePublishInfo": "This is a beta feature that optimizes your space for mobile devices. If you encounter errors, please refresh your browser and try again, or use the regular “Publish” function for now.", "jumpControlFeature": "Jump Control", "jumpControlFeatureDesc": "Enable your avatar’s ability to jump.", "vrCapabilitiesFeature": "VR Capabilities", "vrCapabilitiesFeatureDesc": "Enable your eligible devices to render AR experiences."}}, "properties": {"title": "Properties", "info": "Properties let you access and edit detailed information about objects in the Scene.", "lbl-visible": "Visible", "lbl-preventBake": "Prevent Bake", "lbl-dynamicLoad": "Load Children Dynamically", "lbl-dynamicLoadDistance": "Distance", "lbl-addComponent": "Add Component", "multipleNodeSelected": "Multiple nodes of different types selected.", "noNodeSelected": "No node selected.", "ambientLight": {"name": "Ambient Light", "description": "A light that illuminates all objects in your Space.", "lbl-intensity": "Intensity", "lbl-color": "Color"}, "envmap": {"name": "Environment Map", "description": "Environment Map properties", "lbl-source": "Environment Map Source", "lbl-color": "Environment Map Color", "lbl-bake": "Environment Map Bake", "lbl-textureType": "Texture Type", "lbl-textureUrl": "Texture URL", "lbl-intensity": "Envmap Intensity", "info-intensity": "Visual intensity may vary depending on material, shader, and environment map type configuration."}, "trigger": {"name": "<PERSON><PERSON>", "description": "A Trigger component fires callbacks on the target when a Collider enters and exits the Trigger Volume Collider.", "lbl-target": "Target", "lbl-onenter": "On Enter", "lbl-onexit": "On Exit"}, "rigidbody": {"name": "Rigidbody", "description": "A Rigidbody component that allows the entity to be affected by physics.", "lbl-type": "Body Type", "duplicateWarning": "A Rigidbody component already exists in the current entity hierarchy, removing it from entity {{entity}} with name {{name}}."}, "prefab": {"name": "Prefab", "description": "A grouping of Scene components which can be dynamically loaded and unloaded.", "lbl-options": "Options", "lbl-prefabPath": "Prefab Path", "lbl-prefabName": "Export Name", "lbl-exportOptions": "Export Options", "lbl-export": "Export Prefab", "lbl-load": "Load Into Scene", "lbl-loadOptions": "Load Options", "lbl-unload": "Unload", "lbl-reload": "Reload", "lbl-confimation": "Prefab Created"}, "persistent-anchor": {"name": "Persistent <PERSON>", "description": "Anchors this entity to a real life reference.", "lbl-name": "<PERSON><PERSON>"}, "input": {"name": "Input Component", "description": "An Input component that listens for input events and passes them to InputSink components.", "lbl-activationDistance": "Proximity activation distance", "info-activationDistance": "The distance within which the input component can be activated by proximity.", "lbl-sinkTarget": "Sink Target", "lbl-addSinkTarget": "Add Sink Target", "lbl-removeSinkTarget": "Remove Sink Target"}, "interactable": {"name": "Interactable", "description": "An Interactable component with an optional XRUI containing a label that can trigger callbacks.", "lbl-label": "Label", "lbl-UIactivationDistance": "UI Activation Distance", "info-UIactivationDistance": "The distance at which the UI will be activated. Check the Input Component activation distance for the distance at which you can interact with the object.", "lbl-addcallback": "Add <PERSON><PERSON>", "lbl-removecallback": "Remove <PERSON>", "lbl-clickInteract": "Click to Interact", "info-clickInteract": "Enables clicking to interact with the object", "callbacks": {"lbl-target": "Target", "lbl-callbackID": "Callback ID"}}, "lightmap": {"name": "Lightmap", "description": "Generate lightmaps for static lighting in your scene.", "lbl-resolution": "Resolution", "lbl-uv-channel": "UV Channel", "lbl-samples": "<PERSON><PERSON>", "lbl-bake-indirect": "Bake Indirect Lighting", "desc-bake-indirect": "Enable indirect lighting calculation", "lbl-bake-ao": "Bake Ambient Occlusion", "desc-bake-ao": "Enable ambient occlusion calculation", "btn-generateAtlas": "Generate Atlas", "btn-bakeLightmap": "Bake Lightmap", "baking-progress": "Baking Progress"}, "lookAt": {"name": "LookAt", "description": "A component that makes the entity always face the user, or another entity.", "target": "Target Entity", "xAxis": "X Axis", "yAxis": "Y Axis"}, "grabbable": {"name": "Grabbable", "description": "A Grabbable component that can be picked up and manipulated by the user."}, "media": {"name": "Media", "description": "Audio and video playback.", "path": "Path", "paths": "Source Paths", "playmode": "Play Mode", "audiomode": "Audio Mode", "playtitle": "Play", "pausetitle": "Pause", "resettitle": "Reset", "lbl-ui-offset": "UI Offset", "lbl-controls": "Show Controls In Viewport", "lbl-paused": "Paused", "info-paused": "Toggle media playback", "info-controls": "Toggle the visibility of the media controls.", "lbl-autoplay": "Autoplay", "lbl-muteEditor": "Mute in Editor", "info-autoplay": "Toggle whether this video autoplays", "lbl-synchronize": "Synchronize", "info-synchronize": "If true, the media will be synchronized on all the connected clients.", "lbl-volume": "Volume", "lbl-currentTime": "Current Time", "lbl-isMusic": "Is Music", "info-isMusic": "If true, the media will be treated as music and will not be spatialized (even if a spatial audio component exists).", "lbl-size": "Size", "seektime": "Start Time", "lbl-mediaControls": "Media Controls", "info-mediaControls": "Control media playback (positional audio based on camera position)", "lbl-mediaOptions": "Media Options", "info-mediaOptions": "Media Options", "lbl-mediaSynchronize": "Synchronize Client Playback"}, "media-preview": {"lbl-mediaPreview": "Media Preview", "info-mediaPreview": "Preview the selected media (audio or video) source here in the Properties panel without spatial audio or a video texture in the Scene.", "lbl-selected-source": "Source to Preview"}, "mesh": {"name": "<PERSON><PERSON>", "description": "A mesh is a collection of vertices, edges, and faces that describe the shape of a 3D object.", "geometryEditor": "Geometry Editor", "materialEditor": "Material Editor", "geometry": {"name": "Name:", "count": "Count:", "itemSize": "<PERSON><PERSON>:", "recalculateNormals": "Recalculate Normals"}, "material": {"name": "Name", "source": "Source", "type": "Type:", "path": "Path:", "prototype": "Prototype", "loading": "Loading...", "setPlugin": "<PERSON>lugin", "plugin": "Plugin"}, "materialProperties": {"title": "Material", "info": "Access and edit detailed information about materials in the Scene."}}, "audio": {"name": "Audio", "description": "Audio settings", "lbl-distanceModel": "Distance Model", "info-distanceModel": "The algorithm used to calculate audio rolloff.", "lbl-rolloffFactor": "Rolloff Factor", "info-rolloffFactor": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to 1.", "info-rfInfinity": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to infinity.", "lbl-refDistance": "Reference Distance.", "info-refDistance": "A double value representing the reference distance for reducing volume as the audio source moves further from the listener.", "lbl-maxDistance": "Max Distance", "info-maxDistance": "A double value representing the maximum distance between the audio source and the listener, after which the volume is not reduced any further.", "info-maxDistanceDisabled": "Only supported with the Linear Distance Model.", "lbl-coneAngle": "<PERSON><PERSON>", "info-coneAngle": "The angle (double), in degrees, of a cone.", "lbl-coneInnerAngle": "Inner", "info-coneInnerAngle": "The angle (double), in degrees, of a cone inside of which there will be no directional volume reduction (gain = 1).", "lbl-coneOuterAngle": "Outer", "info-coneOuterAngle": "The angle (double), in degrees, of an outer cone - within which the volume will be directionally reduced down to a constant value (coneOuterGain). The volume level will linearly reduce from 1 at edge of the inner angle cone to coneOuterGain's value at the edge of the outer angle cone.", "lbl-coreOuterGain": "Cone Outer Gain", "info-coreOuterGain": "A double value describing the amount of volume reduction outside the cone defined by the coneOuterAngle attribute. Its default value is 0, meaning that no sound can be heard.", "lbl-play": "Play", "lbl-pause": "Pause", "error-url": "Error Loading From URL", "lbl-media": "Media Source", "lbl-media-info": "Media Source"}, "collider": {"name": "Collider", "description": "An invisible box that objects will bounce off of or rest on top of.\nWithout colliders, objects will fall through floors and go through walls.", "lbl-isTrigger": "<PERSON><PERSON>", "lbl-shape": "<PERSON><PERSON><PERSON>", "lbl-matchMesh": "Match Mesh", "info-matchMesh": "Matches the Collider dimension settings to fit the Mesh.", "lbl-warnRigidBody": "Colliders require a Rigidbody component here or on a parent object to function.", "lbl-addRigidBody": "Add RigidBody", "lbl-colliderProps": "Collider Properties", "lbl-centerOffset": "Center Offset", "lbl-boxSize": "Box Size", "lbl-radius": "<PERSON><PERSON>", "lbl-height": "Height", "lbl-type": "Body Type", "lbl-mass": "Mass", "lbl-massCenter": "Mass Center", "lbl-friction": "Friction", "info-friction": "A friction coefficient of 0 implies no friction at all (completely sliding contact) and a coefficient greater or equal to 1 implies a very strong friction. Values greater than 1 are allowed.", "lbl-restitution": "Restitution", "info-restitution": "The bounciness of the object. 0 is no bounce, 1 is full bounce.", "lbl-collisionLayer": "Col<PERSON><PERSON> Layer", "lbl-collisionMask": "Collision Mask", "lbl-removeMesh": "<PERSON><PERSON><PERSON>"}, "camera": {"name": "Camera", "description": "Camera settings.", "lbl-camera-raycast": "Camera Raycasting", "lbl-camera-raycast-toggle": "Enabled", "lbl-camera-raycast-frequency": "Frequency", "lbl-camera-raycast-count": "<PERSON>", "lbl-camera-raycast-length": "Ray <PERSON>"}, "directionalLight": {"name": "Directional Light", "description": "A light that illuminates the entire Scene, but emits along a single direction.", "lbl-color": "Color", "lbl-intensity": "Intensity", "lbl-cameraFar": "Camera Far", "lbl-cameraNear": "Camera Near", "lbl-cameraTop": "Camera Top", "lbl-cameraBottom": "Camera Bottom", "lbl-cameraLeft": "Camera Left", "lbl-cameraRight": "Camera Right", "lbl-castShadow": "Cast Shadow", "lbl-disableForCSM": "Disabled when CSM is enabled", "lbl-shadowmapResolution": "Shadow Map Resolution", "lbl-shadowBias": "Shadow B<PERSON>", "lbl-shadowRadius": "Shadow Radius", "lbl-showCameraHelper": "Show Camera Helper", "lbl-castShadows": "Cast Shadows", "lbl-calculateShadowBias": "<PERSON><PERSON> Shadow <PERSON>"}, "groundPlane": {"name": "Ground Plane", "description": "A flat Ground Plane that extends into the distance.", "lbl-color": "Color", "lbl-visible": "Visible", "info-visible": "Toggle the visibility of the Ground Plane. Setting to false will still allow shadows to be received, rather than setting the entity visibility."}, "loopAnimation": {"title": "Loop Animation", "description": "Looped animation associated with your 3D model, loaded from a GLTF URL or file.", "lbl-loopAnimation": "Loop Animation", "lbl-timeScale": "Time Scale", "lbl-animationPack": "Animation Pack (via Mixamo Rig)", "lbl-useVRM": "Use VRM Retargeting", "lbl-paused": "Paused", "lbl-loop": "Loop"}, "variant": {"name": "<PERSON><PERSON><PERSON>", "description": "Load different assets based on heuristics.", "add-variant": "<PERSON><PERSON>", "remove-variant": "Remove", "device": "<PERSON><PERSON>", "device-mobile": "Mobile", "device-desktop": "Desktop", "device-xr": "XR", "heuristic": "Heuristic", "heuristic-distance": "Distance", "heuristic-manual": "Manual", "heuristic-device": "<PERSON><PERSON>", "useDistance": "Use Distance", "textureSize": "Texture Size: ", "vertexCount": "Vertices: ", "preview": "Preview", "minDistance": "Minimum Distance", "maxDistance": "Maximum Distance", "src": "Source"}, "map": {"description": "3D geospatial map, e.g., a city."}, "shadow": {"name": "Shadows", "description": "Allow your model to cast or receive shadows for improved visual realism.", "lbl-castShadow": "Cast Shadow", "lbl-receiveShadow": "Receive Shadow"}, "model": {"title": "Model", "description": "A 3D model in your Scene, loaded from a GLTF URL or file.", "lbl-usingGPUInstancingFlag": "Use GPU Instancing", "lbl-modelurl": "Model URL", "lbl-envmapUrl": "Environment Map URL", "lbl-exportType": "File Extension", "lbl-textureOverride": "Override Texture", "lbl-materialOverride": "Material Overrides", "error-url": "Error Loading From URL", "lbl-loopAnimation": "Loop Animation", "lbl-animationSpeed": "Animation Speed", "lbl-convertToVRM": "Force VRM", "lbl-isGPUInstancing": "Is Using GPU Instancing", "lbl-cameraOcclusion": "Occlude Camera", "lbl-applyColliders": "Apply Colliders", "lbl-warnRigidBody": "Colliders require a Rigidbody component here or on a parent object to function", "lbl-addRigidBody": "Add RigidBody", "lbl-shape": "<PERSON><PERSON><PERSON>", "lbl-matrixAutoUpdate": "MatrixAutoUpdate", "lbl-name": "Name", "lbl-url": "Model URL", "lbl-buy": "Buy URL", "lbl-learnMore": "Learn More URL", "lbl-htmlContent": "Html Content", "lbl-interactionText": "Interaction Text", "lbl-interactionType": "Interaction Type", "saveChanges": "Save Changes", "transform": {"lodLevels": "LOD Levels", "lodLevelNumber": "LOD Level {{index}}", "compress": "Compress", "compressionComplete": "Compress Complete", "compressionError": "Compression failed on {{file}}", "compressImage": "Compress Image", "applyPresetConfirmation": "Would you like to apply this preset?", "savePreset": "Save Preset", "useDraco": "Use DRACO Mesh Compression", "useMeshopt": "Use Meshoptimizer", "useQuantization": "Use Mesh Quantization", "textureFormat": "Image Format", "modelFormat": "Model Format", "resourceUri": "Resource URL", "dst": "File name", "info-dst": "The output filename for the transformed model", "info-resourceUri": "The URL where the model's resources will be accessed from", "info-textureFormat": "The output format for textures (De<PERSON>ult, JPG, KTX2, PNG, WebP). KTX2 provides best compression and performance", "maxTextureSize": "Max Texture Size", "info-maxTextureSize": "Maximum resolution for textures. Larger textures will be resized to this limit", "simplifyRatio": "Simplify Ratio", "info-simplifyRatio": "Percentage of vertices to retain (0.0-1.0). Lower values create simpler meshes", "simplifyErrorThreshold": "Simplify E<PERSON><PERSON>", "info-simplifyErrorThreshold": "Maximum deviation when simplifying geometry. Higher values allow more aggressive simplification", "flipYTooltip": "If checked, the source images will be Y flipped before compression.", "srgb": "Linear Color Space", "srgbTooltip": "If checked, the provided map is assumed to be in sRGB space.", "textureCompressionType": "Texture Compression Type", "info-textureCompressionType": "KTX2 compression format: UASTC (higher quality) or ETC1 (smaller size)", "ktx2Quality": "Compression Quality", "info-ktx2Quality": "Quality level for KTX2 texture compression (0.0-1.0)", "split": "Split Meshes", "info-split": "Splits mesh primitives into separate meshes for better optimization", "combineMaterials": "Combine Materials", "info-combineMaterials": "Merges materials with identical properties to reduce draw calls", "palette": "Generate Palette Textures", "info-palette": "Creates texture atlases to reduce draw calls and improve performance", "flipY": "Flip Y", "info-flipY": "Flips textures vertically before processing", "linear": "Linear Color Space", "info-linear": "Process textures in linear color space instead of sRGB", "mipmaps": "Generate Mipmaps", "info-mipmaps": "Creates mipmap levels for textures to improve rendering at different distances", "instance": "Instance Identical Meshes", "info-instance": "Uses mesh instancing for identical geometries to reduce memory usage", "join": "Join <PERSON>", "info-join": "Combines compatible meshes to reduce draw calls", "weldVertices": "Weld Vertices", "info-weldVertices": "Merges vertices sharing the same position and attributes to reduce vertex count", "weldThreshold": "Weld Threshold", "resourceOverrides": "Resource Overrides", "quality": "Quality", "qualityTooltip": "Sets the ETC1S encoder's quality level, which controls the file size vs. quality tradeoff.", "mode": "Mode", "modeTooltip": "If checked, the encoder will output a UASTC texture, otherwise a ETC1S texture.", "mipmapsTooltip": "If checked, the encoder will generate mipmaps.", "compressionLevel": "Compression Level", "compressionLevelTooltip": "The compression level parameter controls the encoder perf vs. file size tradeoff for ETC1S files. It does not directly control file size vs. quality (see Quality).", "uastcFlags": "UASTC Flags", "uastcFlagsTooltip": "Sets the UASTC encoding performance vs. quality tradeoff, and other lesser used UASTC encoder flags. This is a combination of flags.", "normalMap": "Normal Map", "normalMapTooltip": "<PERSON>nes several codec parameters so compression works better on normal maps.", "uastcZstandard": "UASTC Zstandard", "uastcZstandardTooltip": "Use UASTC Zstandard supercompression.", "useMeshoptimizer": "Use Meshoptimizer", "info-useMeshoptimizer": "Applies Meshoptimizer compression for more efficient vertex data storage", "info-useDraco": "Applies Draco compression to reduce geometry size significantly", "removeDuplicates": "Remove Duplicates", "info-removeDuplicates": "Removes duplicate meshes, materials, and textures", "flatten": "Flatten Scene Graph", "info-flatten": "Simplifies the scene hierarchy by removing unnecessary nodes", "pruneUnused": "<PERSON><PERSON><PERSON> Unused", "info-pruneUnused": "Removes unused materials, textures, and other assets", "reorder": "Reorder", "info-reorder": "Optimizes the order of draw calls for better rendering performance", "resampleAnimations": "Resample Animations", "info-resampleAnimations": "Resamples animation keyframes for smoother playback", "status": {"transformingmodels": "Transforming models...", "processingtexture": "Processing texture {{numerator}} of {{denominator}}...", "writingfiles": "Writing files...", "complete": "Complete"}, "progress": "LOD {{currentLOD}} of {{totalLODs}}: {{caption}}"}, "lods": {"label": "Level of Detail", "serialize": "Serialize", "generate": "Generate LODs"}, "lbl-export": "Export Settings"}, "materialAssignment": {"lbl-materialID": "Material", "error-materialID": "Error finding Material", "placeholder-materialID": "Select Material", "lbl-patternTarget": "Pattern Match", "lbl-pattern": "Pattern", "lbl-isRegex": "Regex"}, "object3d": {"name": "Object3D", "description": "Scene Object Properties."}, "interaction": {"type": "Interaction Type", "text": "Interaction Text", "distance": "Interaction Distance", "theme": "Interaction Theme", "name": "Interaction Name", "description": "Interaction Description", "images": "Interaction Images", "videos": "Interaction Videos", "urls": "Interaction Urls", "models": "Interaction Models"}, "group": {"name": "Group", "description": "A group of multiple objects that can be moved or duplicated together.\nDrag and drop objects into the Group in the Hierarchy."}, "hemisphere": {"name": "Hemisphere Light", "description": "A light that illuminates the Scene with a sky color from above and a ground color from below.", "lbl-skyColor": "Sky Color", "lbl-groundColor": "Ground Color", "lbl-intensity": "Intensity"}, "image": {"name": "Image", "description": "Dynamically loads an image.", "lbl-imgURL": "Image URL", "lbl-transparency": "Transparency", "lbl-alphaCutoff": "Alpha Cutoff", "lbl-projection": "Projection", "lbl-side": "Side", "info-controls": "Toggle the visibility of the media controls.", "info-transparency": "How to apply transparency:", "info-opaque": "'opaque' = no transparency.", "info-blend": "'blend' = use the image's alpha channel.", "info-mask": "'mask' = Use a specified cutoff value for on/off transparency (more performant.)", "info-alphaCutoff": "Pixels with alpha values lower than this will be transparent.", "error-url": "Invalid URL", "lbl-aspect-ratio": "Aspect Ratio", "lbl-match-aspect-ratio": "Match Aspect Ratio", "lbl-fit": "Image Fit", "lbl-fit-info": "How the image should be stretched within the given canvas size"}, "screenshare": {"name": "Screenshare Target", "description": "Allows this object to be a target for screenshare projection"}, "name": {"lbl-name": "Name"}, "particle-system": {"name": "Particle System", "description": "Particle emitter to create particles.", "looping": "Looping", "duration": "Duration", "prewarm": "Prewarm", "emitter-shape": "Emitter <PERSON><PERSON><PERSON>", "emitter-shape-type": {"point": "Point", "sphere": "Sphere", "cone": "Cone", "donut": "Donut", "mesh": "<PERSON><PERSON>"}, "poiComponent": {"name": "POI Component", "description": "Settings for a point of interest camera view", "lbl-poiHotspotEntities": "POI Hotspot Entities"}, "emission-bursts": "Emission Bursts", "add-burst": "<PERSON><PERSON>", "remove-burst": "Remove <PERSON>", "shape-mesh": "Emitter <PERSON><PERSON><PERSON>", "shape-mesh-info": "Drag glTF or GLB file here (or paste its file asset URL) to set the Mesh to use as the Emitter Shape", "start-life": "Start Life", "start-size": "Start Size", "start-speed": "Start Speed", "start-rotation": "Start Rotation", "startColor": {"title": "Start Color", "type": "Type", "constant": "Constant", "range": "Range", "random": "Random", "gradient": "Gradient", "color": "Color", "opacity": "Opacity", "start": "Start", "remove": "Remove"}, "rotation": {"axisAngle": "Axis Angle", "euler": "<PERSON>uler", "randomQuaternion": "Random Quaternion", "axis": "Axis", "angle": "<PERSON><PERSON>", "anglePosition": "Angle {{position}}"}, "emission-over-time": "Emission Over Time", "renderMode": {"title": "Render Mode", "billboard": "Billboard", "stretched-billboard": "Stretched Billboard", "mesh": "<PERSON><PERSON>", "trail": "Trail", "speedFactor": "Speed Factor", "lengthFactor": "Length Factor", "trailLength": "Trail Length"}, "texture": "Particle Texture", "u-tiles": "U Tiles", "v-tiles": "V Tiles", "start-tile-index": "Starting Tile Index", "mesh": "Particle Mesh", "blending": "Blending", "transparent": "Transparent", "world-space": "Use World Space", "valueGenerator": {"value": "Value", "min": "Min", "max": "Max", "addBezier": "<PERSON><PERSON>", "pIndex": "P{{index}}", "start": "Start", "remove": "Remove", "type": "Type", "constant": "Constant", "interval": "Interval", "bezier": "<PERSON><PERSON>"}, "blending-type": {"normal": "Normal", "additive": "Additive", "subtractive": "Subtractive", "multiply": "Multiply", "custom": "Custom", "no-blending": "No Blending"}, "burst": {"time": "Burst Time", "count": "<PERSON><PERSON><PERSON>", "cycle": "Burst Cycle", "interval": "Burst Interval", "probability": "Burst Probability"}, "addBehavior": "Add Behavior", "behaviors": "Behaviors", "behavior": {"force": "Force", "magnitude": "Magnitude", "frequency": "Frequency", "Power": "Power", "positionAmount": "Position Amount", "rotationAmount": "Rotation Amount", "scale": "Scale", "octaves": "Octaves", "velocityMultiplier": "Velocity Multiplier", "timeScale": "Time Scale", "center": "Center", "color": "Color", "angularVelocity": "Angular Velocity", "dynamic": "Dynamic", "size": "Size", "speed": "Speed", "frame": "<PERSON>ame", "orbit": "Orbit", "axis": "Axis", "width": "<PERSON><PERSON><PERSON>", "angle": "<PERSON><PERSON>", "subParticleSystem": "Sub Particle System", "type": "Type", "applyForce": "Apply Force", "noise": "Noise", "turbulenceField": "Turbulence Field", "gravity": "Gravity", "typeOverLifetime": "{{type}} Over Lifetime", "widthOverLength": "Width Over Length", "changeEmitDirection": "Change Emit Direction", "emitSubParticleSystem": "Emit Sub Particle System"}, "remove": "Remove"}, "clouds": {"name": "Cloud", "description": "Sprite-based cloud volume.", "lbl-image": "Sprite Image", "lbl-wroldScale": "World Scale", "lbl-dimensions": "Dimensions", "lbl-noiseZoom": "Noise Zoom", "lbl-noiseOffset": "Noise Offset", "lbl-spriteScale": "Sprite Scale", "lbl-fogColor": "Fog Color", "lbl-fogRange": "Fog Range", "error-url": "Error Loading From URL"}, "ocean": {"name": "Ocean", "description": "Ocean surface with waves and foam.", "lbl-normalMap": "Normal Map", "lbl-distortionMap": "Distortion Map", "lbl-envMap": "Environment Map", "lbl-color": "Color", "lbl-shallowWaterColor": "Shallow Water Color", "lbl-shallowToDeepDistance": "Shallow to Deep Distance", "lbl-opacityFadeDistance": "Opacity Fade Distance", "lbl-opacityRange": "Opacity Range", "lbl-shininess": "Shininess", "lbl-reflectivity": "Reflectivity", "lbl-foamColor": "Foam Color", "lbl-foamSpeed": "Foam Speed", "lbl-foamTiling": "Foam Tiling", "lbl-bigWaveTiling": "Big Wave Tiling", "lbl-bigWaveSpeed": "Big Wave Speed", "lbl-bigWaveHeight": "Big Wave Height", "lbl-waveSpeed": "Wave Speed", "lbl-waveScale": "Wave Scale", "lbl-waveTiling": "Wave Tiling", "lbl-waveDistortionSpeed": "Wave Distortion Speed", "lbl-waveDistortionTiling": "Wave Distortion Tiling", "error-url": "Error Loading From URL"}, "water": {"name": "Water", "description": "Small water surface."}, "interior": {"name": "Interior", "description": "A surface with mapped cube texture to emulate interior environments.", "lbl-cubeMap": "Cube Map texture", "lbl-tiling": "Tiling amount", "lbl-size": "Surface size", "error-url": "Error Loading From URL"}, "pointLight": {"name": "Point Light", "description": "A light that emits in all directions from a single point.", "lbl-color": "Color", "lbl-intensity": "Intensity", "lbl-range": "Range", "lbl-decay": "Decay"}, "instancing": {"name": "Instancing", "description": "Generates instanced geometry according to input.", "count": "Instance Count", "lbl-surface": "Target Surface", "error-surface": "An error occurred while finding the target surface.", "placeholder-surface": "No Target Selected", "lbl-mode": "Instancing Mode", "lbl-load": "Stage", "lbl-unload": "Unstage", "lbl-reload": "Restage", "lbl-options": "Options", "samplingMode": "Sampling Mode", "sampling": {"properties": "Sampling Properties", "heightMap": "Height Map", "heightMapStrength": "Height Map Strength", "densityMap": "Density Map", "densityMapStrength": "Density Map Strength", "root": "Root Node"}, "grass": {"properties": "Grass Properties", "bladeHeight": "Blade Height", "bladeWidth": "<PERSON>", "joints": "Blade Joint Count", "alphaMap": "Alpha Map", "texture": "Grass Texture", "ambientStrength": "Ambient Strength", "diffuseStrength": "Diffuse Strength", "shininess": "Shininess", "sunColor": "Sun Color"}, "mesh": {"properties": "Mesh Instancing Properties", "instancedMesh": "In<PERSON><PERSON>", "error-instancedMesh": "Error loading instanced mesh", "placeholder-instancedMesh": "Select Mesh to Instance"}}, "cameraSettings": {"name": "Camera Settings", "description": "Adjust camera settings to optimize Scene visualization, including projection type, field of view, and distance parameters.", "lbl-projectionType": "Projection Type", "lbl-fov": "Field of View", "lbl-calcClippingPlanes": "Calculate Clipping Planes", "lbl-clippingPlanes": "Clipping Planes", "lbl-nearClip": "Near", "lbl-farClip": "Far ", "lbl-minCamDist": "Min Distance", "lbl-maxCamDist": "Max Distance", "lbl-cameraDistance": "Camera Distance", "lbl-enableTransitionButtons": "Transition Buttons", "lbl-enableTransitionButtonsDescription": "Show UI buttons for navigating between POIs.", "lbl-phi": "Phi", "lbl-cameraMode": "Camera Mode", "lbl-avatar": "Avatar", "lbl-avatarVisible": "Avatar is Visible", "lbl-scrollingSpeed": "<PERSON><PERSON><PERSON> Speed", "lbl-followCameraModes": "Follow Camera Modes", "lbl-followCameraFristPerson": "First Person Camera", "lbl-followCameraThirdPerson": "Third Person Camera", "lbl-followCameraTopDown": "Top-Down Camera", "lbl-followCameraError": "You need to select at least one Follow Camera Mode", "lbl-firstPersonSettings": "First Person Camera Settings", "lbl-thirdPersonSettings": "Third Person Camera Settings", "lbl-topDownSettings": "Top-Down Camera Settings", "lbl-defaultDistance": "Default Distance", "lbl-freeCamera": "Free Camera", "lbl-cameraLimits": "Camera Limits", "lbl-cameraReset": "Camera Reset"}, "cameraComponent": {"name": "Camera", "description": "Configure camera parameters for optimal Scene rendering.", "lbl-fov": "FOV", "lbl-aspect": "Aspect", "lbl-near": "Near", "lbl-far": "Far"}, "postprocessing": {"name": "Post-Processing Effect", "enabled": "Enabled", "description": "For applying post-processing effects to you Space."}, "mediaSettings": {"name": "Media Settings", "description": "Enhance immersion by adjusting media settings, including positional audio, distance-based sound, and immersive media for a more dynamic auditory experience.", "lbl-audioSettings": "Use Positional Audio", "lbl-avatarDistanceModel": "Avatar Distance Model", "lbl-avatarRolloffFactor": "Avatar Rolloff Factor", "lbl-avatarRefDistance": "Avatar Ref Distance", "lbl-avatarMaxDistance": "Avatar Max Distance", "lbl-mediaVolume": "Media Volume", "lbl-mediaDistanceModel": "Media Distance Model", "lbl-immersiveMedia": "Use Immersive Media", "lbl-mediaRolloffFactor": "Media Rolloff Factor", "lbl-mediaRefDistance": "Media Ref Distance", "lbl-mediaMaxDistance": "Media Max Distance", "lbl-mediaConeInnerAngle": "Media Cone Inner Angle", "lbl-mediaConeOuterAngle": "Media Cone Outer Angle", "lbl-mediaConeOuterGain": "Media Cone Outer Gain", "info-avatarDistanceModel": "The algorithim used to calculate audio rolloff.", "info-avatarRolloffFactor": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to 1.", "info-avatarRolloffFactorInifinity": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to infinity.", "info-avatarRefDistance": "A double value representing the reference distance for reducing volume as the audio source moves further from the listener.", "info-avatarMaxDistance": "A double value representing the maximum distance between the audio source and the listener, after which the volume is not reduced any further.", "info-mediaDistanceModel": "The algorithim used to calculate audio rolloff.", "info-immersiveMedia": "Enable immersive media for non-immersive platforms.", "info-mediaRolloffFactor": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to 1.", "info-mediaRolloffFactorInfinity": "A double value describing how quickly the volume is reduced as the source moves away from the listener. 0 to infinity.", "info-mediaRefDistance": "A double value representing the reference distance for reducing volume as the audio source moves further from the listener.", "info-mediaMaxDistance": "A double value representing the maximum distance between the audio source and the listener, after which the volume is not reduced any further.", "info-mediaConeInnerAngle": "A double value describing the angle, in degrees, of a cone inside of which there will be no volume reduction.", "info-mediaConeOuterAngle": "A double value describing the angle, in degrees, of a cone outside of which the volume will be reduced by a constant value, defined by the coneOuterGain attribute.", "info-mediaConeOuterGain": "A double value describing the amount of volume reduction outside the cone defined by the coneOuterAngle attribute. Its default value is 0, meaning that no sound can be heard."}, "audioAnalysis": {"name": "Audio Analysis", "description": "Adjust advanced audio settings with options to enable and fine-tune bass, treble, and mid frequencies using configurable multipliers for precise sound control.", "lbl-bassEnabled": "Bass Enabled", "lbl-trebleEnabled": "Treble Enabled", "lbl-midEnabled": "Mid Enabled", "lbl-bassMultiplier": "Bass Multiplier", "lbl-trebleMultiplier": "Treble Multiplier", "lbl-midMultiplier": "Mid Multiplier"}, "renderSettings": {"name": "Render Settings", "description": "Configure various rendering settings for the Scene, including lighting, shadows, post-processing effects, tone mapping, and level of detail adjustments.", "lbl-bgcolor": "Background Color", "lbl-lods": "LODs", "info-lods": "Sets the distances at which levels of detail change.", "lbl-rendererSettings": "Override Renderer Settings", "lbl-usePostProcessing": "Use Post Processing", "info-usePostProcessing": "Enables post processing", "lbl-useShadows": "Use Shadows", "info-useShadows": "Enables shadows", "lbl-primaryLight": "Primary Light", "info-primaryLight": "Set the primary light for the Scene", "lbl-csm": "Cascading Shadow Maps", "info-csm": "Enables cascading shadow maps.", "lbl-csm-cascades": "Cascades", "info-csm-cascades": "Smaller the size of the Scene. More efficient to have less cascades.", "lbl-toneMapping": "<PERSON>ne <PERSON>", "info-toneMapping": "Enables tone mapping on the Scene renderer. Has no effect when post-processing is enabled.", "lbl-toneMappingExposure": "Tone Mapping Exposure", "info-toneMappingExposure": "Specifies the tone mapping exposure on the Scene renderer. Has no effect when a post processing is enabled.", "lbl-shadowMapType": "Shadow Map Type", "info-shadowMapType": "Type of shadow map"}, "sceneSettings": {"name": "Scene Settings", "description": "Enhance user experience by customizing thumbnails, loading screens, colors, and spectating options.", "lbl-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "info-thumbnail": "Change the thumbnail", "generate": "Generate", "save": "Save", "lbl-loading": "Loading Screen", "info-loading": "Change the loading screen", "lbl-colors": "Colors", "lbl-killHeight": "Kill Height", "lbl-spectate": "Force Spectator", "info-spectate": "Disable avatars and force the user to spectate the Scene.", "lbl-uuid": "Spectate Entity", "info-uuid": "Optional entity to spectate"}, "scene": {"name": "Scene", "description": "The root object of the Scene.", "error-url": "Error Loading From URL"}, "sceneCamera": {"name": "Scene Preview Camera", "description": "The camera used to generate the thumbnail for your Scene and the starting position for the preview camera.", "lbl-setFromViewPort": "Set From Viewport", "lbl-updateThumbnail": "Update Thumbnail"}, "skybox": {"name": "Skybox", "description": "", "lbl-timeOfDay": "Time of Day", "lbl-color": "Color", "lbl-latitude": "Latitude", "lbl-luminance": "Luminance", "lbl-scattering": "Scattering Amount", "lbl-scatteringDistance": "Scattering Distance", "lbl-horizonStart": "Horizon Start", "lbl-horizonEnd": "Horizon End", "lbl-texture": "Texture", "lbl-skyType": "Sky Type", "error-url": "Error Loading From URL"}, "spawnPoint": {"name": "Spawn Point", "description": "A point where people will appear when they enter your Scene.\nThe icon in the Viewport represents the actual size of an avatar."}, "linkComp": {"title": "Link Component", "lbl-url": "Link URL", "lbl-navigateScene": "Scene navigation", "lbl-locaiton": "Published Space Name", "lbl-newTab": "Open in new tab", "description": "Adds an interactable that opens links or navigates the Scene."}, "overlay": {"title": "Overlay", "description": "Places content over the Scene view as an overlay.", "contentSource": "Content Source", "overlayPosition": "Overlay Position", "optionalUrl": "URL (Optional)", "src": "src"}, "spotLight": {"name": "Spot Light", "description": "A light which emits along a direction, illuminating objects within a cone.", "lbl-color": "Color", "lbl-intensity": "Intensity", "lbl-penumbra": "Penumbra", "lbl-angle": "<PERSON><PERSON>", "lbl-range": "Range", "lbl-decay": "Decay", "lbl-shadowBias": "Shadow B<PERSON>", "lbl-shadowRadius": "Shadow Radius", "lbl-castShadow": "Cast Shadow"}, "transform": {"title": "Transform", "description": "Change the transform of an entity.", "lbl-position": "Position", "lbl-rotation": "Rotation", "lbl-scale": "Scale", "lbl-useGlobalTransform": "Use Global Transform"}, "triggerVolume": {"name": "<PERSON><PERSON>", "description": "Sets a property on the target object on enter and leave.", "lbl-addTrigger": "<PERSON><PERSON>", "lbl-removeTrigger": "<PERSON><PERSON><PERSON>", "lbl-target": "Target Object", "info-target": "Drag an entity into this field to set it as the target object and enable callback selections.", "lbl-onenter": "On Enter", "info-onenter": "Select a callback to trigger on above target entity when entering this trigger volume.", "lbl-onexit": "On Exit", "info-onexit": "Select a callback to trigger on the target entity when entering this trigger volume.", "info-disabled-callback": "(Disabled) No target entity selected, please drag a target entity into the 'Target Object' field above.", "lbl-showHelper": "Show Helper <PERSON>", "ph-errorNode": "Error missing node.", "ph-selectNode": "Select node...", "lbl-addRigidBody": "Add RigidBody"}, "video": {"name": "Video", "lbl-media": "Media Source", "lbl-size": "<PERSON>vas Si<PERSON>", "lbl-size-info": "The dimensions of the surface on which the video will be presented", "lbl-fit": "Video Fit", "lbl-fit-info": "How the video should be stretched within the given canvas size", "lbl-media-info": "An entity w/ a media component that should be sourced for display", "description": "Display a video", "lbl-projection": "Projection", "lbl-side": "Side", "lbl-side-info": "The side of the video to display", "lbl-use-alpha": "Transparency", "lbl-use-alphaEnable": "Enable", "lbl-use-alphaInvert": "Invert", "lbl-use-alpha-info": "If true, the video will be rendered with an alpha channel", "lbl-alpha-threshold": "<PERSON><PERSON><PERSON><PERSON>", "lbl-alpha-threshold-info": "The alpha value below which the video will be considered transparent", "lbl-uv-offset": "UV Offset", "lbl-uv-offset-info": "The offset of the video texture in UV space", "lbl-uv-scale": "UV Scale", "lbl-uv-scale-info": "The scale of the video texture in UV space", "lbl-wrap": "Wrap", "lbl-wrap-info": "The wrap mode of the video texture", "lbl-wrap-s": "S", "lbl-wrap-s-info": "The wrap mode of the video texture in the S direction", "lbl-wrap-t": "T", "lbl-wrap-t-info": "The wrap mode of the video texture in the T direction", "lbl-use-alpha-uv-transform": "Alpha Transform", "lbl-use-alpha-uv-transform-info": "If true, the alpha channel will be transformed by its own UV transform", "lbl-alpha-uv-scale": "Alpha UV Scale", "lbl-alpha-uv-scale-info": "The scale of the alpha channel in UV space", "lbl-synchronized-media-source": "Synchronized Media Source", "lbl-synchronized-media-source-info": "Synchronized Media Source", "lbl-aspect-ratio": "Aspect Ratio", "lbl-match-aspect-ratio": "Match Aspect Ratio"}, "volumetric": {"name": "Volumetric", "description": "Dynamically loads a volumetric video.", "lbl-useLoadingEffect": "Use Loading Effect"}, "spline": {"name": "Spline", "description": "Create and customize curves.", "lbl-addNode": "Nodes"}, "splinetrack": {"name": "Spline Track", "description": "Have an entity follow a spline.", "lbl-spline": "Source Spline", "lbl-velocity": "Velocity", "lbl-enableRotation": "Enable Rotation", "lbl-lockXZ": "Lock to XZ Plane", "lbl-loop": "Loop"}, "systemnode": {"name": "System Node", "description": "Attach script.", "lbl-filePath": "URL", "lbl-insertUUID": "Reference System", "lbl-insertOrder": "Insertion Order", "lbl-enableClient": "Client", "lbl-enableServer": "Server", "lbl-args": "<PERSON><PERSON><PERSON>", "error-url": "Error loading script from URL"}, "primitiveGeometry": {"name": "Primitive Geometry", "description": "3D models of basic geometry shapes.", "lbl-geometryType": "Geometry Type", "lbl-box": "Box", "lbl-sphere": "Sphere", "lbl-cylinder": "<PERSON><PERSON><PERSON>", "lbl-capsule": "Capsule", "lbl-plane": "Plane", "lbl-circle": "Circle", "lbl-ring": "Ring", "lbl-torus": "<PERSON><PERSON>", "lbl-polyhedron": "Polyhedron", "lbl-torusknot": "<PERSON><PERSON>"}, "portal": {"name": "Portal", "lbl-portal": "Linked Portal", "lbl-redirect": "Use Page Redirect", "description": "A portal to teleport players to a specified Space", "lbl-locationName": "Space", "lbl-modelUrl": "Model URL", "lbl-displayText": "Display Text", "lbl-effectType": "Loading Effect", "lbl-previewType": "Preview Type", "lbl-previewImageURL": "Preview Image URL", "lbl-savedImageURL": "Saved Image", "lbl-generateImage": "Generate Image", "lbl-saveImage": "Save Image", "lbl-spawnPosition": "Spawn Position", "lbl-spawnRotation": "Spawn Rotation", "lbl-triggerPosition": "Trigger Position", "lbl-triggerRotation": "Trigger Rotation", "lbl-triggerScale": "Trigger Scale"}, "fog": {"name": "Fog", "description": "A universal fog.", "lbl-fogType": "Fog Type", "lbl-fogColor": "Fog Color", "lbl-fogHeight": "Fog Height", "lbl-fogTimeScale": "Fog Time Scale", "lbl-forNearDistance": "Fog Near Distance", "lbl-fogFarDistance": "Fog Far Distance", "lbl-fogDensity": "Fog Density"}, "xr": {"name": "XR Settings"}, "mountPoint": {"name": "Mount point", "description": "An object with a pose to define interactable behaviors.", "interact-message-seat": "Press E to Sit", "lbl-type": "Type", "lbl-dismount": "Dismount point, offsets this transform", "lbl-force-dismount": "Force Dismount Position", "lbl-force-dismount-info": "If true, the dismount position will be forced to the specified position (mid-air OK), regardless of the raycast result returning a hit within 2m below the dismount offset point."}, "text": {"name": "3D Text", "description": "A 3D text object.", "textGroup": "Text", "textOpacity": "Opacity", "textWidth": "<PERSON><PERSON><PERSON>", "textIndent": "Indent", "textAlign": "Align", "textWrap": "Wrap", "textAnchor": "<PERSON><PERSON>", "textDepthOffset": "Depth offset", "textCurveRadius": "Curve radius", "letterSpacing": "Letter spacing", "lineHeight": "Line Height", "textDirection": "Direction", "fontGroup": "Font", "fontFamily": "Family", "fontSize": "Size", "fontColor": "Color", "fontMaterial": "Material", "outlineGroup": "Outline", "outlineColor": "Color", "outlineOpacity": "Opacity", "outlineWidth": "<PERSON><PERSON><PERSON>", "outlineBlur": "Blur", "outlineOffset": "Offset", "strokeGroup": "Stroke", "strokeColor": "Color", "strokeOpacity": "Opacity", "strokeWidth": "<PERSON><PERSON><PERSON>", "advancedActive": "Show Advanced", "advancedGroup": "Advanced", "clippingActive": "clip.active", "clippingMin": "clip.min", "clippingMax": "clip.max", "glyphResolution": "glyph.resolution", "glyphDetail": "glyph.detail", "gpuAccelerated": "GPU Accelerated"}, "textBox": {"name": "Textbox Component", "description": "An XRUI text box component", "lbl-text": "Text", "lbl-family": "Family", "lbl-size": "Size", "lbl-fontStroke": "Font Stroke", "lbl-fontColor": "Font Color", "lbl-cornerRadius": "Corner Radius", "lbl-backgroundColor": "Background Color", "lbl-opacity": "Opacity", "lbl-padding": "Padding"}, "gallery": {"name": "Gallery Component", "description": "XRUI Gallery Component", "lbl-imageGrid": "ImageGrid Component", "lbl-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "lbl-outfitURL": "Outfit URL", "lbl-performerURL": "Performer URL", "lbl-asset": "<PERSON><PERSON>", "assets": "Assets"}}, "inspector": {"switchInspector": "Switch to Inspector tab for asset selection", "title": "Inspector", "assetType": "Type", "assetAuthor": "Author", "assetDateCreated": "Date Created", "assetFileSize": "File Size", "assetDimensions": "Dimensions", "assetMeshComplexity": "Mesh Complexity", "assetResources": "Resources", "assetTags": "Tags", "addToScene": "Add to Scene", "noAssetSelected": "No asset selected"}, "projects": {"createProject": {"header": "New Project", "backLink": "Back to projects", "featured": "Featured", "all": "All", "newProject": "New Empty Project", "ph-search": "Search Scenes..."}, "grid": {"loading": "Loading..."}, "page": {"header": "Welcome", "headerMsg": "If you're new here we recommend going through the tutorial. Otherwise, jump right in and create a Project from scratch or from one of our templates.", "lbl-startTutorial": "Start Tutorial", "projects": "Projects", "lbl-newProject": "New Project"}}, "visualScript": {"panel": {"title": "Visual Script", "addVisualScript": "Add Visual Script"}, "picker": {"title": "Add Node"}, "modal": {"clear": {"title": "Clear Visual Script", "confirm": "Are you sure?"}, "help": {"title": "Help", "addNodeHelp": " Right click anywhere to add a new node.", "addConnectionHelp": "Drag from the output of one node to the input of another to create a connection or into empty space to add a new node and connect it", "deleteNodeHelp": "Left click to select individual or use Shift+Left click to box select nodes or connections, backspace to delete selected nodes or connections."}, "load": {"title": "Load Visual Script", "examples": "Select an example"}, "save": "Save Visual Script", "buttons": {"clear": "Clear", "cancel": "Cancel", "load": "Load", "close": "Close", "copy": {"done": "Copied!", "begin": "Copy"}}}, "sidePanel": {"title": "Graph Utilities", "node": {"name": "Nodes", "description": "Individual logic units connected together to create a graph"}, "template": {"name": "Templates", "description": "Reusable graphs that can be added to other graphs."}, "variables": {"name": "Variables", "description": "Variables that can be used in the graph.", "add": "Add Variable"}}, "editorPanel": {"makeTemplate": "Make into template"}}, "layout": {"assetGrid": {"loading": "Loading...", "copyURL": "Copy URL", "openInNewTab": "Open URL in New Tab", "deleteAsset": "Delete Asset", "prefab": "Prefab", "prefabs": "Prefabs", "prefabs-search": "Search Prefabs ...", "components": "Components", "components-search": "Search Components ...", "component-detail": {"model": "Creates objects in the hierarchy. Drag a model from the assets folder into the URL box, or drag assets directly from the Project files into the hierarchy.", "audio": "Imports audio clips. Supports .mp3, .flac, .ogg, .wav, and .m4a.", "volumetric": "Imports volumetric files. Supports DRCS, UVOL, or manifest files and links to cloud hosting.", "video": "Imports a 2D plane that accepts .mp4, .mkv, and .avi video files.", "variant": "Adds a model variant for level of detail.", "image": "Imports an image into the Scene.", "ground-plane": "Create collision ground plane.", "group": "Collection of models or assets.", "collider": "Creates a collision shape (sphere, box, capsule, or cylinder) for manual placement.", "rigidbody": "Adds physics to your object, allowing it to interact with other objects in the Scene.", "mount-point": "Attaches a property for users to mount or attach themselves, such as sitting on a chair.", "trigger": "Adds a callback to detect collisions with other objects and execute an action.", "spawn-point": "Defines the point where users will appear when entering the Space.", "interactable": "Allows your entity to respond to user interactions such as proximity, hover, or clicks.", "input": "Listens for user input events and forwards them to entities with InputSink components.", "loop-animation": "Configures the animation associated with your 3D model, loaded from a glTF URL of file.", "shadow": "Allows your object to cast and receive shadows in the scene.", "lookAt": "Makes an entity face the user or another entity in the scene.", "fog": "Adds fog to the entity.", "ecommerce-product": "Adds a product to the entity from a preconfigured store provider.", "text-spatial": "Adds spatial 3D text to the entity.", "portal": "A portal to teleport a player to other portals in different Spaces.", "ambient-light": "A combination of direct and indirect light, provides general lighting to all assets.", "point-light": "A light that emits in all directions from a single point.", "spot-light": "Creates a light that shines in a specific direction.", "directional-light": "Creates a light that emits evenly in a single direction.", "hemisphere-light": "A light that illuminates the Scene with a sky color from above and a ground color from below.", "particle-system": "Creates a dynamic particle emitter for visual effects.", "system": "Inserts logic into the scene by creating a new Entity Component System based on a .ts file.", "visual-script": "Customizes the state and behavior of entities using visual node connections.", "envmapbake": "Adds baked environment lighting to your scene.", "scene-preview-camera": "Generates a scene thumbnail and defines the starting camera position.", "skybox": "Sets the environment outside your map using a selected skybox style.", "spline-track": "Creates a spline-based track for movement or animation.", "spline": "Creates and customizes curved paths.", "envmap": "Adds an environment map to your scene for lighting and reflections.", "postprocessing": "Applies postprocessing visual effects to your scene.", "camera_settings": "Adjusts camera parameters for optimal Scene rendering.", "primitive-geometry": "Adds basic geometric shapes to your scene.", "link": "Adds a clickable link to an entity to route users to a different webpage.", "dynamic-load": "Loads entities dynamically at runtime.", "overlay": "Creates an overlay based on iframe."}, "placeObject": "Place Object", "placeObjectAtOrigin": "Place Object at Origin"}, "filebrowser": {"tab-name": "Files", "addNewFolder": "Add New Folder", "cutAsset": "Cut", "copyAsset": "Copy", "pasteAsset": "Paste", "renameAsset": "<PERSON><PERSON>", "renameFile": "Rename File", "renameFileError": "Not a valid file name", "deleteFile": "Delete File", "viewAssetProperties": "View Properties", "refresh": "Refresh", "back": "Back", "loadingFiles": "Loading Files", "loadingProjects": "Loading Projects", "compress": "Compress", "convert": "Convert", "downloadProject": "Download Project", "downloadProjectUnavailable": "Cannot Download From Project Directory", "uploadAssets": "Upload Assets", "uploadFiles": "Upload Files", "uploadFolder": "Upload Folder", "uploadingFiles": "Uploading Files ({{completed}}/{{total}})", "fetchingProject": "Fetching Project", "downloadingProject": "Downloading Project", "search-placeholder": "Search", "generatingThumbnails": "Generating Thumbnails ({{count}} remaining)", "generatingDimension": "Generating Dimensions ({{count}} remaining)", "generatingMultiView": "Generating Multi-View ({{count}} remaining)", "file": "File", "directory": "Directory", "fileNameLengthError": "File name must be between 4 and 64 characters.", "fileProperties": {"header": "Info for {{fileName}}", "header-plural": "Info for {{itemCount}} Items", "regenerateThumbnail": "Regenerate Thumbnail", "name": "Name", "fileName": "File Name", "type": "Type", "size": "Size", "url": "URL", "author": "Author", "attribution": "Attribution", "licensing": "Licensing", "description": "Description", "tag": "Tag", "addTag": "Add New Tag", "add": "Add", "save-changes": "Save Changes", "discard": "Discard", "mixedValues": "Mixed", "uploadThumbnail": "Upload Thumbnail"}, "view-mode": {"icons": "View: Icons", "list": "View: List", "settings": {"name": "View Options", "iconSize": "Icon Size", "select-listColumns": "Select List Columns", "fontSize": "Font Size"}}, "table-list": {"headers": {"name": "Name", "type": "Type", "dateModified": "Date Modified", "size": "Size", "author": "Author", "statistics": "Statistics", "createdAt": "Created At"}}, "image-convert": {"format": "Format", "resize": "Resize", "width": "<PERSON><PERSON><PERSON>", "height": "Height"}}, "scene-assets": {"no-category": "No category selected", "search-placeholder": "Search", "preview": "Preview", "info-drag-drop": "Drag and Drop these items into the Scene", "settings": "Settings", "no-search-results": "No results found", "total-assets": "Showing {{total}} assets", "previous": "Previous"}}, "hierarchy": {"lbl": "Hierarchy", "info": "The Scene Hierarchy contains all elements currently in your Scene (assets, lighting, items from the tool menu, etc.).", "lbl-rename": "<PERSON><PERSON>", "lbl-renameScene": "Rename Scene", "lbl-edited": "Edited", "lbl-duplicate": "Duplicate", "lbl-group": "Group", "lbl-ungroup": "Ungroup", "lbl-cloneScene": "Clone Scene", "lbl-cloneSceneToProject": "Clone Scene to Project", "lbl-cloneOptions": "Please select the clone action you want to perform", "lbl-copy": "Copy", "lbl-paste": "Paste", "lbl-copyEmbedCode": "Copy Embed code", "lbl-delete": "Delete", "lbl-deleteScene": "Delete Scene", "lbl-deleteSceneDescription": "Are you sure you want to delete the '{{sceneName}}' Scene", "lbl-expandAll": "Expand All", "lbl-collapseAll": "Collapse All", "lbl-explode": "Explode Objects", "lbl-addEntity": "Add Entity", "lbl-createPrefab": "Create Prefab", "lbl-savePrefab": "Save Prefab", "issues": "Issues", "copy-paste": {"no-hierarchy-nodes": "No hierarchy nodes found"}}, "materialLibrary": {"lbl": "Material Library", "tab-materials": "Materials", "info": "Location of an asset's materials and where you can select and edit them."}, "dnd": {"nodes": "{{count}} Nodes Selected", "models": "{{count}} Models Selected", "images": "{{count}} Images Selected", "videos": "{{count}} Videos Selected", "audio": "{{count}} Audio Sources Selected"}, "dialog": {"title": "Editor", "lbl-confirm": "Ok", "lbl-cancel": "Cancel", "sure-confirm": "Are you sure?", "exportProject": {"title": "Export Project", "lbl-confirm": "Export Project", "lbl-combineMesh": "<PERSON><PERSON><PERSON>", "lbl-removeUnused": "Remove Unused Objects"}, "performance": {"title": "Performance Check", "lbl-confirm": "Publish Scene", "learnMore": "Learn More", "lbl-polycount": "Polygon Count", "lbl-material": "Materials", "lbl-texture": "Textures", "lbl-lights": "Lights", "info-polycount": "We recommend your Scene use no more than 50,000 triangles for mobile devices.", "info-material": "We recommend using no more than 25 unique materials in your Scene to reduce draw calls on mobile devices.", "info-texture": "We recommend your textures use no more than 256MB of video RAM for mobile devices. We also recommend against using textures larger than 2048 x 2048.", "info-lights": "While dynamic lights are not enabled on mobile devices, we recommend using no more than 3 lights in your Scene (excluding ambient and hemisphere lights) for your Scene to run on low end PCs.", "info-fileSize": "We recommend a final file size of no more than 16MB for low bandwidth connections. Reducing the file size will reduce the time it takes to download your Scene.", "txt-polycount": "{{count}} Triangles", "txt-material": "{{count}} Materials", "txt-ram": "~{{count}} Video RAM", "txt-texture": "{{count}} Large Textures", "txt-lights": "{{count}} Lights"}, "progress": {"title": "Loading...", "msg": "Loading...", "lbl-cancel": "Cancel"}, "publish": {"title": "Publish Scene", "lbl-confirm": "Save and Publish", "lbl-name": "Scene Name", "info-name": "Name must be between 4 and 64 characters and cannot contain underscores"}, "published": {"title": "Scene Published", "header": "Your Scene has been published.", "lbl-view": "View Your Scene"}, "saveScene": {"title": "Save", "lbl-thumbnail": "Generate thumbnail & envmap", "lbl-confirm": "Save Scene", "discard-quit": "Discard Changes and Quit", "lbl-save-quit": "Save and Quit", "info-confirm": "Are you sure you want to save the Scene?", "info-question": "Save changes to the current Scene?", "info-warning": "Discarded changes can not be recovered", "info-save-success": "Scene Saved", "unsavedChanges": {"title": "Quit to Dashboard"}}, "saveNewScene": {"title": "Save As", "lbl-name": "Scene Name", "info-name": "Name must be between 4 and 64 characters (with only letters, numbers, underscores, dashes and periods allowed)", "lbl-confirm": "Save Scene"}, "saveModel": {"title": "Save Changes", "text": "Are you sure you want to save changes to this file?"}, "savePrefab": {"title": "Save Prefab", "lbl-save-path": "Save Path", "warn-save-path": "Name must be between 4 and 64 characters (with only letters, numbers, underscores, dashes and periods allowed)"}, "revertModel": {"lbl-name": "<PERSON><PERSON>", "title": "Revert Changes", "text": "Are you sure you want to revert changes to this file?"}, "support": {"title": "Support", "header": "Need to report a problem?", "msg": "You can file a <1>GitHub Issue</1> or e-mail us for support at <3>{{mail}}</3>", "discord": "You can also find us on <1>Discord</1>"}, "delete": {"confirm-content": "Do you want to delete {{content}}?", "confirm-multiple": "Do you want to delete {{first}}, and {{count}} others?", "not-allowed-local-dev": "To prevent accidental loss of data, projects cannot be deleted from this menu in a local dev environment. Use the file system instead."}, "createScene": {"title": "New Scene", "create": "New Empty Scene"}, "addScene": {"continue": "Continue", "cancel": "Back To Editor", "title": "Select a way to get started", "optionButtons": {"defaultEditor": {"title": "Blank Scene", "description": "Use the advanced Worlds Editor to create your Space from the ground up. Recommended for experienced 3D creators.", "submitButtonText": "Continue in Editor"}}}, "file": "file", "folder": "folder"}, "sources": {"bingImage": {"name": "Bing Images", "search": "Search by <PERSON>"}, "bingVideo": {"name": "Bing Videos", "search": "Search by <PERSON>"}, "element": {"name": "Elements"}, "myAssets": {"name": "My Assets"}, "googlePoly": {"name": "Google Poly", "search": "Search by Google"}, "tenor": {"name": "Tenor GIFs", "search": "Search by <PERSON>or", "ph-search": "Search GIFs..."}, "sketchfab": {"name": "Sketchfab", "search": "Search by Sketchfab"}}, "asset": {"dropZone": {"title": "Upload Asset"}, "imageSourcePanel": {"ph-search": "Search images..."}, "videoSourcePanel": {"ph-search": "Search videos..."}, "assetSourcePanel": {"ph-search": "Search assets..."}, "modelSourcePanel": {"ph-search": "Search models..."}, "useUpload": {"multipleFileError": "Input does not accept multiple files.", "mineTypeError": "\"{{name}}\" does not match the following mime types or extensions: {{types}}", "progressMsg": "Uploading files {{uploaded}} of {{total}}: {{percentage}}%", "uploadError": "Upload Error", "uploadErrorMsg": "Error uploading file: {{message}}", "uploadErrorDefaultMsg": "An unknown error has occurred. Please try again or contact support for assistance."}}, "openLink": {"description": "Please click 'Open' to navigate to the link.", "lbl-open": "Open"}, "interactableModel": {"lbl-buy": "Buy", "lbl-learnMore": "Learn More"}, "corsProxyButton": {"tooltip": "Use CORS Proxy Server"}, "tabs": {"scene-assets": "Assets", "project-assets": "Project Assets"}, "generatingThumbnails": {"title": "Generating Thumbnails", "amountRemaining": "{{count}} remaining"}, "assetMetadata": {"name": "Name", "path": "Path", "type": "Type", "tags": "Tags"}, "unsupportedFile": {"title": "Unsupported File Type!", "okay": "Okay"}, "prefab": {"createTitle": "Create Prefab", "createLookdevTitle": "Create Lookdev Prefab", "defaultSaveFolder": "Default Save Folder", "name": "Name", "alreadyExistsTitle": "Overwrite Prefab", "alreadyExists": "Prefab with this name already exists. You will overwrite it.", "exportedSuccess": "Prefab exported successfully"}}