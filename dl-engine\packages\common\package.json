{"name": "@dl-engine/common", "version": "1.0.0", "publishConfig": {"access": "public"}, "main": "./index.ts", "types": "./index.d.ts", "description": "Digital Learning Engine 公共工具库和类型定义", "scripts": {"build": "tsc --build", "dev": "tsc --build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "repository": "https://github.com/dl-engine/dl-engine", "author": "Digital Learning Engine Team", "license": "MIT", "dependencies": {"@dl-engine/hyperflux": "1.0.0", "@dl-engine/ecs": "1.0.0", "react": "18.2.0", "react-i18next": "^13.5.0", "i18next": "^23.7.6", "three": "0.176.0", "uuid": "9.0.0"}, "devDependencies": {"@types/node": "22.11.0", "@types/uuid": "9.0.0", "rimraf": "4.4.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "exports": {".": "./index.ts", "./src/*": "./src/*"}, "files": ["dist", "src", "index.ts", "index.d.ts"]}