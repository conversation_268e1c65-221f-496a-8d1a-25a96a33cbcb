ARG REPO_URL
ARG REPO_NAME
ARG STAGE
ARG TAG
FROM ${REPO_URL}/${REPO_NAME}:${TAG} AS builder

# Create app directory
WORKDIR /app

COPY package.json .
COPY packages/client/package.json ./packages/client/
COPY packages/client-core/package.json ./packages/client-core/
COPY packages/editor/package.json ./packages/editor/
COPY packages/hyperflux/package.json ./packages/hyperflux/
COPY project-package-jsons ./
COPY packages/ui/package.json ./packages/ui/
COPY patches/ ./patches/

ARG NODE_ENV
RUN --mount=type=cache,target=/root/.npm npm install --loglevel notice --legacy-peer-deps --production

# copy then compile the code
COPY . .

ARG KUBERNETES
ARG STAGE
ARG STORAGE_PROVIDER
ARG STORAGE_CDN_DOMAIN
ARG STORAGE_CLOUDFRONT_DISTIRBUTION_ID
ARG STORAGE_STATIC_RESOURCE_BUCKET
ARG STORAGE_AWS_ACCESS_KEY_ID
ARG STORAGE_AWS_ACCESS_KEY_SECRET
ARG STORAGE_AWS_ROLE_ARN
ARG STORAGE_AWS_ENABLE_ACLS
ARG STORAGE_REGION
ARG STORAGE_S3_AVATAR_DIRECTORY
ARG SERVE_CLIENT_FROM_STORAGE_PROVIDER
ARG MYSQL_HOST
ARG MYSQL_USER
ARG MYSQL_PORT
ARG MYSQL_PASSWORD
ARG MYSQL_DATABASE
ARG POSTGRES_HOST
ARG POSTGRES_USER
ARG POSTGRES_PORT
ARG POSTGRES_PASSWORD
ARG POSTGRES_DATABASE
ARG VITE_AGENT_API_URL
ARG VITE_APP_HOST
ARG VITE_APP_PORT
ARG VITE_PWA_ENABLED
ARG VITE_SERVER_HOST
ARG VITE_SERVER_PORT
ARG VITE_FEATHERS_STORE_KEY
ARG VITE_FILE_SERVER
ARG VITE_MEDIATOR_SERVER
ARG VITE_LOGIN_WITH_WALLET
ARG VITE_8TH_WALL
ARG VITE_INSTANCESERVER_HOST
ARG VITE_INSTANCESERVER_PORT
ARG VITE_LOCAL_BUILD
ARG VITE_SOURCEMAPS
ARG VITE_READY_PLAYER_ME_URL
ARG VITE_DISABLE_LOG
ARG VITE_AVATURN_URL
ARG VITE_AVATURN_API
ARG AUTH_SECRET
ARG VITE_ZENDESK_ENABLED
ARG VITE_ZENDESK_KEY
ARG VITE_ZENDESK_AUTHENTICATION_ENABLED
ARG VITE_DNS_PROVIDER
ARG VITE_MIDDLEWARE_API_URL
ARG APP_HOST
ARG GCP_PROJECT
ARG GCP_EDGE_CACHE_SERVICE
ARG GCP_URL_MAP
ENV KUBERNETES=$KUBERNETES
ENV AUTH_SECRET=$AUTH_SECRET
ENV STORAGE_CDN_DOMAIN=$STORAGE_CDN_DOMAIN
ENV STORAGE_CLOUDFRONT_DISTRIBUTION_ID=$STORAGE_CLOUDFRONT_DISTRIBUTION_ID
ENV STORAGE_STATIC_RESOURCE_BUCKET=$STORAGE_STATIC_RESOURCE_BUCKET
ENV STORAGE_REGION=$STORAGE_REGION
ENV STORAGE_AWS_ROLE_ARN=$STORAGE_AWS_ROLE_ARN
ENV STORAGE_AWS_ENABLE_ACLS=$STORAGE_AWS_ENABLE_ACLS
ENV MYSQL_HOST=$MYSQL_HOST
ENV MYSQL_PORT=$MYSQL_PORT
ENV MYSQL_PASSWORD=$MYSQL_PASSWORD
ENV MYSQL_DATABASE=$MYSQL_DATABASE
ENV MYSQL_USER=$MYSQL_USER
ENV POSTGRES_HOST=$POSTGRES_HOST
ENV POSTGRES_PORT=$POSTGRES_PORT
ENV POSTGRES_PASSWORD=$POSTGRES_PASSWORD
ENV POSTGRES_DATABASE=$POSTGRES_DATABASE
ENV POSTGRES_USER=$POSTGRES_USER
ENV VITE_AGENT_API_URL=$VITE_AGENT_API_URL
ENV VITE_APP_HOST=$VITE_APP_HOST
ENV VITE_APP_PORT=$VITE_APP_PORT
ENV VITE_PWA_ENABLED=$VITE_PWA_ENABLED
ENV VITE_SERVER_HOST=$VITE_SERVER_HOST
ENV VITE_FEATHERS_STORE_KEY=$VITE_FEATHERS_STORE_KEY
ENV VITE_FILE_SERVER=$VITE_FILE_SERVER
ENV VITE_SERVER_PORT=$VITE_SERVER_PORT
ENV VITE_MEDIATOR_SERVER=$VITE_MEDIATOR_SERVER
ENV VITE_LOGIN_WITH_WALLET=$VITE_LOGIN_WITH_WALLET
ENV VITE_8TH_WALL=$VITE_8TH_WALL
ENV VITE_INSTANCESERVER_HOST=$VITE_INSTANCESERVER_HOST
ENV VITE_INSTANCESERVER_PORT=$VITE_INSTANCESERVER_PORT
ENV VITE_LOCAL_BUILD=$VITE_LOCAL_BUILD
ENV VITE_SOURCEMAPS=$VITE_SOURCEMAPS
ENV VITE_READY_PLAYER_ME_URL=$VITE_READY_PLAYER_ME_URL
ENV VITE_DISABLE_LOG=$VITE_DISABLE_LOG
ENV VITE_AVATURN_URL=$VITE_AVATURN_URL
ENV VITE_AVATURN_API=$VITE_AVATURN_API
ENV VITE_ZENDESK_ENABLED=$VITE_ZENDESK_ENABLED
ENV VITE_ZENDESK_KEY=$VITE_ZENDESK_KEY
ENV VITE_ZENDESK_AUTHENTICATION_ENABLED=$VITE_ZENDESK_AUTHENTICATION_ENABLED
ENV VITE_DNS_PROVIDER=$VITE_DNS_PROVIDER
ENV VITE_MIDDLEWARE_API_URL=$VITE_MIDDLEWARE_API_URL
ENV APP_HOST=$APP_HOST
ENV GCP_PROJECT=$GCP_PROJECT
ENV GCP_EDGE_CACHE_SERVICE=$GCP_EDGE_CACHE_SERVICE
ENV GCP_URL_MAP=$GCP_URL_MAP

RUN npm run build-client

RUN rm -r packages/client/public

ENV APP_ENV=production

FROM node:22-slim AS runner
WORKDIR /app

COPY --from=builder /app/packages/client ./packages/client
COPY --from=builder /app/scripts ./scripts

RUN --mount=type=cache,target=/root/.npm npm install app-root-path koa koa-body koa-qs koa-static
CMD ["scripts/start-server.sh"]
