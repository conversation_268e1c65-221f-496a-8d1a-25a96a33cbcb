/*
Digital Learning Engine
Copyright © 2025 Digital Learning Engine Team. All Rights Reserved.
*/

import { Quaternion, Vector3 } from 'three'

// 数学常量
export const EPSILON = 0.000001
export const DEG_TO_RAD = Math.PI / 180
export const RAD_TO_DEG = 180 / Math.PI
export const TWO_PI = Math.PI * 2
export const HALF_PI = Math.PI / 2

// 基础数学函数
export function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value))
}

export function lerp(a: number, b: number, t: number): number {
  return a + (b - a) * t
}

export function smoothstep(edge0: number, edge1: number, x: number): number {
  const t = clamp((x - edge0) / (edge1 - edge0), 0, 1)
  return t * t * (3 - 2 * t)
}

export function smootherstep(edge0: number, edge1: number, x: number): number {
  const t = clamp((x - edge0) / (edge1 - edge0), 0, 1)
  return t * t * t * (t * (t * 6 - 15) + 10)
}

// 角度转换
export function degreesToRadians(degrees: number): number {
  return degrees * DEG_TO_RAD
}

export function radiansToDegrees(radians: number): number {
  return radians * RAD_TO_DEG
}

// 浮点数比较
export function equalsTolerance(a: number, b: number, tolerance: number = EPSILON): boolean {
  return Math.abs(a - b) < tolerance
}

export function equalsAutoTolerance(a: number, b: number): boolean {
  return Math.abs(a - b) <= EPSILON * Math.max(1, Math.abs(a), Math.abs(b))
}

// 精度控制
export function toPrecision(value: number, precision: number): number {
  const factor = Math.pow(10, precision)
  return Math.round(value * factor) / factor
}

// 随机数生成
export function randomFloat(min: number, max: number): number {
  return Math.random() * (max - min) + min
}

export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function randomBool(): boolean {
  return Math.random() < 0.5
}

// 种子随机数生成器
export class SeededRandom {
  private seed: number

  constructor(seed: number) {
    this.seed = seed
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280
    return this.seed / 233280
  }

  nextFloat(min: number, max: number): number {
    return this.next() * (max - min) + min
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.nextFloat(min, max + 1))
  }

  nextBool(): boolean {
    return this.next() < 0.5
  }
}

// 缓动函数
export function easeInQuad(t: number): number {
  return t * t
}

export function easeOutQuad(t: number): number {
  return t * (2 - t)
}

export function easeInOutQuad(t: number): number {
  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
}

export function easeInCubic(t: number): number {
  return t * t * t
}

export function easeOutCubic(t: number): number {
  return 1 - Math.pow(1 - t, 3)
}

export function easeInOutCubic(t: number): number {
  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
}

export function easeInElastic(t: number): number {
  const c4 = (2 * Math.PI) / 3
  return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4)
}

export function easeOutElastic(t: number): number {
  const c4 = (2 * Math.PI) / 3
  return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
}

// Vector3 工具函数
export function vector3Distance(a: Vector3, b: Vector3): number {
  const dx = a.x - b.x
  const dy = a.y - b.y
  const dz = a.z - b.z
  return Math.sqrt(dx * dx + dy * dy + dz * dz)
}

export function vector3DistanceSquared(a: Vector3, b: Vector3): number {
  const dx = a.x - b.x
  const dy = a.y - b.y
  const dz = a.z - b.z
  return dx * dx + dy * dy + dz * dz
}

export function vector3Lerp(a: Vector3, b: Vector3, t: number): Vector3 {
  return new Vector3(
    lerp(a.x, b.x, t),
    lerp(a.y, b.y, t),
    lerp(a.z, b.z, t)
  )
}

export function vector3Equals(a: Vector3, b: Vector3, tolerance: number = EPSILON): boolean {
  return (
    equalsTolerance(a.x, b.x, tolerance) &&
    equalsTolerance(a.y, b.y, tolerance) &&
    equalsTolerance(a.z, b.z, tolerance)
  )
}

// Quaternion 工具函数
export function quaternionSlerp(a: Quaternion, b: Quaternion, t: number): Quaternion {
  const result = new Quaternion()
  result.slerpQuaternions(a, b, t)
  return result
}

export function quaternionEquals(a: Quaternion, b: Quaternion, tolerance: number = EPSILON): boolean {
  return (
    equalsTolerance(a.x, b.x, tolerance) &&
    equalsTolerance(a.y, b.y, tolerance) &&
    equalsTolerance(a.z, b.z, tolerance) &&
    equalsTolerance(a.w, b.w, tolerance)
  )
}

// 哈希函数
export function stringHash(str: string): number {
  let hash = 0
  if (str.length === 0) return hash
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return hash
}

// 数组工具函数
export function arraySum(arr: number[]): number {
  return arr.reduce((sum, val) => sum + val, 0)
}

export function arrayAverage(arr: number[]): number {
  return arr.length > 0 ? arraySum(arr) / arr.length : 0
}

export function arrayMin(arr: number[]): number {
  return Math.min(...arr)
}

export function arrayMax(arr: number[]): number {
  return Math.max(...arr)
}

// 性能测量
export function measureTime<T>(fn: () => T): { result: T; time: number } {
  const start = performance.now()
  const result = fn()
  const time = performance.now() - start
  return { result, time }
}

// 节流和防抖
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}
