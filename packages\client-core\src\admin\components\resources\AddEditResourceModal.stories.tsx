/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2023 
Infinite Reality Engine. All Rights Reserved.
*/

import { StaticResourceType } from '@ir-engine/common/src/schema.type.module'
import AddEditResourceModal from './AddEditResourceModal'

export default {
  title: 'Client/AddEditResourceModal',
  component: AddEditResourceModal,
  parameters: {
    componentSubtitle: 'AddEditResourceModal',
    design: {
      type: 'figma',
      url: ''
    }
  }
}

export const Default = {
  args: {
    selectedResource: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      key: 'resource-key',
      mimeType: 'image/png',
      userId: '123e4567-e89b-12d3-a456-426614174001',
      hash: 'abcdef123456',
      type: 'thumbnail',
      project: 'project-name',
      tags: ['tag1', 'tag2'],
      dependencies: ['dep1', 'dep2'],
      attribution: 'Author Name',
      licensing: 'MIT',
      description: 'A sample static resource',
      name: 'Sample Resource',
      url: 'https://example.com/resource.png',
      stats: { size: 1024 },
      thumbnailKey: 'thumb-key',
      thumbnailURL: 'https://example.com/thumb.png',
      thumbnailMode: 'automatic',
      updatedBy: '123e4567-e89b-12d3-a456-426614174002',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as unknown as StaticResourceType
  }
}
