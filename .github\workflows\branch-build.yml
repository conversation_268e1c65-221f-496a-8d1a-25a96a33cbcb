name: branch-build
on:
  pull_request:
    branches: [dev, int, stg, release*]
  push:
    branches: [dev, int, stg, release*]
  merge_group:
    types: [checks_requested]
jobs:
  secrets-gate:
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.check-secrets.outputs.ok }}
    steps:
      - name: check for secrets needed to run workflows
        id: check-secrets
        run: |
          if [ ${{ secrets.BRANCH_BUILD_ENABLED }} == 'true' ]; then
            echo "ok=enabled" >> $GITHUB_OUTPUT
          fi
  install-modules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - name: Cache node-modules
        if: steps.cache.outputs.cache-hit != 'true'
        uses: actions/cache/save@v4
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
  check-lint:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: npm run lint
      - run: npm run check-eslint
        timeout-minutes: 20

  check-errors:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: npm run dev-docker
      - run: npm run dev-reinit
      - run: npx lerna run --scope '@ir-engine/*' check-errors
        timeout-minutes: 20

  build-client:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: npm run dev-docker
      - run: npm run dev-reinit
      - run: rm -rf packages/instanceserver
      - run: rm -rf packages/server
      - run: rm -rf packages/server-core
      - run: rm -rf packages/taskserver
      - run: npm run build-client

  test_hyperflux:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: mkdir -p packages_moved/hyperflux/
      - run: mv packages/hyperflux packages_moved/
      - run: rm -rf packages/*
      - run: mv packages_moved/hyperflux packages/
      - run: cd packages/hyperflux && npm run test

  test_ecs:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: mkdir -p packages_moved/hyperflux/
      - run: mkdir -p packages_moved/ecs/
      - run: mv packages/hyperflux packages_moved/
      - run: mv packages/ecs packages_moved/
      - run: rm -rf packages/*
      - run: mv packages_moved/hyperflux packages/
      - run: mv packages_moved/ecs packages/
      - run: cd packages/ecs && npm run test

  test_spatial:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: mkdir -p packages_moved/hyperflux/
      - run: mkdir -p packages_moved/ecs/
      - run: mkdir -p packages_moved/xrui/
      - run: mkdir -p packages_moved/spatial/
      - run: mv packages/hyperflux packages_moved/
      - run: mv packages/ecs packages_moved/
      - run: mv packages/xrui packages_moved/
      - run: mv packages/spatial packages_moved/
      - run: rm -rf packages/*
      - run: mv packages_moved/hyperflux packages/
      - run: mv packages_moved/ecs packages/
      - run: mv packages_moved/xrui packages/
      - run: mv packages_moved/spatial packages/
      - run: cd packages/spatial && npm run test

  test_engine:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: mkdir -p packages_moved/hyperflux/
      - run: mkdir -p packages_moved/ecs/
      - run: mkdir -p packages_moved/xrui/
      - run: mkdir -p packages_moved/spatial/
      - run: mkdir -p packages_moved/visual-script/
      - run: mkdir -p packages_moved/engine/
      - run: mkdir -p packages_moved/projects/
      - run: mv packages/hyperflux packages_moved/
      - run: mv packages/ecs packages_moved/
      - run: mv packages/xrui packages_moved/
      - run: mv packages/spatial packages_moved/
      - run: mv packages/visual-script packages_moved/
      - run: mv packages/engine packages_moved/
      - run: mv packages/projects packages_moved/
      - run: rm -rf packages/*
      - run: mv packages_moved/hyperflux packages/
      - run: mv packages_moved/ecs packages/
      - run: mv packages_moved/xrui packages/
      - run: mv packages_moved/spatial packages/
      - run: mv packages_moved/visual-script packages/
      - run: mv packages_moved/engine packages/
      - run: mv packages_moved/projects packages/
      - run: cd packages/engine && npm run test

  test_visual_script:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: mkdir -p packages_moved/hyperflux/
      - run: mkdir -p packages_moved/ecs/
      - run: mkdir -p packages_moved/xrui/
      - run: mkdir -p packages_moved/spatial/
      - run: mkdir -p packages_moved/visual-script/
      - run: mkdir -p packages_moved/engine/
      - run: mkdir -p packages_moved/projects/
      - run: mv packages/hyperflux packages_moved/
      - run: mv packages/ecs packages_moved/
      - run: mv packages/xrui packages_moved/
      - run: mv packages/spatial packages_moved/
      - run: mv packages/visual-script packages_moved/
      - run: mv packages/engine packages_moved/
      - run: mv packages/projects packages_moved/
      - run: rm -rf packages/*
      - run: mv packages_moved/hyperflux packages/
      - run: mv packages_moved/ecs packages/
      - run: mv packages_moved/xrui packages/
      - run: mv packages_moved/spatial packages/
      - run: mv packages_moved/visual-script packages/
      - run: mv packages_moved/engine packages/
      - run: mv packages_moved/projects packages/
      - run: cd packages/visual-script && npm run test

  test_client:
    needs: install-modules
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        package:
          - client
          - client-core
          - common
          - editor
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: rm -rf packages/instanceserver
      - run: rm -rf packages/server
      - run: rm -rf packages/server-core
      - run: rm -rf packages/taskserver
      - run: cd packages/${{ matrix.package }} && npm run test

  test_server:
    needs: install-modules
    runs-on: ubuntu-latest
    strategy:
      matrix:
        package:
          - instanceserver
          - matchmaking
          - server
          - server-core
          - taskserver
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - run: cp .env.local.default .env.local
      - run: sed -i 's/localhost/127.0.0.1/g' .env.local
      - run: rm -rf packages/client
      - run: rm -rf packages/client-core
      - run: rm -rf packages/editor
      - run: rm -rf packages/ui
      - name: Start Docker
        run: npm run dev-docker
        timeout-minutes: 20
      - run: npm run dev-reinit
      - run: git config --global user.email "<EMAIL>" && git config --global user.name "Your Name"
      - run: cd packages/${{ matrix.package }} && npm run test
        timeout-minutes: 20

  npm_builds:
    needs: install-modules
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package.json'
      - name: Restore
        uses: actions/cache/restore@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./packages/*/node_modules
          key: ${{ runner.os }}-build-${{ hashFiles('./package.json', './packages/*/package.json') }}
      - name: Install Dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm i
      - name: Checkout NPM Build Test
        uses: actions/checkout@v4
        with:
          repository: ir-engine/ir-engine-npm-tests
          path: ir-engine-npm-tests
      - name: Run npm build test
        run: |
          cd ./ir-engine-npm-tests
          npm i
          npm run setup
          npm run test
