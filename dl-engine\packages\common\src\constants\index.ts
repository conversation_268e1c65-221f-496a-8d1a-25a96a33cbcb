/*
Digital Learning Engine
Copyright © 2025 Digital Learning Engine Team. All Rights Reserved.
*/

// 应用常量
export const APP_NAME = 'Digital Learning Engine'
export const APP_VERSION = '1.0.0'
export const APP_DESCRIPTION = '专注于数字化学习和教育场景的3D/XR应用开发平台'

// 网络常量
export const DEFAULT_PORT = {
  CLIENT: 3000,
  API_SERVER: 3030,
  INSTANCE_SERVER: 3031,
  TASK_SERVER: 3032,
  FILE_SERVER: 8642
} as const

// 数据库常量
export const DATABASE = {
  MYSQL_PORT: 3306,
  REDIS_PORT: 6379,
  POSTGRES_PORT: 5432,
  MINIO_PORT: 9000,
  OLLAMA_PORT: 11434
} as const

// 用户常量
export const USER = {
  USERNAME_MIN_LENGTH: 2,
  USERNAME_MAX_LENGTH: 50,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15,
  VERIFICATION_CODE_LENGTH: 6,
  VERIFICATION_CODE_EXPIRY: 300000, // 5分钟
  SESSION_EXPIRY: 86400000 // 24小时
} as const

// 文件上传常量
export const FILE_UPLOAD = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_MODEL_TYPES: ['model/gltf-binary', 'model/gltf+json'],
  ALLOWED_AUDIO_TYPES: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg']
} as const

// 3D/XR 常量
export const XR = {
  DEFAULT_FOV: 60,
  DEFAULT_NEAR: 0.1,
  DEFAULT_FAR: 1000,
  MAX_RENDER_DISTANCE: 10000,
  PHYSICS_TIMESTEP: 1 / 60,
  NETWORK_TICK_RATE: 20
} as const

// 教育相关常量
export const EDUCATION = {
  MAX_STUDENTS_PER_ROOM: 50,
  MAX_CONCURRENT_ROOMS: 100,
  DEFAULT_LESSON_DURATION: 3600000, // 1小时
  MAX_LESSON_DURATION: 14400000, // 4小时
  COLLABORATION_TIMEOUT: 30000 // 30秒
} as const

// 错误代码
export const ERROR_CODES = {
  // 认证错误
  AUTH_INVALID_PHONE: 'AUTH_INVALID_PHONE',
  AUTH_INVALID_CODE: 'AUTH_INVALID_CODE',
  AUTH_CODE_EXPIRED: 'AUTH_CODE_EXPIRED',
  AUTH_TOO_MANY_ATTEMPTS: 'AUTH_TOO_MANY_ATTEMPTS',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  USER_DEACTIVATED: 'USER_DEACTIVATED',
  
  // 文件错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  FILE_TYPE_NOT_ALLOWED: 'FILE_TYPE_NOT_ALLOWED',
  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
  
  // 网络错误
  NETWORK_CONNECTION_FAILED: 'NETWORK_CONNECTION_FAILED',
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_UNAUTHORIZED: 'NETWORK_UNAUTHORIZED',
  
  // 系统错误
  SYSTEM_MAINTENANCE: 'SYSTEM_MAINTENANCE',
  SYSTEM_OVERLOAD: 'SYSTEM_OVERLOAD',
  SYSTEM_INTERNAL_ERROR: 'SYSTEM_INTERNAL_ERROR'
} as const

// 日志级别
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  FATAL: 'fatal'
} as const

// 缓存键前缀
export const CACHE_KEYS = {
  USER_SESSION: 'user:session:',
  SMS_CODE: 'sms:code:',
  ROOM_STATE: 'room:state:',
  ASSET_CACHE: 'asset:cache:',
  USER_PROFILE: 'user:profile:',
  ROOM_PARTICIPANTS: 'room:participants:'
} as const

// WebRTC 配置
export const WEBRTC = {
  ICE_SERVERS: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ],
  MEDIA_CONSTRAINTS: {
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    },
    video: {
      width: { ideal: 1280 },
      height: { ideal: 720 },
      frameRate: { ideal: 30 }
    }
  }
} as const
