/*
Digital Learning Engine
Copyright © 2025 Digital Learning Engine Team. All Rights Reserved.
*/

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

import zhTranslation from './locales/zh.json'
import enTranslation from './locales/en.json'

// 国际化资源
const resources = {
  zh: {
    translation: zhTranslation
  },
  en: {
    translation: enTranslation
  }
}

// 初始化 i18n
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh', // 默认中文
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false // React 已经默认转义
    },
    
    // 调试模式（仅在开发环境）
    debug: process.env.NODE_ENV === 'development',
    
    // 命名空间
    defaultNS: 'translation',
    
    // 检测选项
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage']
    },
    
    // 后端选项
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json'
    }
  })

export default i18n

// 导出类型
export interface I18nResource {
  [key: string]: string | I18nResource
}

export interface I18nOptions {
  lng?: string
  fallbackLng?: string
  resources: Record<string, { translation: I18nResource }>
}

// 导出常用的翻译函数
export const t = i18n.t.bind(i18n)
export const changeLanguage = i18n.changeLanguage.bind(i18n)
export const getCurrentLanguage = () => i18n.language

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', nativeName: '中文' },
  { code: 'en', name: 'English', nativeName: 'English' }
] as const

export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number]['code']

// 语言检测工具
export function detectLanguage(): SupportedLanguage {
  // 优先从 localStorage 读取
  const stored = localStorage.getItem('i18nextLng')
  if (stored && SUPPORTED_LANGUAGES.some(lang => lang.code === stored)) {
    return stored as SupportedLanguage
  }
  
  // 从浏览器语言检测
  const browserLang = navigator.language.split('-')[0]
  if (SUPPORTED_LANGUAGES.some(lang => lang.code === browserLang)) {
    return browserLang as SupportedLanguage
  }
  
  // 默认返回中文
  return 'zh'
}

// 设置语言
export function setLanguage(language: SupportedLanguage): void {
  changeLanguage(language)
  localStorage.setItem('i18nextLng', language)
}

// React Hook for language switching
export function useLanguage() {
  const currentLanguage = getCurrentLanguage() as SupportedLanguage
  
  return {
    currentLanguage,
    supportedLanguages: SUPPORTED_LANGUAGES,
    setLanguage,
    t
  }
}
