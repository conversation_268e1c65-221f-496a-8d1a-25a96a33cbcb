name: Deploy Napster Engine

on:
  push:
    branches: [dev, int, stg]
    paths-ignore:
      - '**/*.md'
      - '.*ignore'
  workflow_dispatch:
    inputs:
      environment:
        description: Evironment to Deploy
        required: true
        type: choice
        options: 
        - dev
        - int
        - stg

env:
  TARGET_BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

jobs:
  remote-dispatch-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository with limited history
        uses: actions/checkout@v3
        with:
          fetch-depth: 2

      - name: Generate GitHub App Token
        id: app-token
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ secrets.IR_REPO_AUTH_APP_ID }}
          private-key: ${{ secrets.IR_REPO_AUTH_PRIVATE_KEY }}
          owner: theinfinitereality

      - name: Send Remote Dispatch to Deploy GCP IR-Engine
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
          repository: ${{ secrets.IR_ENGINE_OPS_GCP_REPO }}
          event-type: deploy-ir-engine
          client-payload: |
            {
              "environment": "dev"
            }

      - name: Detect changes in packages/ui
        id: check_changes
        run: |
          echo "Checking for changes in packages/ui directory..."
          if git diff --name-only HEAD~1 HEAD | grep -q 'packages/ui/'; then
            echo "UI package changes detected."
            echo "ui_package_changed=true" >> $GITHUB_ENV
          else
            echo "No changes detected in UI package."
            echo "ui_package_changed=false" >> $GITHUB_ENV
          fi
      - name: Deploy Storybook
        if: env.TARGET_BRANCH_NAME == 'dev' && env.ui_package_changed == 'true'
        run: |
          echo "Deploying storybook"
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ steps.app-token.outputs.token }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/ir-engine/storybook-docs/dispatches \
            -d '{"event_type":"push"}'
