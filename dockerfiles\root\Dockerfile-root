FROM node:22-slim AS runner

RUN apt update
# Create app directory
WORKDIR /app

RUN apt -y install build-essential meson python3-testresources python3-venv python3-pip git

COPY patches/ ./patches/
COPY package.json .
COPY packages/common/package.json ./packages/common/
COPY packages/ecs/package.json ./packages/ecs/
COPY packages/engine/package.json ./packages/engine/
COPY packages/hyperflux/package.json ./packages/hyperflux/
COPY packages/matchmaking/package.json ./packages/matchmaking/
COPY packages/projects/package.json ./packages/projects/
COPY packages/server-core/package.json ./packages/server-core/
COPY packages/spatial/package.json ./packages/spatial/
COPY packages/visual-script/package.json ./packages/visual-script/
COPY packages/xrui/package.json ./packages/xrui/

ARG NODE_ENV
RUN --mount=type=cache,target=/root/.npm npm install --loglevel notice --legacy-peer-deps