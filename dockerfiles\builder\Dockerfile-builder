ARG REPO_URL
ARG REPO_NAME
ARG STAGE
FROM $REPO_URL/$REPO_NAME-root:latest_${STAGE} AS builder

COPY packages/server/package.json ./packages/server/
COPY packages/server-core/package.json ./packages/server-core/
COPY packages/spatial/package.json ./packages/spatial/
COPY packages/visual-script/package.json ./packages/visual-script/
COPY packages/xrui/package.json ./packages/xrui/

RUN --mount=type=cache,target=/root/.npm npm install --loglevel notice --legacy-peer-deps

COPY . .

CMD ["scripts/strip-engine.sh"]

# copy then compile the code

ENV APP_ENV=development 

FROM node:22-slim AS runner
WORKDIR /app

ARG CACHEBUST

# This commands runs the setup_docker.sh and setup_helm.sh scripts to install Docker, helm, and relevent installations
COPY scripts/setup_docker.sh .
COPY scripts/setup_helm.sh .
RUN chmod +x setup_docker.sh && ./setup_docker.sh true

# Install debugging tools (vim, default-mysql-client and postgresql-client) and gcloud prerequisites
RUN apt-get update && \
  apt-get install -y vim default-mysql-client postgresql-client apt-transport-https ca-certificates gnupg curl && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

# Install gcloud for Debian: https://cloud.google.com/sdk/docs/install
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" \
  | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list \
  && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg \
  | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg \
  && apt-get update -y \
  && apt-get install google-cloud-cli -y

RUN echo "$CACHEBUST"
RUN chmod +x setup_helm.sh && ./setup_helm.sh

COPY --from=builder /app ./

CMD ["scripts/run-builder.sh"]